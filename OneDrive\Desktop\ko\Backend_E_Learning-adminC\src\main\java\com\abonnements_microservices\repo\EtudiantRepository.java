package com.abonnements_microservices.repo;

import com.abonnements_microservices.model.Etudiant;
import com.abonnements_microservices.model.Matiere;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface EtudiantRepository extends JpaRepository<Etudiant, Long> {
    Optional<Etudiant> findByUsername(String username);
    Optional<Etudiant> findByEmail(String email);
    List<Etudiant> findByStatus(String status);
    List<Etudiant> findByFirstNameContainingIgnoreCaseOrLastNameContainingIgnoreCase(String firstName, String lastName);
        @Query("SELECT DISTINCT m FROM Etudiant e " +
               "JOIN e.abonnements a " +
               "JOIN a.matieres m " +
               "WHERE e.id = :etudiantId")
        List<Matiere> findMatieresByEtudiantIdWithAbonnements(@Param("etudiantId") Long etudiantId);
    
}