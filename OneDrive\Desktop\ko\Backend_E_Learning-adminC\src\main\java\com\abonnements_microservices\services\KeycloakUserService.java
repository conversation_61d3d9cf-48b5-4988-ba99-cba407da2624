package com.abonnements_microservices.services;

import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.ws.rs.core.Response;
import java.util.Collections;
import java.util.List;

@Service
public class KeycloakUserService {

    @Value("${keycloak.realm}")
    private String realm;

    private final Keycloak keycloak;

    public KeycloakUserService(Keycloak keycloak) {
        this.keycloak = keycloak;
    }

    public void createUser(String username, String firstName, String lastName, String email, String password, String role) {
        try {
            UsersResource usersResource = keycloak.realm(realm).users();

            // Check if user exists
            List<UserRepresentation> existingUsers = usersResource.search(username);
            if (!existingUsers.isEmpty()) {
                throw new RuntimeException("Username already exists in Keycloak");
            }

            // Create user representation
            UserRepresentation user = new UserRepresentation();
            user.setUsername(username);
            user.setFirstName(firstName);
            user.setLastName(lastName);
            user.setEmail(email);
            user.setEnabled(true);
            user.setEmailVerified(true);

            // Create user in Keycloak
            Response response = usersResource.create(user);
            
            if (response.getStatus() == 409) {
                throw new RuntimeException("User already exists (Conflict)");
            } else if (response.getStatus() != 201) {
                throw new RuntimeException("Failed to create user in Keycloak. Status: " + response.getStatus());
            }

            // Get user id from response
            String userId = response.getLocation().getPath().replaceAll(".*/([^/]+)$", "$1");

            // Set password
            CredentialRepresentation credential = new CredentialRepresentation();
            credential.setType(CredentialRepresentation.PASSWORD);
            credential.setValue(password);
            credential.setTemporary(false);
            usersResource.get(userId).resetPassword(credential);

            // Assign role
            try {
                RoleRepresentation roleRepresentation = keycloak.realm(realm).roles().get(role).toRepresentation();
                if (roleRepresentation == null) {
                    throw new RuntimeException("Role " + role + " not found in realm");
                }
                usersResource.get(userId).roles().realmLevel().add(Collections.singletonList(roleRepresentation));
            } catch (Exception e) {
                // If role assignment fails, delete the created user to maintain consistency
                usersResource.get(userId).remove();
                throw new RuntimeException("Failed to assign role: " + e.getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to create user in Keycloak: " + e.getMessage());
        }
    }

    /**
     * Update a user's role in Keycloak
     * Used when approving a pending student registration
     */
    public void updateUserRole(String username, String newRole) {
        try {
            UsersResource usersResource = keycloak.realm(realm).users();
            
            // Find the user by username
            List<UserRepresentation> users = usersResource.search(username);
            if (users.isEmpty()) {
                throw new RuntimeException("User not found: " + username);
            }
            
            String userId = users.get(0).getId();
            
            // Get current roles
            List<RoleRepresentation> currentRoles = usersResource.get(userId).roles().realmLevel().listAll();
            
            // Remove current roles
            if (!currentRoles.isEmpty()) {
                usersResource.get(userId).roles().realmLevel().remove(currentRoles);
            }
            
            // Assign new role
            RoleRepresentation roleRepresentation = keycloak.realm(realm).roles().get(newRole).toRepresentation();
            if (roleRepresentation == null) {
                throw new RuntimeException("Role " + newRole + " not found in realm");
            }
            
            usersResource.get(userId).roles().realmLevel().add(Collections.singletonList(roleRepresentation));
            System.out.println("Updated role for user " + username + " to " + newRole);
        } catch (Exception e) {
            System.err.println("Error updating user role: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to update user role: " + e.getMessage());
        }
    }
}