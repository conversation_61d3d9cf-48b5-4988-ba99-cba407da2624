version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: elearning-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: elearning_db
      MYSQL_USER: elearning_user
      MY<PERSON>QL_PASSWORD: elearning_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - elearning-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Keycloak Authentication Server
  keycloak:
    image: quay.io/keycloak/keycloak:22.0.4
    container_name: elearning-keycloak
    restart: unless-stopped
    environment:
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: "0000"
      KC_DB: mysql
      KC_DB_URL: ***********************************
      KC_DB_USERNAME: keycloak_user
      KC_DB_PASSWORD: keycloak_password
      KC_HOSTNAME: localhost
      KC_HOSTNAME_PORT: 8080
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false
      KC_HTTP_ENABLED: true
      KC_HEALTH_ENABLED: true
      KC_METRICS_ENABLED: true
    ports:
      - "8080:8080"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - elearning-network
    command: start-dev
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health/ready || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Backend Spring Boot Application
  backend:
    build:
      context: ./Backend_E_Learning-adminC
      dockerfile: Dockerfile
    container_name: elearning-backend
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************
      SPRING_DATASOURCE_USERNAME: elearning_user
      SPRING_DATASOURCE_PASSWORD: elearning_password
      KEYCLOAK_AUTH_SERVER_URL: http://keycloak:8080
      KEYCLOAK_REALM: e-learning-realm
      KEYCLOAK_RESOURCE: e-learning
      KEYCLOAK_CREDENTIALS_SECRET: aRjZ2PB2jnt6NVBypwy4dooa9Fx4lBku
      KEYCLOAK_ADMIN_USERNAME: admin
      KEYCLOAK_ADMIN_PASSWORD: "0000"
    ports:
      - "8081:8081"
    depends_on:
      mysql:
        condition: service_healthy
      keycloak:
        condition: service_healthy
    networks:
      - elearning-network
    volumes:
      - backend_logs:/app/logs

  # Frontend User Application
  frontend-user:
    build:
      context: ./Frontend_User_E_Learning-adminC
      dockerfile: Dockerfile
    container_name: elearning-frontend-user
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - elearning-network
    environment:
      REACT_APP_API_URL: http://localhost:8081
      REACT_APP_KEYCLOAK_URL: http://localhost:8080
      REACT_APP_KEYCLOAK_REALM: e-learning-realm
      REACT_APP_KEYCLOAK_CLIENT_ID: e-learning

  # Frontend Vitrine Application
  frontend-vitrine:
    build:
      context: ./vitrine-vitrine3
      dockerfile: Dockerfile
    container_name: elearning-frontend-vitrine
    restart: unless-stopped
    ports:
      - "3001:80"
    depends_on:
      - backend
    networks:
      - elearning-network
    environment:
      REACT_APP_API_URL: http://localhost:8081
      REACT_APP_KEYCLOAK_URL: http://localhost:8080
      REACT_APP_KEYCLOAK_REALM: e-learning-realm
      REACT_APP_KEYCLOAK_CLIENT_ID: e-learning

networks:
  elearning-network:
    driver: bridge

volumes:
  mysql_data:
  backend_logs:
