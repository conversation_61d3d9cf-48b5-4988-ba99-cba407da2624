package com.abonnements_microservices.services;

import com.abonnements_microservices.model.ChatHistory;
import com.abonnements_microservices.repository.ChatHistoryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Service
@Slf4j
public class ChatService {

    @Autowired
    private ChatHistoryRepository chatHistoryRepository;

    @Value("${openai.api.key:********************************************************************************************************************************************************************}")
    private String openAiApiKey;

    // Flag to enable/disable fallback mode - set to true to always use fallback responses
    private boolean useFallbackMode = false;

    private final RestTemplate restTemplate = new RestTemplate();

    // Map of predefined responses for common questions
    private final Map<String, String> commonResponses = new HashMap<>();

    // Initialize common responses
    {
        // Greetings
        commonResponses.put("bonjour", "Bonjour ! Comment puis-je vous aider aujourd'hui ?");
        commonResponses.put("salut", "Salut ! Comment puis-je vous aider aujourd'hui ?");
        commonResponses.put("hello", "Bonjour ! Comment puis-je vous aider aujourd'hui ?");
        commonResponses.put("bonsoir", "Bonsoir ! Comment puis-je vous aider aujourd'hui ?");

        // Thanks
        commonResponses.put("merci", "De rien ! N'hésitez pas si vous avez d'autres questions.");
        commonResponses.put("thanks", "De rien ! N'hésitez pas si vous avez d'autres questions.");

        // Programming
        commonResponses.put("programmation", "Pour améliorer vos compétences en programmation, je vous recommande de :\n\n" +
                "1. Pratiquer régulièrement avec des projets personnels\n" +
                "2. Suivre des cours en ligne sur des plateformes comme Coursera, Udemy ou freeCodeCamp\n" +
                "3. Participer à des défis de codage sur LeetCode ou HackerRank\n" +
                "4. Contribuer à des projets open source\n" +
                "5. Rejoindre des communautés de développeurs pour échanger et apprendre");

        // AI concepts
        commonResponses.put("intelligence artificielle", "L'intelligence artificielle (IA) est un domaine de l'informatique qui vise à créer des machines capables de simuler l'intelligence humaine. " +
                "Elle englobe plusieurs sous-domaines comme :\n\n" +
                "- L'apprentissage automatique (machine learning)\n" +
                "- Les réseaux de neurones et l'apprentissage profond (deep learning)\n" +
                "- Le traitement du langage naturel (NLP)\n" +
                "- La vision par ordinateur\n\n" +
                "Les applications de l'IA sont nombreuses : assistants virtuels, recommandations personnalisées, voitures autonomes, diagnostic médical, etc.");

        // Study tips
        commonResponses.put("étudier efficacement", "Pour étudier efficacement, voici quelques conseils :\n\n" +
                "1. Planifiez vos sessions d'étude avec la technique Pomodoro (25 min de travail, 5 min de pause)\n" +
                "2. Créez un environnement d'étude sans distractions\n" +
                "3. Utilisez des techniques de mémorisation active comme les flashcards\n" +
                "4. Enseignez ce que vous apprenez à quelqu'un d'autre\n" +
                "5. Prenez des notes à la main plutôt qu'à l'ordinateur\n" +
                "6. Faites des pauses régulières et dormez suffisamment");

        // Machine learning
        commonResponses.put("machine learning", "Le machine learning (apprentissage automatique) est une branche de l'intelligence artificielle qui permet aux ordinateurs d'apprendre à partir de données sans être explicitement programmés. " +
                "Il fonctionne en trois étapes principales :\n\n" +
                "1. Collecte et préparation des données\n" +
                "2. Entraînement d'un modèle sur ces données\n" +
                "3. Utilisation du modèle pour faire des prédictions sur de nouvelles données\n\n" +
                "Il existe plusieurs types d'apprentissage : supervisé, non supervisé, par renforcement, etc.");

        // Programming languages
        commonResponses.put("langages de programmation", "Les langages de programmation les plus demandés en 2025 sont :\n\n" +
                "1. Python - Pour l'IA, la data science et le développement web\n" +
                "2. JavaScript - Pour le développement web frontend et backend (Node.js)\n" +
                "3. Java - Pour les applications d'entreprise et Android\n" +
                "4. C# - Pour le développement .NET et les jeux (Unity)\n" +
                "5. Rust - Pour les systèmes performants et sécurisés\n" +
                "6. Go - Pour les microservices et applications cloud\n" +
                "7. TypeScript - Pour le développement web à grande échelle\n" +
                "8. Kotlin - Pour le développement Android moderne");
    }

    /**
     * Get a fallback response based on the user's message
     *
     * @param message The user's message
     * @return A predefined response or a default message
     */
    private String getFallbackResponse(String message) {
        // Convert message to lowercase for case-insensitive matching
        String lowerMessage = message.toLowerCase();

        // Check for exact matches in common responses
        for (Map.Entry<String, String> entry : commonResponses.entrySet()) {
            if (lowerMessage.contains(entry.getKey())) {
                return entry.getValue();
            }
        }

        // Check for specific question patterns
        if (lowerMessage.contains("comment") && lowerMessage.contains("programmation")) {
            return commonResponses.get("programmation");
        }

        if (lowerMessage.contains("intelligence artificielle") ||
            (lowerMessage.contains("ia") && !lowerMessage.contains("iai"))) {
            return commonResponses.get("intelligence artificielle");
        }

        if (lowerMessage.contains("étudier") || lowerMessage.contains("étude") ||
            lowerMessage.contains("apprendre") || lowerMessage.contains("mémoriser")) {
            return commonResponses.get("étudier efficacement");
        }

        if (lowerMessage.contains("machine learning") || lowerMessage.contains("apprentissage automatique")) {
            return commonResponses.get("machine learning");
        }

        if (lowerMessage.contains("langage") || lowerMessage.contains("programmation") ||
            lowerMessage.contains("coder") || lowerMessage.contains("développement")) {
            return commonResponses.get("langages de programmation");
        }

        // Default response if no match is found
        return "Je suis désolé, je ne peux pas répondre à cette question spécifique pour le moment. " +
               "Je suis actuellement en mode limité en raison de restrictions d'API. " +
               "Je peux vous aider avec des questions sur la programmation, l'intelligence artificielle, " +
               "les techniques d'étude, le machine learning, ou les langages de programmation populaires. " +
               "N'hésitez pas à me poser une question sur ces sujets !";
    }

    /**
     * Send a message to the chat model and save the conversation to history
     * This method will try to use the OpenAI API first, and if that fails, it will use a fallback response
     *
     * @param message The user's message
     * @param userId The ID of the user
     * @return The AI's response or a fallback response
     */
    public Map<String, Object> sendMessage(String message, Long userId) {
        try {
            // First, check if we should use the fallback mode due to API quota issues
            String fallbackResponse = null;

            // If fallback mode is enabled, skip the API call and use fallback responses directly
            if (useFallbackMode) {
                fallbackResponse = getFallbackResponse(message);
            } else {
                // Try to use OpenAI API
                String url = "https://api.openai.com/v1/chat/completions";

                // Set up headers with API key
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.setBearerAuth(openAiApiKey);

                // Prepare the request body
                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("model", "gpt-3.5-turbo");

                List<Map<String, String>> messages = new ArrayList<>();
                messages.add(Map.of(
                    "role", "system",
                    "content", "You are a helpful assistant for an educational platform. Provide concise, accurate, and helpful responses."
                ));
                messages.add(Map.of(
                    "role", "user",
                    "content", message
                ));

                requestBody.put("messages", messages);
                requestBody.put("max_tokens", 500);
                requestBody.put("temperature", 0.7);

                // Create the HTTP entity with the request body
                HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

                try {
                    // Make the request to OpenAI API
                    ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                        url,
                        HttpMethod.POST,
                        requestEntity,
                        new ParameterizedTypeReference<Map<String, Object>>() {}
                    );

                    // Process the response
                    if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                        Map<String, Object> responseBody = response.getBody();
                        List<Map<String, Object>> choices = (List<Map<String, Object>>) responseBody.get("choices");

                        if (choices != null && !choices.isEmpty()) {
                            Map<String, Object> choice = choices.get(0);
                            Map<String, String> messageResponse = (Map<String, String>) choice.get("message");
                            String aiResponse = messageResponse.get("content");

                            // Save to history
                            saveToHistory(message, aiResponse, userId);

                            // Return the response
                            return Map.of(
                                "response", aiResponse,
                                "success", true
                            );
                        }
                    }

                    // If we get here, the API returned an unexpected response
                    log.warn("OpenAI API returned unexpected response: {}", response);
                    fallbackResponse = getFallbackResponse(message);
                } catch (Exception apiError) {
                    // API call failed, use fallback
                    log.warn("OpenAI API call failed, using fallback response", apiError);
                    fallbackResponse = getFallbackResponse(message);

                    // Check for specific error types for logging
                    String errorMessage = apiError.getMessage();
                    if (errorMessage != null) {
                        if (errorMessage.contains("insufficient_quota") ||
                            errorMessage.contains("exceeded your current quota") ||
                            errorMessage.contains("429 Too Many Requests")) {
                            log.error("OpenAI API quota exceeded");
                        } else if (errorMessage.contains("invalid_api_key") ||
                                   errorMessage.contains("Incorrect API key") ||
                                   errorMessage.contains("401 Unauthorized")) {
                            log.error("Invalid OpenAI API key");
                        }
                    }
                }
            }

            // If we have a fallback response, use it
            if (fallbackResponse != null) {
                // Save to history
                saveToHistory(message, fallbackResponse, userId);

                // Return the fallback response
                return Map.of(
                    "response", fallbackResponse,
                    "success", true,
                    "isFallback", true
                );
            }

            // If we get here, something went wrong and we don't have a fallback
            return Map.of(
                "success", false,
                "message", "Failed to get response from AI model",
                "friendlyMessage", "Impossible d'obtenir une réponse du modèle IA. Veuillez réessayer plus tard."
            );

        } catch (Exception e) {
            log.error("Error in chat service: {}", e.getMessage(), e);

            // Try to get a fallback response
            try {
                String fallbackResponse = getFallbackResponse(message);
                saveToHistory(message, fallbackResponse, userId);

                return Map.of(
                    "response", fallbackResponse,
                    "success", true,
                    "isFallback", true
                );
            } catch (Exception fallbackError) {
                log.error("Error getting fallback response: {}", fallbackError.getMessage(), fallbackError);
            }

            // Generic error response
            return Map.of(
                "success", false,
                "message", "Error processing chat request",
                "errorType", "general_error",
                "friendlyMessage", "Une erreur s'est produite lors du traitement de votre demande. Veuillez réessayer plus tard."
            );
        }
    }

    /**
     * Save a chat conversation to history
     *
     * @param userMessage The user's message
     * @param aiResponse The AI's response
     * @param userId The ID of the user
     * @return The saved history entry
     */
    public ChatHistory saveToHistory(String userMessage, String aiResponse, Long userId) {
        // Limit text length to prevent database issues
        if (userMessage.length() > 1000) {
            userMessage = userMessage.substring(0, 997) + "...";
        }

        if (aiResponse.length() > 2000) {
            aiResponse = aiResponse.substring(0, 1997) + "...";
        }

        ChatHistory history = new ChatHistory();
        history.setUserMessage(userMessage);
        history.setAiResponse(aiResponse);
        history.setUserId(userId);

        return chatHistoryRepository.save(history);
    }

    /**
     * Get the chat history for a user
     *
     * @param userId The ID of the user
     * @return A list of chat history entries
     */
    public List<ChatHistory> getHistoryForUser(Long userId) {
        return chatHistoryRepository.findByUserIdOrderByCreatedAtDesc(userId);
    }
}
