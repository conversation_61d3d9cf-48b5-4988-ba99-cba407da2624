package com.abonnements_microservices.controller;

import com.abonnements_microservices.dto.AbonnementTypeDTO;
import com.abonnements_microservices.services.AbonnementTypeService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/abonnements/types")
@CrossOrigin(origins = { "http://localhost:3036", "http://localhost:3000" })
@Slf4j
public class AbonnementTypeController {

    @Autowired
    private AbonnementTypeService abonnementTypeService;

    @GetMapping
    public ResponseEntity<List<AbonnementTypeDTO>> getAllAbonnementTypes() {
        return ResponseEntity.ok(abonnementTypeService.getAllAbonnementTypes());
    }

    @GetMapping("/{id}")
    public ResponseEntity<AbonnementTypeDTO> getAbonnementTypeById(@PathVariable Long id) {
        return ResponseEntity.ok(abonnementTypeService.getAbonnementTypeById(id));
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<AbonnementTypeDTO> createAbonnementType(@Valid @RequestBody AbonnementTypeDTO dto) {
        return ResponseEntity.status(HttpStatus.CREATED).body(abonnementTypeService.createAbonnementType(dto));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<AbonnementTypeDTO> updateAbonnementType(
            @PathVariable Long id,
            @Valid @RequestBody AbonnementTypeDTO dto) {
        return ResponseEntity.ok(abonnementTypeService.updateAbonnementType(id, dto));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteAbonnementType(@PathVariable Long id) {
        abonnementTypeService.deleteAbonnementType(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/page")
    public ResponseEntity<Page<AbonnementTypeDTO>> getAllAbonnementTypesPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        return ResponseEntity.ok(abonnementTypeService.getAllAbonnementTypesPage(page, size));
    }
    
    @PostMapping("/initialize")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<String> initializeDefaultTypes() {
        abonnementTypeService.initializeDefaultTypes();
        return ResponseEntity.ok("Default subscription types initialized successfully");
    }
}
