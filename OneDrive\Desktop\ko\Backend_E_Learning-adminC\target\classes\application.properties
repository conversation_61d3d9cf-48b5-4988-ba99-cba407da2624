# Existing security configuration
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080/realms/e-learning-realm
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=http://localhost:8080/realms/e-learning-realm/protocol/openid-connect/certs

# Keycloak Admin Client Configuration
keycloak.realm=e-learning-realm
keycloak.auth-server-url=http://localhost:8080
keycloak.resource=e-learning
keycloak.credentials.secret=aRjZ2PB2jnt6NVBypwy4dooa9Fx4lBku
keycloak.bearer-only=true

# Keycloak Admin Credentials
keycloak.admin.username=admin
keycloak.admin.password=0000

# Logging Configuration
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.springframework.security=DEBUG
logging.level.com.abonnements_microservices=DEBUG
logging.level.org.keycloak=DEBUG
logging.level.org.keycloak.admin.client=DEBUG

# Additional Keycloak Settings
keycloak.public-client=false
keycloak.ssl-required=external

# Twilio Configuration
twilio.account.sid=**********************************
twilio.auth.token=b733e4cd2cdc3e02c28be4ecf9a8240d
twilio.phone.number=+***********

# Brevo (Sendinblue) Email Configuration
brevo.api.key=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
brevo.sender.email=<EMAIL>
brevo.sender.name=E-Learning Platform

# 100ms Configuration
# Using 100ms for video conferencing
hmsvideo.app.access.key=681bc3be4944f067313a9afc
hmsvideo.app.secret=B3GIwpC1IK4URwBG1ZHuyKvSe7gHdxA4U6M9y5Ttf-AV7m6LR4IpyfQIxr-RUTKigMKZDSAb3cefWXh-F5mcz3Bc26HTPCVu_XsATiS9x3GdOXj-4G68jVgTPFfI3GxtNLcIeSD76zOgYUK2MH6hDJm2wC1BxMMo6MkYJf7DnkI=
hmsvideo.management.token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************.Y05QRoBlwEXTlWvYygJxC9k6E0TxuQRj9O8Pmwqzn54
hmsvideo.token.expiration=86400
# OpenAI Configuration
openai.api.key=********************************************************************************************************************************************************************