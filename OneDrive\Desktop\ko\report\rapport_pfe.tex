\documentclass[a4paper,12pt]{report}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage[french]{babel}
\usepackage{graphicx}
\usepackage{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{hyperref}
\usepackage{booktabs}
\usepackage{enumitem}
\usepackage{caption}
\usepackage{tocloft}
\usepackage{xcolor}

\geometry{margin=2.5cm}
\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    filecolor=magenta,      
    urlcolor=cyan,
}

\title{Projet de Fin de Parcours : Plateforme E-learning Deutza}
\author{Abir Saidi \and Oussema Nassraoui}
\date{Juin 2025}

\begin{document}

\maketitle

\begin{center}
    \textbf{Ministère de l’Enseignement Supérieur et de la Recherche Scientifique} \\
    \textbf{Direction Générale des Etudes Technologiques} \\
    \textbf{Institut Supérieur des Etudes Technologiques de Nabeul} \\
    \vspace{0.5cm}
    \textbf{Département Technologies de l’Informatique} \\
    \vspace{1cm}
    \textbf{PROJET DE FIN DE PARCOURS} \\
    \vspace{1cm}
    \textbf{Réalisé par :} \\
    Abir Saidi \\
    Oussema Nassraoui \\
    \vspace{0.5cm}
    \textbf{Société d’accueil :} Think Trend \\
    \textbf{Encadré par :} Afef Fkhiri, Dalel Loussayf \\
    \vspace{0.5cm}
    \textbf{Année Universitaire : 2024 – 2025} \\
    \textbf{Code : DSI21/25}
\end{center}}

\tableofcontents
\newpage

\chapter*{Introduction générale}
\addcontentsline{toc}{chapter}{Introduction générale}

Dans un monde en constante évolution, l’apprentissage en ligne s’est imposé comme une solution incontournable pour l’éducation et la formation. Grâce aux nouvelles technologies, il est désormais possible d’accéder à des contenus pédagogiques de qualité, à tout moment et depuis n’importe quel endroit.

C’est dans ce contexte que s’inscrit notre projet Deutza, une plateforme e-learning innovante qui vise à offrir une expérience d’apprentissage interactive et flexible. Cette solution permet aux utilisateurs d’accéder à des cours sous différents formats (vidéos, exercices Wee, sessions live, enregistrements), selon un modèle d’abonnement adapté à leurs besoins.

Le projet repose sur une architecture moderne, combinant React.js pour le frontend et un backend robuste permettant la gestion des utilisateurs, des contenus et des abonnements. L’objectif principal est de fournir une plateforme ergonomique, intuitive et performante, répondant aux exigences des apprenants et des enseignants.

Ce rapport détaillera les différentes étapes de conception et de développement de Deutza, en mettant en avant les choix technologiques, les fonctionnalités mises en place, ainsi que les défis rencontrés et les solutions adoptées.

\chapter{Étude préalable}

\section{Cadre du projet}

Avant de commencer le développement de Deutza, il était essentiel d’analyser le contexte et les besoins du projet. Ce chapitre présente l’étude préalable, en introduisant d’abord Think Trend, l’entreprise qui porte ce projet. Nous définirons ensuite les objectifs, les acteurs impliqués et les exigences fonctionnelles et techniques de la plateforme. Cette analyse permet de poser une base solide pour la conception et le développement de l’application.

\section{Présentation de Think Trend}

Think Trend est une entreprise spécialisée dans le développement et la gestion de stratégies de marketing digital innovantes. Nous offrons une gamme complète de services conçus pour aider les entreprises à se démarquer sur le marché numérique, à attirer leur public cible et à atteindre leurs objectifs commerciaux.

\begin{figure}[h]
    \centering
    \includegraphics[width=0.3\textwidth]{tt.jpg}
    \caption{Logo de l’entreprise Think Trend}
    \label{fig:thinktrend_logo}
\end{figure}

\section{Domaines d’Activités}

L’entreprise Think Trend propose une gamme variée de services dans plusieurs domaines clés. Voici un aperçu des principales prestations offertes :

\begin{itemize}
    \item \textbf{Stratégie Marketing} : Élaboration de stratégies marketing sur mesure, adaptées aux besoins spécifiques de chaque client.
    \item \textbf{Gestion des Réseaux Sociaux} : Création et gestion de contenus engageants pour les différentes plateformes sociales.
    \item \textbf{Création de Contenu} : Rédaction de contenus marketing percutants, conception de visuels attractifs et production de vidéos promotionnelles.
    \item \textbf{Publicité en Ligne} : Mise en place et gestion de campagnes publicitaires ciblées.
    \item \textbf{Développement Web} : Conception et développement de sites web optimisés pour favoriser les conversions.
    \item \textbf{Analyse et Reporting} : Suivi détaillé des performances des campagnes et fourniture de rapports complets.
    \item \textbf{Formations} : Cours spécialisés en développement web et marketing digital.
\end{itemize}

\section{Problématique traitée}

Dans un monde où la digitalisation transforme profondément les secteurs de l’éducation et de la formation, les plateformes d’apprentissage en ligne se multiplient. Cependant, ces plateformes rencontrent souvent des obstacles liés à la personnalisation des expériences d’apprentissage, à l’intégration de fonctionnalités complexes (comme la gestion des séances en direct), et à la gestion centralisée des ressources pédagogiques.

\textbf{La question centrale est :} Comment concevoir une plateforme éducative en ligne innovante, offrant une expérience utilisateur fluide et personnalisée, tout en garantissant une gestion centralisée, une flexibilité technique et une évolutivité adaptée aux besoins des utilisateurs et aux défis technologiques actuels ?

\section{Étude de l’existant}

\subsection{Plateformes éducatives internationales}

\subsubsection{Moodle}

\begin{itemize}
    \item \textbf{Description} : Système de gestion de l’apprentissage (LMS) open-source.
    \item \textbf{Fonctionnalités principales} :
    \begin{itemize}
        \item Gestion des cours (vidéos, PDF, quiz, évaluations).
        \item Forums de discussion.
        \item Notifications et rappels.
    \end{itemize}
    \item \textbf{Limites} :
    \begin{itemize}
        \item Interface utilisateur complexe pour les débutants.
        \item Intégration limitée pour des fonctionnalités comme le chat en temps réel.
    \end{itemize}
    \begin{figure}[h]
    \centering
    \includegraphics[width=0.5\textwidth]{moodle.jpg}
    \caption{Interface de Moodle}
    \label{fig:moodle}
\end{figure}
\end{itemize}




\subsubsection{Google Classroom}

\begin{itemize}
    \item \textbf{Description} : Plateforme gratuite pour organiser et gérer des cours en ligne.
    \item \textbf{Fonctionnalités principales} :
    \begin{itemize}
        \item Organisation des cours et des devoirs.
        \item Intégration avec Google Drive et Meet.
        \item Notifications automatiques.
    \end{itemize}
    
    \item \textbf{Limites} :
    \begin{itemize}
        \item Manque d’outils pour la communication en temps réel.
        \item Gestion limitée pour le suivi personnalisé des étudiants.
    \end{itemize}
\end{itemize}

\begin{figure}[h]
    \centering
    \includegraphics[width=0.5\textwidth]{GC.png}
    \caption{Interface de Google Classroom}
    \label{fig:google_classroom}
\end{figure}

\subsubsection{Udemy}

\begin{itemize}
    \item \textbf{Description} : Plateforme de cours en ligne pour formations spécialisées.
    \item \textbf{Fonctionnalités principales} :
    \begin{itemize}
        \item Accès à des cours en vidéo et des exercices.
        \item Système de paiement intégré.
        \item Certifications pour les cours terminés.
    \end{itemize}
    \item \textbf{Limites} :
    \begin{itemize}
        \item Orientation vers des formations générales.
        \item Absence de chat instantané.
    \end{itemize}
\end{itemize}

\begin{figure}[h]
    \centering
    \includegraphics[width=0.5\textwidth]{udemy.png}
    \caption{Interface d’Udemy}
    \label{fig:udemy}
\end{figure}

\subsection{Plateformes éducatives locales en Tunisie}

\subsubsection{TakiAcademy}

\begin{itemize}
    \item \textbf{Description} : Plateforme tunisienne spécialisée dans le soutien scolaire.
    \item \textbf{Fonctionnalités principales} :
    \begin{itemize}
        \item Cours en ligne avec vidéos enregistrées.
        \item Sessions interactives en direct.
        \item Gestion des emplois du temps.
    \end{itemize}
    \item \textbf{Limites} :
    \begin{itemize}
        \item Manque de communication directe étudiant-enseignant.
        \item Retard de performance.
    \end{itemize}
\end{itemize}

\begin{figure}[h]
    \centering
    \includegraphics[width=0.5\textwidth]{taki.png}
    \caption{Interface de TakiAcademy}
    \label{fig:takiacademy}
\end{figure}

\subsubsection{Almourabi}

\begin{itemize}
    \item \textbf{Description} : Plateforme éducistique tunisienne offrant des ressources pédagogiques.
    \item \textbf{Fonctionnalités principales} :
    \begin{itemize}
        \item Accès aux cours et exercices corrigés.
        \item Modules pour le baccalauréat tunisien.
    \end{itemize}
    \item \textbf{Limites} :
    \begin{itemize}
        \item Interface peu moderne.
        \item Manque de communication directe.
    \end{itemize}
\end{itemize}

\begin{figure}[h]
    \centering
    \includegraphics[width=0.5\textwidth]{mourabi.jpg}
    \caption{Interface d’Almourabi}
    \label{fig:almourabi}
\end{figure}

\section{Solution proposée}

La solution proposée consiste à développer une plateforme e-learning innovante utilisant une architecture microservices. Elle intégrera des fonctionnalités clés comme la gestion des utilisateurs, la création de cours multimédias, les notifications automatisées, les séances en direct avec Vimeo ou webinar, et un chat intégré. Grâce à des technologies comme Spring Boot, React.js, Docker, et une base de données optimisée, la solution garantit une expérience utilisateur intuitive, une scalabilité technique et une sécurité renforcée.

\section{Méthodologie de travail}

\subsection{Les méthodes agiles}

La méthode Agile privilégie la flexibilité, la collaboration et l’itération rapide. Ses principes, définis dans le Manifeste Agile (2001), incluent :

\begin{enumerate}
    \item Individus et interactions plutôt que processus et outils.
    \item Logiciel fonctionnel plutôt que documentation exhaustive.
    \item Collaboration avec le client plutôt que négociation contractuelle.
    \item Réponse au changement plutôt que suivi d’un plan strict.
\end{enumerate}

\subsection{Scrum : Une méthode agile spécifique}

Scrum est un framework agile favorisant un développement itératif et incrémental via des sprints.

\subsubsection{Principaux éléments de Scrum}

\begin{itemize}
    \item \textbf{Rôles} :
    \begin{itemize}
        \item \textbf{Product Owner} : Définit la vision du produit.
        \item \textbf{Scrum Master} : Assure l’application de Scrum.
        \item \textbf{Équipe de développement} : Transforme les besoins en fonctionnalités.
    \end{itemize}
    \item \textbf{Réunions} :
    \begin{itemize}
        \item Sprint Planning.
        \item Daily Scrum.
        \item Sprint Review.
        \item Sprint Retrospective.
    \end{itemize}
    \item \textbf{Artifacts} :
    \begin{itemize}
        \item Product Backlog.
        \item Sprint Backlog.
        \item Incrément.
    \end{itemize}
\end{itemize}

\subsubsection{Les principes fondamentaux de Scrum}

Scrum repose sur trois piliers : transparence, inspection, adaptation.

\subsubsection{Cinq valeurs de Scrum}

Engagement, courage, focus, ouverture, respect.

\begin{figure}[h]
    \centering
    \includegraphics[width=0.5\textwidth]{scrum.png}
    \caption{Cycle Scrum}
    \label{fig:scrum_cycle}
\end{figure}

\section{Conclusion}

Ce chapitre a analysé le contexte du projet Deutza, présenté Think Trend, les objectifs, et les enjeux du e-learning. L’étude des plateformes existantes a justifié notre approche innovante. La méthodologie Scrum a été retenue pour un développement flexible et efficace.

\chapter{Préparation du projet}

\section{Capture des besoins}

\subsection{Spécifications des besoins}

\subsubsection{Identification des acteurs}

\begin{itemize}
    \item \textbf{Administrateur} :
    \begin{itemize}
        \item Gestion des utilisateurs, abonnements, matières, cours, chapitres, niveaux d’études.
        \item Supervision des statistiques.
        \item Envoi de SMS et e-mails automatiques.
    \end{itemize}
    \item \textbf{Enseignant} :
    \begin{itemize}
        \item Donner des cours en direct.
        \item Évaluer les étudiants.
        \item Gérer les cours et chapitres.
    \end{itemize}
    \item \textbf{Étudiant} :
    \begin{itemize}
        \item Consulter les cours et vidéos.
        \item Participer aux séances en direct.
        \item Recevoir SMS et e-mails.
    \end{itemize}
\end{itemize}

\subsubsection{Besoins fonctionnels}

\begin{itemize}
    \item \textbf{Gestion des utilisateurs} : Rôles (admin, étudiant, enseignant), inscription, validation.
    \item \textbf{Gestion des cours} : Vidéos, PDF, catégorisation, recherche, suivi de progression.
    \item \textbf{Gestion des abonnements} :
    \begin{itemize}
        \item \textbf{Base} : Vidéos et exercices.
        \item \textbf{Intermédiaire} : Vidéos, exercices, enregistrements.
        \item \textbf{Premium} : Accès complet, séances en direct, communication avec enseignants.
    \end{itemize}
    \item \textbf{Gestion des matières et chapitres}.
    \item \textbf{Séances en direct} : Planning, accès via lien.
    \item \textbf{Système de notification} : E-mails pour confirmations.
    \item \textbf{Modèle d’IA} : Conversion PDF en audio.
    \item \textbf{Containerisation} : Déploiement avec Docker.
\end{itemize}

\subsubsection{Besoins non fonctionnels}

\begin{itemize}
    \item \textbf{Disponibilité} : 24/7.
    \item \textbf{Accessibilité} : Interface intuitive.
    \item \textbf{Maintenabilité} : Code modulaire.
    \item \textbf{Sécurité} : Authentification via Keycloak.
\end{itemize}

\subsection{Modélisation des besoins}

\begin{figure}[h]
    \centering
    \includegraphics[width=0.7\textwidth]{casD.png}
    \caption{Diagramme de cas d’utilisation globale}
    \label{fig:use_case}
\end{figure}

\section{Pilotage du projet avec Scrum}

\subsection{Équipe et rôles}

L’équipe comprend le Product Owner, le Scrum Master, et les développeurs.

\subsection{Le Backlog du produit}

\begin{table}[h]
    \centering
    \caption{Backlog du produit}
    \begin{tabular}{|c|l|p{7cm}|c|}
        \hline
        \textbf{ID} & \textbf{Fonctionnalité} & \textbf{User Story} & \textbf{Priorité} \\
        \hline
        1 & Authentification et création & En tant qu'utilisateur, je veux pouvoir m’authentifier... & Élevée \\
        2 & Validation des comptes & En tant qu'administrateur, je veux pouvoir valider... & \\
        3 & Gestion des abonnements & En tant qu'administrateur, je veux gérer les abonnements... & \\
        \hline
    \end{tabular}
    \label{tab:backlog}
\end{table}

\subsection{Planifications des sprints}

\begin{table}[h]
    \centering
    \caption{Planification des sprints}
    \begin{tabular}{|l|p{8cm}|c|}
        \hline
        \textbf{Sprint} & \textbf{Tâches} & \textbf{Durée} \\
        \hline
        Sprint 1: Module Administratif & Gestion des utilisateurs, abonnements, cours... & 3 semaines \\
        Sprint 2: Enseignant & Gestion des cours, planification des séances... & 4 semaines \\
        Sprint 3: Module étudiant & Consultation des cours, participation aux séances... & 4 semaines \\
        Sprint 4: IA et containerisation & Modèle AI, déploiement Docker... & 4 semaines \\
        \hline
    \end{tabular}
    \label{tab:sprints}
\end{table}

\section{Environnement de travail}

\subsection{Technologies et outils}

\subsubsection{Technologies}

\begin{itemize}
    \item \textbf{HTML} : Structure des pages web.
    \item \textbf{CSS} : Présentation des éléments.
    \item \textbf{JavaScript} : Interactivité.
    \item \textbf{Bootstrap} : Framework pour interfaces responsives.
    \item \textbf{React.js} : Bibliothèque pour interfaces utilisateur.
    \item \textbf{Spring Boot} : Backend robuste.
\end{itemize}

\subsubsection{Outils collaboratifs}

\begin{itemize}
    \item \textbf{Git} : Contrôle de version.
    \item \textbf{Postman} : Test d’API.
    \item \textbf{Draw.io} : Création de diagrammes.
    \item \textbf{Keycloak} : Gestion des identités.
    \item \textbf{100ms} : Vidéoconférence.
    \item \textbf{Twilio} : Communications.
    \item \textbf{Brevo} : Marketing numérique.
    \item \textbf{Vimeo} : Hébergement de vidéos.
\end{itemize}

\subsubsection{Langage de modélisation}

\textbf{UML} : Modélisation des cas d’utilisation, diagrammes de séquence, etc.

\begin{figure}[h]
    \centering
    \includegraphics[width=0.3\textwidth]{media/image23.png}
    \caption{Logo UML}
    \label{fig:uml}
\end{figure}

\section{Conclusion}

Ce chapitre a présenté le backlog, la conception, et les environnements technologiques utilisés. Le chapitre suivant abordera le premier sprint.

\end{document}