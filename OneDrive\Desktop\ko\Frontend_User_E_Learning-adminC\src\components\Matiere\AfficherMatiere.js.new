import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button, Form } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosService";
import axios from "axios";
import keycloak from "../../keycloak";
import Select from "react-select";

const AfficherMatiere = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [matiereModif, setMatiereModif] = useState(null);
  const [niveaux, setNiveaux] = useState([]);
  const [abonnements, setAbonnements] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [matieres, setMatieres] = useState([]);
  const [showModalDelete, setShowModalDelete] = useState(false);
  const [selectedMatiere, setSelectedMatiere] = useState(null);
  const [successMessage, setSuccessMessage] = useState("");
  const [errors, setErrors] = useState({});
  const [selectedImage, setSelectedImage] = useState(null);
  const [nouveauNom, setNouveauNom] = useState("");
  const [nouvelleDescription, setNouvelleDescription] = useState("");
  const [nouvelleDuree, setNouvelleDuree] = useState("");
  const [nouveauNiveau, setNouveauNiveau] = useState("");
  const [nouveauAbonnement, setNouveauAbonnement] = useState("");
  const [nouvelleImage, setNouvelleImage] = useState(null);
  const [imageUrls, setImageUrls] = useState({});
  const [userId, setUserId] = useState(null);
  const [isEnseignant, setIsEnseignant] = useState(false);
  const [chapitres, setChapitres] = useState([]);
  const [showNiveaux, setShowNiveaux] = useState(false);
  const [matiere, setMatiere] = useState(null);
  const [matiereCourante, setMatiereCourante] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [niveauSelectionne, setNiveauSelectionne] = useState("");
  
  // Video recording states
  const [liveSessionRecordings, setLiveSessionRecordings] = useState({});
  const [showRecordingsModal, setShowRecordingsModal] = useState(false);
  const [selectedMatiereRecordings, setSelectedMatiereRecordings] = useState(null);
  const [recordingFile, setRecordingFile] = useState(null);
  const [uploadingRecording, setUploadingRecording] = useState(false);
  const [recordingUploadSuccess, setRecordingUploadSuccess] = useState("");
  const [recordingUploadError, setRecordingUploadError] = useState("");
  const [selectedNiveau, setSelectedNiveau] = useState(null);
  const [filterNiveau, setFilterNiveau] = useState("");

  // Check if user is an enseignant and get user ID
  useEffect(() => {
    if (keycloak.authenticated) {
      const isTeacher = keycloak.hasResourceRole("ENSEIGNANT");
      setIsEnseignant(isTeacher);

      try {
        const tokenParsed = keycloak.tokenParsed;
        if (
          tokenParsed &&
          tokenParsed.preferred_username &&
          !isNaN(tokenParsed.preferred_username)
        ) {
          setUserId(tokenParsed.preferred_username);
        } else if (tokenParsed && tokenParsed.sub) {
          setUserId(tokenParsed.sub);
        }
      } catch (error) {
        console.error("Error parsing token:", error);
      }
    }
  }, []);

  const abonnementsParPage = 8;

  const loadImage = async (imageId) => {
    try {
      // Vérifie si imageId est valide avant d'envoyer la requête
      if (!imageId) {
        console.log("ID de l'image invalide");
        return;
      }

      const response = await axiosInstance.get(`/api/image/load/${imageId}`, {
        responseType: "blob",
        headers: {
          Accept: "image/*",
        },
      });
      const blobUrl = URL.createObjectURL(new Blob([response.data]));

      // Log pour vérifier que l'URL de l'image est bien ajoutée
      //console.log(`Image chargée: ${blobUrl}`);

      setImageUrls((prev) => ({ ...prev, [imageId]: blobUrl }));
    } catch (error) {
      console.error("Erreur lors du chargement de l'image:", error);
      setImageUrls((prev) => ({
        ...prev,
        [imageId]: prev[imageId] || "placeholder-image.jpg", // Image par défaut en cas d'erreur
      }));
    }
  };

  useEffect(() => {
    // Cleanup function to revoke blob URLs
    return () => {
      Object.values(imageUrls).forEach((url) => URL.revokeObjectURL(url));
    };
  }, [imageUrls]);
  
  // Fetch recordings for a specific matiere with optional niveau filter
  const fetchRecordings = async (matiereId, niveauId = null) => {
    try {
      console.log("Fetching recordings for matiere ID:", matiereId, "niveau ID:", niveauId);
      
      // Build the URL with or without the niveau filter
      let url = `/api/recordings/matiere/${matiereId}`;
      if (niveauId) {
        url = `/api/recordings/matiere/${matiereId}/niveau/${niveauId}`;
      }
      
      const response = await axiosInstance.get(url, {
        headers: { Authorization: `Bearer ${keycloak.token}` }
      });
      
      console.log("Recordings response:", response.data);
      
      // Update the recordings state for this matiere
      setLiveSessionRecordings(prev => ({
        ...prev,
        [matiereId]: response.data
      }));
      
      return response.data;
    } catch (error) {
      console.error("Error fetching recordings for matiere:", matiereId, "niveau:", niveauId, error);
      // Don't update state with empty array on error, keep previous state
      return liveSessionRecordings[matiereId] || [];
    }
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const promises = [
        isEnseignant && userId
          ? axiosInstance.get(`/api/enseignants/${userId}/matieres`)
          : axiosInstance.get(
              `/api/matieres/page?page=${
                currentPage - 1
              }&size=${abonnementsParPage}`
            ),
        axiosInstance.get("/api/niveaux/all"),
        axios.get("http://localhost:8084/api/abonnements/all", {
          headers: { Authorization: `Bearer ${keycloak.token}` },
        }),
      ];

      const [matieresRes, niveauxRes, abonnementsRes] = await Promise.all(
        promises
      );

      if (matieresRes.data) {
        if (isEnseignant && userId) {
          setMatieres(matieresRes.data || []);
          setTotalPages(
            Math.ceil(matieresRes.data.length / abonnementsParPage) || 1
          );
          
          // Fetch recordings for each matiere
          const matiereIds = matieresRes.data.map(matiere => matiere.idMatiere);
          for (const matiereId of matiereIds) {
            fetchRecordings(matiereId);
          }
        } else {
          setMatieres(matieresRes.data.content || []);
          setTotalPages(matieresRes.data.totalPages || 1);
          
          // Fetch recordings for each matiere
          const matiereIds = matieresRes.data.content.map(matiere => matiere.idMatiere);
          for (const matiereId of matiereIds) {
            fetchRecordings(matiereId);
          }
        }

        // Précharger les images
        const imagePromises =
          matieresRes.data.content?.map((matiere) => {
            const id = matiere?.image?.idImage;
            if (id) return loadImage(id);
            return Promise.resolve(null);
          }) || [];

        const urls = await Promise.all(imagePromises);
        const newImageUrls = {};
        matieresRes.data.content?.forEach((matiere, index) => {
          if (matiere.image?.idImage && urls[index]) {
            newImageUrls[matiere.image.idImage] = urls[index];
          }
        });
        setImageUrls(newImageUrls);
      }

      if (niveauxRes.data) setNiveaux(niveauxRes.data);
      if (abonnementsRes.data) setAbonnements(abonnementsRes.data);
    } catch (err) {
      console.error("Error fetching data:", err);
      setError(
        err.response?.data?.message || "Erreur lors du chargement des données"
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (keycloak.authenticated) fetchData();
  }, [currentPage, isEnseignant, userId]);

  const indexOfLastMatiere = currentPage * abonnementsParPage;
  const indexOfFirstMatiere = indexOfLastMatiere - abonnementsParPage;
  const matieresAffiches = matieres.slice(
    indexOfFirstMatiere,
    indexOfLastMatiere
  );

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const [formData, setFormData] = useState({
    nomMatiere: "",
    description: "",
    duree: "",
    niveaux: [],
    abonnements: [],
  });

  // Filtrage des matiere selon le terme de recherche
  const matieresFiltres = matieres.filter((matiere) => {
    return (
      (matiere.nomMatiere &&
        typeof matiere.nomMatiere === "string" &&
        matiere.nomMatiere.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (matiere.niveau &&
        typeof matiere.niveau === "string" &&
        matiere.niveau.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (matiere.duree &&
        !isNaN(matiere.duree) &&
        matiere.duree.toString().includes(searchTerm)) // Recherche par durée
    );
  });

  const [matiereToDelete, setMatiereToDelete] = useState(null);

  // Fonction pour afficher le modal de suppression
  const handleShowDeleteModal = (matiere) => {
    if (matiere && matiere.idMatiere) {
      setSelectedMatiere(matiere);
      setShowModalDelete(true);
    } else {
      console.error("Matière invalide !");
    }
  };

  const supprimerMatiere = async () => {
    if (!selectedMatiere || !selectedMatiere.idMatiere) {
      console.error("Erreur : Aucune matière sélectionnée.");
      return;
    }

    try {
      await axiosInstance.delete(`/api/matieres/${selectedMatiere.idMatiere}`);
      const response = await axiosInstance.get("/api/matieres");
      setMatieres(response.data);

      setMatieres((prevMatieres) =>
        prevMatieres.filter((m) => m.idMatiere !== selectedMatiere.idMatiere)
      );
      setShowModalDelete(false);
      setSelectedMatiere(null); // <-- Important
      console.log("selectedMatiere après suppression:", selectedMatiere); // <--

      setSuccessMessage("Matière supprimée avec succès");
      setTimeout(() => setSuccessMessage(""), 3000);
      fetchData();
    } catch (error) {
      console.error("Erreur lors de la suppression :", error);
      setError(
        error.response?.data?.message ||
          "Erreur lors de la suppression de la matière"
      );
    }
  };

  // Gérer le changement de fichier image
  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setSelectedImage(file);
  };

  // Fonction pour ouvrir le modal de modification
  const ouvrirFormModif = (matiere) => {
    setMatiereModif(matiere);
    setNouveauNom(matiere.nomMatiere);
    setNouvelleDescription(matiere.description);
    setNouvelleDuree(matiere.duree);
    setNouveauNiveau(matiere.niveaux?.map((niveau) => niveau.id) || []);
    setNouveauAbonnement(
      matiere.abonnements?.map((abonnement) => abonnement.id) || []
    );
    setNouvelleImage(null);
    setErrors({});
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Vérifier le type de fichier
      if (!file.type.match("image.*")) {
        setErrors({ general: "Veuillez sélectionner un fichier image valide" });
        return;
      }

      // Vérifier la taille du fichier (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setErrors({
          general: "La taille de l'image ne doit pas dépasser 10MB",
        });
        return;
      }

      setNouvelleImage(file);
      setErrors({});
    }
  };
  
  // Show recordings modal for a specific matiere
  const handleShowRecordingsModal = async (matiere) => {
    setSelectedMatiereRecordings(matiere);
    setRecordingFile(null);
    setRecordingUploadSuccess("");
    setRecordingUploadError("");
    setFilterNiveau(""); // Reset the filter when opening the modal
    
    // Force refresh the teacher/admin role status
    const hasTeacherRole = keycloak.hasResourceRole("ENSEIGNANT") || keycloak.hasRealmRole("ENSEIGNANT");
    const hasAdminRole = keycloak.hasResourceRole("ADMIN") || keycloak.hasRealmRole("ADMIN");
    console.log("Current user roles:", keycloak.resourceAccess);
    console.log("Current realm roles:", keycloak.realmAccess);
    console.log("Is user a teacher:", hasTeacherRole, "Is user an admin:", hasAdminRole);
    
    // Set isEnseignant based on role check
    setIsEnseignant(hasTeacherRole || hasAdminRole);
    
    setShowRecordingsModal(true);
    
    // Fetch the latest recordings for this matiere
    await fetchRecordings(matiere.idMatiere);
  };
  
  // Handle recording file selection
  const handleRecordingFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Verify file is a video type
      if (!file.type.includes("video/")) {
        setRecordingUploadError("Veuillez sélectionner un fichier vidéo valide");
        return;
      }
      
      // Verify file size (max 1GB)
      if (file.size > 1024 * 1024 * 1024) {
        setRecordingUploadError("La taille de la vidéo ne doit pas dépasser 1GB");
        return;
      }
      
      setRecordingFile(file);
      setRecordingUploadError("");
    }
  };
  
  // Upload recording using the API
  const handleUploadRecording = async () => {
    if (!recordingFile) {
      setRecordingUploadError("Veuillez sélectionner un fichier vidéo");
      return;
    }
    
    // Validate the selected niveau
    if (!selectedNiveau) {
      setRecordingUploadError("Veuillez sélectionner un niveau");
      return;
    }
    
    setUploadingRecording(true);
    setRecordingUploadSuccess("");
    setRecordingUploadError("");
    
    try {
      // Log debugging information
      console.log("Uploading recording for matiere:", selectedMatiereRecordings?.idMatiere);
      console.log("Selected niveau:", selectedNiveau);
      console.log("User roles:", keycloak.resourceAccess);
      
      const formData = new FormData();
      formData.append("file", recordingFile);
      formData.append("matiereId", selectedMatiereRecordings?.idMatiere);
      formData.append("niveauId", Number(selectedNiveau)); // Convert to number to ensure proper type
      formData.append("title", `Enregistrement ${new Date().toLocaleDateString()}`);
      formData.append("description", `Enregistrement du cours pour ${selectedMatiereRecordings?.nomMatiere}`);
      
      // Upload using the recordings API
      const response = await axiosInstance.post(
        `/api/recordings/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${keycloak.token}`
          }
        }
      );
      
      // Update the recordings list
      await fetchRecordings(selectedMatiereRecordings.idMatiere);
      
      setRecordingUploadSuccess("Enregistrement ajouté avec succès !");
      setRecordingFile(null);
      setSelectedNiveau(null);
      
      // Clear success message after 3 seconds
      setTimeout(() => setRecordingUploadSuccess(""), 3000);
    } catch (error) {
      console.error("Error uploading recording:", error);
      
      // Provide more specific error messages based on the error type
      if (error.message?.includes("Network Error") || 
          error.response?.data?.includes("api.vimeo.com") ||
          error.response?.status === 500) {
        setRecordingUploadError(
          "Problème de connexion au service de vidéo. Veuillez vérifier votre connexion internet ou réessayer plus tard. Si le problème persiste, contactez l'administrateur du système."
        );
      } else {
        setRecordingUploadError(
          error.response?.data || "Erreur lors de l'upload de l'enregistrement"
        );
      }
    } finally {
      setUploadingRecording(false);
    }
  };
  
  // Handle filter niveau change
  const handleFilterNiveauChange = async (e) => {
    const niveauId = e.target.value;
    setFilterNiveau(niveauId);
    
    if (selectedMatiereRecordings) {
      // If a niveau is selected, fetch filtered recordings
      // Otherwise, fetch all recordings for the matiere
      await fetchRecordings(
        selectedMatiereRecordings.idMatiere, 
        niveauId ? Number(niveauId) : null
      );
    }
  };

  const modifierMatiere = async () => {
    try {
      let requestData;
      let config = {};

      if (nouvelleImage) {
        requestData = new FormData();
        requestData.append("nomMatiere", nouveauNom);
        requestData.append("description", nouvelleDescription);
        requestData.append("duree", nouvelleDuree);
        nouveauNiveau.forEach((id) => {
          requestData.append("niveauIds", id);
        });
        nouveauAbonnement.forEach((id) => {
          requestData.append("abonnementIds", id);
        });
        requestData.append("image", nouvelleImage);

        config.headers = {
          "Content-Type": "multipart/form-data",
          Authorization: `Bearer ${keycloak.token}`,
        };
      } else {
        requestData = {
          nomMatiere: nouveauNom,
          description: nouvelleDescription,
          duree: nouvelleDuree,
          niveauIds: nouveauNiveau,
          abonnementIds: nouveauAbonnement,
        };

        config.headers = {
          "Content-Type": "application/json",
          Authorization: `Bearer ${keycloak.token}`, // ➕ ajoute ça ici aussi
        };
      }

      // Ajoutez cette ligne pour effectuer la requête PUT et capturer la réponse
      const response = await axiosInstance.put(
        `/api/matieres/${matiereModif.idMatiere}`,
        requestData,
        config
      );

      // Update the local state
      setMatieres((prevMatieres) =>
        prevMatieres.map((m) =>
          m.idMatiere === matiereModif.idMatiere
            ? {
                ...m,
                nomMatiere: nouveauNom,
                description: nouvelleDescription,
                duree: nouvelleDuree,
                niveaux: niveaux.filter((niveau) =>
                  nouveauNiveau.includes(niveau.id)
                ),

                abonnements: abonnements.filter((abonnement) =>
                  nouveauAbonnement.includes(abonnement.id)
                ),
                ...(response.data?.image && { image: response.data.image }),
              }
            : m
        )
      );
      fetchData();
      setMatiereModif(null);
      setSuccessMessage("Matière modifiée avec succès !");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Erreur lors de la modification :", error);
      setError("Une erreur est survenue lors de la modification de la matière");
    }
  };
  const handleSelectChange = (selectedOptions, field) => {
    const selectedValues = selectedOptions
      ? selectedOptions.map((option) => option.value)
      : [];
    setFormData((prev) => ({
      ...prev,
      [field]: selectedValues,
    }));
  };

  const handleListeClick = async (matiere) => {
    try {
      const response = await axios.get(
        "http://localhost:8084/api/niveaux/all",
        {
          headers: {
            Authorization: `Bearer ${keycloak.token}`,
          },
        }
      );
      setNiveaux(response.data);
      setMatiereCourante(matiere);
      setShowModal(true); // Open modal
    } catch (error) {
      console.error("Erreur en récupérant les niveaux", error);
    }
  };

  const handleNiveauSelect = async (e) => {
    const niveauId = e.target.value;
    if (!niveauId || !matiereCourante) return;

    try {
      const response = await axios.get(
        `http://localhost:8084/api/chapitres/matiere/${matiereCourante.idMatiere}/niveau/${niveauId}`,
        {
          headers: {
            Authorization: `Bearer ${keycloak.token}`,
          },
        }
      );
      setChapitres(response.data);

      // 🚀 Naviguer vers la page des chapitres
      navigate(`/chapitres/matiere/${matiereCourante.idMatiere}/${niveauId}`);
    } catch (error) {
      console.error("Erreur en récupérant les chapitres", error);
    }
  };

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0 d-flex align-items-center justify-content-between">
        {/* Titre */}
        <div className="col-auto">
          <h4 style={{ color: "#37A7DF" }}>Toutes les Matières</h4>
        </div>

        {/* Champ de recherche */}
        <div className="col-md-4">
          <Form.Control
            type="text"
            placeholder="Rechercher une matière..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* Bouton Ajouter */}
        <div className="col-auto">
          <button
            className="btn btn-primary"
            style={{
              backgroundColor: "#37A7DF",
              borderColor: "#37A7DF",
              color: "#fff",
            }}
            onClick={() => navigate("/matieres/ajouter")}
          >
            + Ajouter une Matière
          </button>
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}

      <div className="row">
        {matieresFiltres.length === 0 ? (
          <p>Aucune matière trouvée.</p>
        ) : (
          matieresFiltres.map((matiere) => {
            return (
              <div
                key={matiere.idMatiere}
                className="col-xl-3 col-lg-4 col-md-6 col-sm-6"
              >
                <div className="card">
                  {matiere.image ? (
                    <img
                      className="card-img-top"
                      src={
                        imageUrls[matiere.image.idImage] ||
                        "placeholder-image.jpg"
                      }
                      alt={matiere.nomMatiere}
                      style={{ height: "150px", objectFit: "cover" }}
                      onLoad={() => {
                        if (
                          matiere.image?.idImage &&
                          !imageUrls[matiere.image.idImage]
                        ) {
                          loadImage(matiere.image.idImage); // Charge l'image si l'idImage est défini
                        }
                      }}
                      onError={(e) => {
                        // Vérifie si l'ID est valide avant de tenter de recharger
                        if (
                          matiere.image?.idImage &&
                          !e.target.retryAttempted
                        ) {
                          e.target.retryAttempted = true;
                          loadImage(matiere.image.idImage); // Recharge l'image en cas d'erreur
                        }
                      }}
                    />
                  ) : (
                    <p className="text-muted text-center">Pas d'image</p>
                  )}

                  <div className="card-body">
                    <h4>{matiere.nomMatiere}</h4>
                    <p>{matiere.description}</p>
                    <p>
                      <strong>Durée :</strong> {matiere.duree}H
                    </p>
                    {matiere.abonnementIds &&
                      matiere.abonnementIds.length > 0 && (
                        <div>
                          <strong>Abonnements :</strong>
                          <div className="mt-1">
                            {matiere.abonnementIds.map((abonnement) => (
                              <span
                                key={abonnement.id}
                                className="badge badge-primary mr-1"
                              >
                                {abonnement.nom}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                   


                    {matiere.niveauIds && matiere.niveauIds.length > 0 && (
                      <div>
                        <strong>Niveaux :</strong>
                        <div className="mt-1">
                          {matiere.niveauIds.map((niveau) => (
                            <span
                              key={niveau.id}
                              className="badge badge-primary mr-1"
                            >
                              {niveau.nom}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                   {matiere.niveauIds && niveaux.length > 0 && (
                      <div>
                        <strong>Niveaux :</strong>
                        <div className="mt-1">
                          {matiere.niveauIds.map((niv) => {
                            // niv peut être soit un ID, soit un objet { id, nom }
                            const niveauId = typeof niv === "object" ? niv.id : niv;
                            const niveau = niveaux.find((n) => n.id === niveauId);
                            return (
                              <span key={niveauId} className="badge badge-info mr-1">
                                {niveau?.nom || (typeof niv === "object" ? niv.nom : "Niveau inconnu")}
                              </span>
                            );
                          })}
                        </div>
                      </div>
                    )}

                    <div>
                      <button
                        onClick={() => handleListeClick(matiere)}
                        className="btn btn-primary mt-2 w-100 btn-sm"
                        style={{
                          backgroundColor: "#37A7DF",
                          borderColor: "#37A7DF",
                        }}
                      >
                        Liste Chapitre
                      </button>
                      
                      <button
                        onClick={() => handleShowRecordingsModal(matiere)}
                        className="btn btn-info mt-2 w-100 btn-sm"
                        style={{
                          backgroundColor: "#6c757d",
                          borderColor: "#6c757d",
                        }}
                      >
                        <i className="fa fa-video-camera mr-1"></i> Enregistrements Live
                      </button>
                      
                      <Modal
                        show={showModal}
                        onHide={() => setShowModal(false)}
                        centered
                      >
                        <Modal.Header
                          closeButton
                          style={{
                            backgroundColor: "#EEF9F5",
                            borderBottom: "1px solid #B7B7B7",
                          }}
                        >
                          <Modal.Title
                            style={{ color: "#1D1D1B", fontWeight: "bold" }}
                          >
                            Choisir un niveau pour{" "}
                            <span style={{ color: "#37A7DF" }}>
                              {matiereCourante?.nomMatiere}
                            </span>
                          </Modal.Title>
                        </Modal.Header>

                        <Modal.Body style={{ backgroundColor: "#F6F4EE" }}>
                          <Form.Group>
                            <Form.Label style={{ color: "#1D1D1B" }}>
                              Niveau
                            </Form.Label>
                            <Form.Select
                              value={niveauSelectionne}
                              onChange={(e) =>
                                setNiveauSelectionne(e.target.value)
                              }
                              style={{
                                borderColor: "#B7B7B7",
                                backgroundColor: "#fff",
                                color: "#1D1D1B",
                              }}
                            >
                              <option value="">
                                -- Sélectionner un niveau --
                              </option>
                              {niveaux.map((niveau) => (
                                <option key={niveau.id} value={niveau.id}>
                                  {niveau.nom}
                                </option>
                              ))}
                            </Form.Select>
                          </Form.Group>
                        </Modal.Body>

                        <Modal.Footer
                          style={{
                            backgroundColor: "#EEF9F5",
                            borderTop: "1px solid #B7B7B7",
                          }}
                        >
                          <Button
                            variant="secondary"
                            onClick={() => setShowModal(false)}
                            style={{
                              backgroundColor: "#B7B7B7",
                              borderColor: "#B7B7B7",
                              color: "#fff",
                            }}
                          >
                            Annuler
                          </Button>
                          <Button
                            onClick={() => {
                              navigate(
                                `/chapitres/matiere/${matiereCourante.idMatiere}/${niveauSelectionne}`
                              );
                              setShowModal(false);
                            }}
                            disabled={!niveauSelectionne}
                            style={{
                              backgroundColor: "#248E39",
                              borderColor: "#248E39",
                              color: "#fff",
                            }}
                          >
                            Voir les chapitres
                          </Button>
                        </Modal.Footer>
                      </Modal>

                      <button
                        onClick={() => ouvrirFormModif(matiere)}
                        className="btn btn-primary mt-2 w-100 btn-sm"
                        style={{
                          backgroundColor: "#F2BC00",
                          borderColor: "#F2BC00",
                        }}
                      >
                        Modifier
                      </button>

                      <Button
                        className=" btn btn-danger light mt-2 w-100 btn-sm"
                        variant="danger"
                        onClick={() => handleShowDeleteModal(matiere)}
                      >
                        Supprimer
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Pagination : boutons pour naviguer entre les pages */}
      <div className="d-flex justify-content-center mt-3">
        <button
          className="btn btn-secondary mx-1"
          onClick={goToPrevPage}
          disabled={currentPage === 1}
        >
          Précédent
        </button>
        <span className="mx-2">
          Page {currentPage} sur {totalPages}
        </span>
        <button
          className="btn btn-secondary mx-1"
          onClick={goToNextPage}
          disabled={currentPage === totalPages}
        >
          Suivant
        </button>
      </div>

      {matiereModif && (
        <div
          className="modal fade show d-block"
          style={{ background: "rgba(0,0,0,0.5)" }}
        >
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Modifier la matière</h5>
                <button
                  className="btn-close"
                  onClick={() => setMatiereModif(null)}
                ></button>
              </div>

              <div className="modal-body">
                {successMessage && (
                  <div className="alert alert-success">{successMessage}</div>
                )}
                {errors.general && (
                  <div className="alert alert-danger">{errors.general}</div>
                )}
                <input
                  type="file"
                  accept="image/*"
                  className="form-control mb-2"
                  onChange={(e) => setNouvelleImage(e.target.files[0])}
                />
                <input
                  type="text"
                  className="form-control mb-2"
                  value={nouveauNom}
                  onChange={(e) => setNouveauNom(e.target.value)}
                  placeholder="Nom de la matière"
                />
                <input
                  type="text"
                  className="form-control mb-2"
                  value={nouvelleDescription}
                  onChange={(e) => setNouvelleDescription(e.target.value)}
                  placeholder="Description"
                />
                <input
                  type="number"
                  className="form-control mb-2"
                  value={nouvelleDuree}
                  onChange={(e) => setNouvelleDuree(e.target.value)}
                  placeholder="Durée"
                />

                {/* Sélection du niveau */}

                <Select
                  isMulti
                  value={nouveauNiveau
                    .map((id) => {
                      const niveau = niveaux.find((a) => a.id === id);
                      return niveau
                        ? { value: niveau.id, label: niveau.nom }
                        : null;
                    })
                    .filter(Boolean)}
                  options={niveaux.map((niveau) => ({
                    value: niveau.id,
                    label: `${niveau.nom} `,
                  }))}
                  onChange={(selectedOptions) => {
                    setNouveauNiveau(
                      selectedOptions
                        ? selectedOptions.map((option) => option.value)
                        : []
                    );
                  }}
                  placeholder="Sélectionner plusieurs niveaux"
                  styles={{
                    control: (provided) => ({
                      ...provided,
                      width: "100%",
                      borderColor: "#ccc",
                      borderRadius: "4px",
                      padding: "5px",
                    }),
                    menu: (provided) => ({
                      ...provided,
                      borderRadius: "4px",
                      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                    }),
                  }}
                />

                {/* Sélection de l'abonnement */}
                <Select
                  isMulti
                  value={nouveauAbonnement
                    .map((id) => {
                      const abonnement = abonnements.find((a) => a.id === id);
                      return abonnement
                        ? { value: abonnement.id, label: abonnement.nom }
                        : null;
                    })
                    .filter(Boolean)}
                  options={abonnements.map((abonnement) => ({
                    value: abonnement.id,
                    label: `${abonnement.nom} - ${abonnement.prix} DT`,
                  }))}
                  onChange={(selectedOptions) => {
                    setNouveauAbonnement(
                      selectedOptions
                        ? selectedOptions.map((option) => option.value)
                        : []
                    );
                  }}
                  placeholder="Sélectionner plusieurs abonnements"
                  styles={{
                    control: (provided) => ({
                      ...provided,
                      width: "100%",
                      borderColor: "#ccc",
                      borderRadius: "4px",
                      padding: "5px",
                    }),
                    menu: (provided) => ({
                      ...provided,
                      borderRadius: "4px",
                      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                    }),
                  }}
                />
              </div>

              <div className="modal-footer">
                <button
                  className="btn btn-danger"
                  onClick={() => setMatiereModif(null)}
                >
                  Annuler
                </button>
                <button className="btn btn-primary" onClick={modifierMatiere}>
                  Enregistrer
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Confirmation de suppression */}
      <Modal
        show={showModalDelete}
        onHide={() => setShowModalDelete(false)}
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>Confirmer la suppression</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Êtes-vous sûr de vouloir supprimer la matière{" "}
          <strong>{selectedMatiere?.nomMatiere}</strong> ?
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModalDelete(false)}>
            Annuler
          </Button>
          <Button variant="danger" onClick={supprimerMatiere}>
            Supprimer
          </Button>
        </Modal.Footer>
      </Modal>
      
      {/* Modal for Live Session Recordings */}
      <Modal
        show={showRecordingsModal}
        onHide={() => setShowRecordingsModal(false)}
        size="lg"
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>
            Enregistrements des séances live - {selectedMatiereRecordings?.nomMatiere}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-2">
            {/* Debug info - remove in production */}
            <div className="small text-muted mb-2">
              Status: {isEnseignant ? "Enseignant/Admin détecté" : "Non détecté comme Enseignant/Admin"}
              <br />
              Admin role: {keycloak.hasRealmRole("ADMIN") ? "Yes (Realm)" : "No (Realm)"} / {keycloak.hasResourceRole("ADMIN") ? "Yes (Resource)" : "No (Resource)"}
              <br />
              Teacher role: {keycloak.hasRealmRole("ENSEIGNANT") ? "Yes (Realm)" : "No (Realm)"} / {keycloak.hasResourceRole("ENSEIGNANT") ? "Yes (Resource)" : "No (Resource)"}
            </div>
          </div>

          {(isEnseignant || keycloak.hasRealmRole("ADMIN") || keycloak.hasRealmRole("ENSEIGNANT")) && (
            <div className="mb-4 p-3 border rounded">
              <h5>Ajouter un nouvel enregistrement</h5>
              
              {recordingUploadSuccess && (
                <div className="alert alert-success">{recordingUploadSuccess}</div>
              )}
              
              {recordingUploadError && (
                <div className="alert alert-danger">{recordingUploadError}</div>
              )}
              
              <div className="custom-file mb-3">
                <input
                  type="file"
                  className="custom-file-input"
                  id="recordingFile"
                  accept="video/*"
                  onChange={handleRecordingFileChange}
                  disabled={uploadingRecording}
                />
                <label className="custom-file-label" htmlFor="recordingFile">
                  {recordingFile ? recordingFile.name : "Choisir un fichier vidéo"}
                </label>
              </div>
              
              {/* Add niveau dropdown */}
              <div className="form-group mb-3">
                <label htmlFor="recordingNiveau">Niveau</label>
                <select
                  className="form-control"
                  id="recordingNiveau"
                  value={selectedNiveau || ""}
                  onChange={(e) => setSelectedNiveau(e.target.value)}
                  disabled={uploadingRecording}
                >
                  <option value="">Sélectionner un niveau</option>
                  {niveaux.map((niveau) => (
                    <option key={niveau.id} value={niveau.id}>
                      {niveau.nom}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="form-group">
                <button 
                  className="btn btn-primary w-100"
                  onClick={handleUploadRecording}
                  disabled={uploadingRecording || !recordingFile}
                >
                  <i className="fa fa-upload mr-2"></i>
                  Uploader l'enregistrement
                </button>
              </div>
              
              {uploadingRecording && (
                <div className="text-center">
                  <div className="spinner-border text-primary" role="status">
                    <span className="sr-only">Chargement...</span>
                  </div>
                  <p>Upload en cours... Cela peut prendre quelques minutes</p>
                </div>
              )}
            </div>
          )}
          
          <div className="d-flex justify-content-between align-items-center mb-3">
            <h5>Enregistrements disponibles</h5>
            <div className="form-group mb-0" style={{ width: "250px" }}>
              <select
                className="form-control form-control-sm"
                value={filterNiveau}
                onChange={handleFilterNiveauChange}
              >
                <option value="">Tous les niveaux</option>
                {niveaux.map((niveau) => (
                  <option key={niveau.id} value={niveau.id}>
                    {niveau.nom}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          {liveSessionRecordings[selectedMatiereRecordings?.idMatiere]?.length > 0 ? (
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    <th>Titre</th>
                    <th>Niveau</th>
                    <th>Date</th>
                    <th>Vidéo</th>
                  </tr>
                </thead>
                <tbody>
                  {liveSessionRecordings[selectedMatiereRecordings?.idMatiere]
                    ?.map(recording => {
                      // Extract video ID from Vimeo URL
                      const vimeoMatch = recording.videoUrl?.match(/(?:\/videos\/|vimeo\.com\/|)(\d+)/);
                      const videoId = vimeoMatch ? vimeoMatch[1] : null;
                      
                      return (
                        <tr key={recording.id}>
                          <td>{recording.title}</td>
                          <td>{recording.niveau?.nom || 'Non spécifié'}</td>
                          <td>
                            {recording.recordingDate ? 
                              new Date(recording.recordingDate).toLocaleDateString() : 
                              'Non spécifiée'}
                          </td>
                          <td>
                            {videoId ? (
                              <div className="embed-responsive embed-responsive-16by9">
                                <iframe
                                  src={`https://player.vimeo.com/video/${videoId}`}
                                  className="embed-responsive-item"
                                  frameBorder="0"
                                  allow="autoplay; fullscreen; picture-in-picture"
                                  allowFullScreen
                                ></iframe>
                              </div>
                            ) : (
                              <a 
                                href={recording.videoUrl} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="btn btn-sm btn-info"
                              >
                                Voir la vidéo
                              </a>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-center font-italic">
              Aucun enregistrement disponible pour cette matière.
            </p>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowRecordingsModal(false)}>
            Fermer
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default AfficherMatiere;
