import React, { useEffect, useState } from "react";
import axiosInstance from "../../services/axiosService";
import "../../styles/responsive.css";
import "../../styles/theme.css";

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalCours: 0,
    totalAbonnements: 0,
    totalNiveaux: 0,
    totalMatiere: 0
  });

  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        const response = await axiosInstance.get("/api/statistics");
        setStats(response.data);
      } catch (error) {
        console.error("Error fetching statistics:", error);
        setError("Failed to load statistics");
      }
    };

    fetchStatistics();
  }, []);

  if (error) {
    return (
        <div className="container-fluid">
          <div className="alert alert-danger">{error}</div>
        </div>
    );
  }

  return (
      <div className="container-fluid">
        <div className="row">
          {/* Total Cours */}
          <div className="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-3">
            <div className="widget-stat card h-100" style={{ backgroundColor: 'var(--primary-blue)' }}>
              <div className="card-body">
                <div className="media d-flex align-items-center">
                  <span className="me-3" style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    width: '50px',
                    height: '50px',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <i className="la la-graduation-cap fs-3 text-white"></i>
                  </span>
                  <div className="media-body text-white">
                    <p className="mb-1" style={{ fontSize: '0.9rem', opacity: '0.8' }}>Total Cours</p>
                    <h3 className="text-white" style={{ fontWeight: '700', marginBottom: '0' }}>{stats.totalCours}</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Total Abonnements */}
          <div className="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-3">
            <div className="widget-stat card h-100" style={{ backgroundColor: 'var(--primary-yellow)' }}>
              <div className="card-body">
                <div className="media d-flex align-items-center">
                  <span className="me-3" style={{
                    backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    width: '50px',
                    height: '50px',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <i className="la la-book fs-3 text-dark"></i>
                  </span>
                  <div className="media-body text-dark">
                    <p className="mb-1" style={{ fontSize: '0.9rem', opacity: '0.8' }}>Total Abonnements</p>
                    <h3 className="text-dark" style={{ fontWeight: '700', marginBottom: '0' }}>{stats.totalAbonnements}</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Total Niveaux */}
          <div className="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-3">
            <div className="widget-stat card h-100" style={{ backgroundColor: 'var(--primary-green)' }}>
              <div className="card-body">
                <div className="media d-flex align-items-center">
                  <span className="me-3" style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    width: '50px',
                    height: '50px',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <i className="la la-layer-group fs-3 text-white"></i>
                  </span>
                  <div className="media-body text-white">
                    <p className="mb-1" style={{ fontSize: '0.9rem', opacity: '0.8' }}>Total Niveaux</p>
                    <h3 className="text-white" style={{ fontWeight: '700', marginBottom: '0' }}>{stats.totalNiveaux}</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Total Matières */}
          <div className="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-3">
            <div className="widget-stat card h-100" style={{ backgroundColor: 'var(--primary-navy)' }}>
              <div className="card-body">
                <div className="media d-flex align-items-center">
                  <span className="me-3" style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    width: '50px',
                    height: '50px',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <i className="la la-book-open fs-3 text-white"></i>
                  </span>
                  <div className="media-body text-white">
                    <p className="mb-1" style={{ fontSize: '0.9rem', opacity: '0.8' }}>Total Matières</p>
                    <h3 className="text-white" style={{ fontWeight: '700', marginBottom: '0' }}>{stats.totalMatiere}</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Responsive welcome message */}
        <div className="row mt-4">
          <div className="col-12">
            <div className="card" style={{
              backgroundColor: 'var(--primary-cream)',
              borderLeft: '4px solid var(--primary-blue)',
              boxShadow: '0 4px 10px rgba(0, 0, 0, 0.05)'
            }}>
              <div className="card-body">
                <div className="d-flex align-items-center mb-3">
                  <i className="la la-info-circle me-2" style={{
                    fontSize: '24px',
                    color: 'var(--primary-blue)'
                  }}></i>
                  <h4 className="card-title mb-0" style={{
                    color: 'var(--primary-dark)',
                    fontWeight: '600'
                  }}>Bienvenue sur le tableau de bord</h4>
                </div>
                <p className="card-text" style={{ color: 'var(--text-secondary)' }}>
                  Utilisez la navigation pour accéder aux différentes sections de l'application.
                  Vous pouvez gérer les cours, les abonnements, les niveaux et les matières depuis ce tableau de bord.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
  );
};

export default Dashboard;
