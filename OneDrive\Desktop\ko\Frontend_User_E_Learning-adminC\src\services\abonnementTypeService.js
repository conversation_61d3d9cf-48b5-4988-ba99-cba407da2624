import axiosInstance from './axiosService';

const API_URL = '/api/abonnements/types';

const abonnementTypeService = {
  // Get all subscription types
  getAllTypes: async () => {
    try {
      const response = await axiosInstance.get(API_URL);
      return response.data;
    } catch (error) {
      console.error('Error fetching subscription types:', error);
      throw error;
    }
  },

  // Get subscription type by ID
  getTypeById: async (id) => {
    try {
      const response = await axiosInstance.get(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching subscription type with ID ${id}:`, error);
      throw error;
    }
  },

  // Create a new subscription type
  createType: async (typeData) => {
    try {
      const response = await axiosInstance.post(API_URL, typeData);
      return response.data;
    } catch (error) {
      console.error('Error creating subscription type:', error);
      throw error;
    }
  },

  // Update an existing subscription type
  updateType: async (id, typeData) => {
    try {
      const response = await axiosInstance.put(`${API_URL}/${id}`, typeData);
      return response.data;
    } catch (error) {
      console.error(`Error updating subscription type with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete a subscription type
  deleteType: async (id) => {
    try {
      const response = await axiosInstance.delete(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting subscription type with ID ${id}:`, error);
      throw error;
    }
  },

  // Initialize default subscription types
  initializeDefaultTypes: async () => {
    try {
      const response = await axiosInstance.post(`${API_URL}/initialize`);
      return response.data;
    } catch (error) {
      console.error('Error initializing default subscription types:', error);
      throw error;
    }
  }
};

export default abonnementTypeService;
