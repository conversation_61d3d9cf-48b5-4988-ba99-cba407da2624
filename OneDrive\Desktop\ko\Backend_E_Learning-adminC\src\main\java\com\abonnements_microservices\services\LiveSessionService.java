package com.abonnements_microservices.services;

import com.abonnements_microservices.model.*;
import com.abonnements_microservices.repo.EnseignantRepository;
import com.abonnements_microservices.repo.LiveSessionRepository;
import com.abonnements_microservices.repo.MatiereRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class LiveSessionService {

    @Autowired
    private LiveSessionRepository liveSessionRepository;

    @Autowired
    private EnseignantRepository enseignantRepository;

    @Autowired
    private MatiereRepository matiereRepository;

    @Autowired
    private HMSVideoService hmsVideoService;

    /**
     * Creates a new live session
     */
    @Transactional
    public LiveSession createLiveSession(LiveSession liveSession, Long enseignantId, Long matiereId) {
        // Validate enseignant
        Enseignant enseignant = enseignantRepository.findById(enseignantId)
            .orElseThrow(() -> new EntityNotFoundException("Enseignant not found with id: " + enseignantId));
        liveSession.setEnseignant(enseignant);

        // Validate matiere if provided
        if (matiereId != null) {
            Matiere matiere = matiereRepository.findById(matiereId)
                .orElseThrow(() -> new EntityNotFoundException("Matiere not found with id: " + matiereId));
            liveSession.setMatiere(matiere);
        }

        // Generate a unique room name if not provided
        if (liveSession.getRoomName() == null || liveSession.getRoomName().isEmpty()) {
            String prefix = enseignant.getUsername().replaceAll("[^a-zA-Z0-9]", "");
            liveSession.setRoomName(hmsVideoService.generateRoomName(prefix));
        }

        // Set default status if not specified
        if (liveSession.getStatus() == null) {
            liveSession.setStatus(LiveSessionStatus.SCHEDULED);
        }

        return liveSessionRepository.save(liveSession);
    }    /**
     * Start a live session
     */
    @Transactional
    public LiveSession startLiveSession(Long sessionId) {
        LiveSession session = getLiveSessionById(sessionId);

        // Only scheduled sessions can be started
        if (session.getStatus() != LiveSessionStatus.SCHEDULED) {
            throw new IllegalStateException("Only scheduled sessions can be started. Current status: " + session.getStatus());
        }

        // Update session status and start time
        session.setStatus(LiveSessionStatus.LIVE);
        session.setActualStartTime(LocalDateTime.now());
        
        // Create the room in 100ms service and store the actual roomId
        String description = session.getTitle();
        if (session.getDescription() != null && !session.getDescription().isEmpty()) {
            description += " - " + session.getDescription();
        }
        String roomId = hmsVideoService.createRoom(session.getRoomName(), description);
        session.setRoomId(roomId);
        
        return liveSessionRepository.save(session);
    }

    /**
     * End a live session
     */
    @Transactional
    public LiveSession endLiveSession(Long sessionId) {
        LiveSession session = getLiveSessionById(sessionId);

        // Only live sessions can be ended
        if (session.getStatus() != LiveSessionStatus.LIVE) {
            throw new IllegalStateException("Only live sessions can be ended. Current status: " + session.getStatus());
        }

        // Update session status and end time
        session.setStatus(LiveSessionStatus.COMPLETED);
        session.setActualEndTime(LocalDateTime.now());
        return liveSessionRepository.save(session);
    }

    /**
     * Cancel a scheduled session
     */
    @Transactional
    public LiveSession cancelLiveSession(Long sessionId) {
        LiveSession session = getLiveSessionById(sessionId);

        // Only scheduled sessions can be cancelled
        if (session.getStatus() != LiveSessionStatus.SCHEDULED) {
            throw new IllegalStateException("Only scheduled sessions can be cancelled. Current status: " + session.getStatus());
        }

        session.setStatus(LiveSessionStatus.CANCELLED);
        return liveSessionRepository.save(session);
    }

    /**
     * Get live session by ID
     */
    public LiveSession getLiveSessionById(Long id) {
        return liveSessionRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Live session not found with id: " + id));
    }

    /**
     * Get all live sessions
     */
    public List<LiveSession> getAllLiveSessions() {
        return liveSessionRepository.findAll();
    }

    /**
     * Get live sessions by enseignant
     */
    public List<LiveSession> getLiveSessionsByEnseignant(Long enseignantId) {
        return liveSessionRepository.findByEnseignantId(enseignantId);
    }

    /**
     * Get live sessions by matiere
     */
    public List<LiveSession> getLiveSessionsByMatiere(Long matiereId) {
        return liveSessionRepository.findByMatiereIdMatiere(matiereId);
    }

    /**
     * Get live sessions by status
     */
    public List<LiveSession> getLiveSessionsByStatus(LiveSessionStatus status) {
        return liveSessionRepository.findByStatus(status);
    }

    /**
     * Get current live sessions for an enseignant
     */
    public List<LiveSession> getCurrentSessionsForEnseignant(Long enseignantId) {
        return liveSessionRepository.findByEnseignantIdAndStatus(enseignantId, LiveSessionStatus.LIVE);
    }    /**
     * Generate 100ms authentication and join info for a participant joining a session
     */
    public Map<String, Object> generateSessionJoinInfo(Long sessionId, Long userId, String username, boolean isTeacher) {
        LiveSession session = getLiveSessionById(sessionId);

        // Check if the session is live or about to start
        if (session.getStatus() != LiveSessionStatus.LIVE && session.getStatus() != LiveSessionStatus.SCHEDULED) {
            throw new IllegalStateException("Cannot join a " + session.getStatus().toString().toLowerCase() + " session");
        }
        
        // For scheduled sessions that haven't been started yet, we need to create the room first
        if (session.getStatus() == LiveSessionStatus.SCHEDULED && (session.getRoomId() == null || session.getRoomId().isEmpty())) {
            // Create the room in 100ms service and store the actual roomId
            String description = session.getTitle();
            if (session.getDescription() != null && !session.getDescription().isEmpty()) {
                description += " - " + session.getDescription();
            }
            
            // First check if the room already exists by name, otherwise create it
            String roomId = hmsVideoService.ensureRoomExists(session.getRoomName(), description);
            session.setRoomId(roomId);
            session = liveSessionRepository.save(session);
        }
        
        // We must have a valid roomId at this point
        if (session.getRoomId() == null || session.getRoomId().isEmpty()) {
            throw new IllegalStateException("Cannot join session: Room has not been properly created");
        }

        // Get join info from 100ms service using the actual room ID, not just the room name
        Map<String, Object> hmsJoinInfo = hmsVideoService.getSessionJoinInfo(
            session.getRoomId(),
            userId.toString(),
            username,
            isTeacher
        );

        // Return session details and connection info
        Map<String, Object> result = new HashMap<>();
        result.put("sessionId", session.getId());
        result.put("title", session.getTitle());
        result.put("description", session.getDescription());
        result.put("roomId", session.getRoomId());
        result.put("token", hmsJoinInfo.get("token"));
        result.put("username", username);
        result.put("isTeacher", isTeacher);
        result.put("role", hmsJoinInfo.get("role"));
        result.put("appId", hmsJoinInfo.get("appId"));

        // Add UI configuration options
        Map<String, Object> uiConfig = new HashMap<>();
        uiConfig.put("hideControls", false);
        uiConfig.put("autoJoin", true);
        uiConfig.put("startWithAudioMuted", false);
        uiConfig.put("startWithVideoMuted", false);
        result.put("uiConfig", uiConfig);

        return result;
    }
}