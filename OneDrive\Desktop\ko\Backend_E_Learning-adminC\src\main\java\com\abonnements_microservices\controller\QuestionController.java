package com.abonnements_microservices.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.abonnements_microservices.model.Question;
import com.abonnements_microservices.services.QuestionService;

import jakarta.persistence.EntityNotFoundException;

@RestController
@RequestMapping("/api/forum/questions")
@CrossOrigin(origins = {"http://localhost:3036", "http://localhost:3000"})
public class QuestionController {

    @Autowired
    private QuestionService questionService;

    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllQuestions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<Question> questionsPage = questionService.getAllQuestions(pageable);

        Map<String, Object> response = new HashMap<>();
        response.put("questions", questionsPage.getContent());
        response.put("currentPage", questionsPage.getNumber());
        response.put("totalItems", questionsPage.getTotalElements());
        response.put("totalPages", questionsPage.getTotalPages());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<Map<String, Object>> getQuestionsByUser(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<Question> questionsPage = questionService.getQuestionsByUser(userId, pageable);

        Map<String, Object> response = new HashMap<>();
        response.put("questions", questionsPage.getContent());
        response.put("currentPage", questionsPage.getNumber());
        response.put("totalItems", questionsPage.getTotalElements());
        response.put("totalPages", questionsPage.getTotalPages());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/matiere/{matiereId}")
    public ResponseEntity<Map<String, Object>> getQuestionsByMatiere(
            @PathVariable Long matiereId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<Question> questionsPage = questionService.getQuestionsByMatiere(matiereId, pageable);

        Map<String, Object> response = new HashMap<>();
        response.put("questions", questionsPage.getContent());
        response.put("currentPage", questionsPage.getNumber());
        response.put("totalItems", questionsPage.getTotalElements());
        response.put("totalPages", questionsPage.getTotalPages());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/search")
    public ResponseEntity<Map<String, Object>> searchQuestions(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<Question> questionsPage = questionService.searchQuestions(keyword, pageable);

        Map<String, Object> response = new HashMap<>();
        response.put("questions", questionsPage.getContent());
        response.put("currentPage", questionsPage.getNumber());
        response.put("totalItems", questionsPage.getTotalElements());
        response.put("totalPages", questionsPage.getTotalPages());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Question> getQuestionById(@PathVariable Long id) {
        try {
            Question question = questionService.getQuestionById(id);
            return ResponseEntity.ok(question);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT', 'ETUDIANT')")
    public ResponseEntity<Question> createQuestion(
            @RequestBody Question question,
            @RequestParam String userId, // Maintenant c'est un nom d'utilisateur, pas un ID
            @RequestParam(required = false) Long matiereId) {

        try {
            // Utiliser le nom d'utilisateur pour créer la question
            Question createdQuestion = questionService.createQuestion(question, userId, matiereId);
            return new ResponseEntity<>(createdQuestion, HttpStatus.CREATED);
        } catch (EntityNotFoundException e) {
            // Ajouter un log pour le débogage
            System.out.println("Erreur lors de la création de la question: " + e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            // Ajouter un log pour le débogage
            System.out.println("Erreur inattendue lors de la création de la question: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT', 'ETUDIANT')")
    public ResponseEntity<Question> updateQuestion(
            @PathVariable Long id,
            @RequestBody Question questionDetails) {

        try {
            Question updatedQuestion = questionService.updateQuestion(id, questionDetails);
            return ResponseEntity.ok(updatedQuestion);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT', 'ETUDIANT')")
    public ResponseEntity<Void> deleteQuestion(@PathVariable Long id) {
        try {
            questionService.deleteQuestion(id);
            return ResponseEntity.noContent().build();
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{questionId}/resolu/{reponseId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT', 'ETUDIANT')")
    public ResponseEntity<Question> marquerCommeResolu(
            @PathVariable Long questionId,
            @PathVariable Long reponseId) {

        try {
            Question updatedQuestion = questionService.marquerCommeResolu(questionId, reponseId);
            return ResponseEntity.ok(updatedQuestion);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/recent")
    public ResponseEntity<List<Question>> getRecentQuestions() {
        List<Question> recentQuestions = questionService.getRecentQuestions();
        return ResponseEntity.ok(recentQuestions);
    }

    @GetMapping("/popular")
    public ResponseEntity<List<Question>> getPopularQuestions() {
        List<Question> popularQuestions = questionService.getPopularQuestions();
        return ResponseEntity.ok(popularQuestions);
    }
}
