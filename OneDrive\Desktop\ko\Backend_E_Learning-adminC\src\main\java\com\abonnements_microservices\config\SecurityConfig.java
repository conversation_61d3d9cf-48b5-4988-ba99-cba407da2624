package com.abonnements_microservices.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {

    @Bean
    public JwtAuthenticationConverter jwtAuthenticationConverter() {
        JwtAuthenticationConverter converter = new JwtAuthenticationConverter();
        converter.setJwtGrantedAuthoritiesConverter(new KeycloakRoleConverter());
        return converter;
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(csrf -> csrf.disable())
            .sessionManagement(session ->
                session.sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            )            .authorizeHttpRequests(authorize -> authorize
                .requestMatchers("/api/auth/**", "/api/statistics/**").permitAll()
                .requestMatchers("/api/abonnements/all").permitAll()
                .requestMatchers("/api/niveaux/all").permitAll()
                .requestMatchers("/api/matieres").permitAll()
                .requestMatchers("/api/registration/**").permitAll() // Allow public access to registration endpoints
                .requestMatchers("/api/niveaux").permitAll() // Allow public access to education levels for registration form
                .requestMatchers("/api/abonnements/types").permitAll() // Allow public access to subscription types for registration form
                .requestMatchers("/api/abonnements/**").hasAnyRole("ADMIN", "ENSEIGNANT","ETUDIANT")


                .requestMatchers("/api/niveaux/add").hasAnyRole("ADMIN", "ENSEIGNANT","ETUDIANT")

                .requestMatchers("/api/imageAbonnement/**").permitAll()  // Allow public access to image endpoints
                .requestMatchers("/api/etudiants/**").hasAnyRole("ADMIN","ETUDIANT")
                .requestMatchers("/api/enseignants/**").hasAnyRole("ADMIN" ,"ENSEIGNANT")
                .requestMatchers("/api/matieres/**").hasAnyRole("ADMIN", "ENSEIGNANT", "ETUDIANT")

                .requestMatchers("/api/matieres/enseignant/**").hasAnyRole("ADMIN", "ENSEIGNANT")  // Allow enseignant access to their matières
                .requestMatchers("/api/chapitres/matiere/{matiereId}/**").hasAnyRole("ADMIN", "ENSEIGNANT","ETUDIANT")  // Allow enseignant access to chapitres for their matières
                .requestMatchers("/api/cours/chapitre/{chapitreId}/**").hasAnyRole("ADMIN", "ENSEIGNANT","ETUDIANT")  // Allow enseignant access to cours for their chapitres
                .requestMatchers("/api/chapitres/{id}").hasAnyRole("ADMIN", "ENSEIGNANT","ETUDIANT")  // Allow enseignant access to chapitres for their matières

                .requestMatchers("/api/cours/**").hasAnyRole("ADMIN", "ENSEIGNANT","ETUDIANT")  // Allow enseignant access to cours for their chapitres
                .requestMatchers("/api/etudiants/{id}/abonnements").hasAnyRole("ADMIN", "ETUDIANT") // <= Vérifiez le rôle exact
                .requestMatchers("/api/etudiants/{id}/abonnements-matieres").hasAnyRole("ADMIN", "ETUDIANT") // <= Vérifiez le rôle exact
                .requestMatchers("/api/etudiants/{id}/matieres").hasAnyRole("ADMIN", "ETUDIANT") // <= Vérifiez le rôle exact
                .requestMatchers("/api/image/load/{id}").hasAnyRole("ADMIN", "ENSEIGNANT" , "ETUDIANT")


                .requestMatchers("/api/niveaux/enseignant/**").hasAnyRole("ADMIN", "ENSEIGNANT")  // Allow enseignant access to their niveaux
                .requestMatchers("/api/image/**").authenticated()  // Allow authenticated users to access images
                .requestMatchers("/api/**").hasAnyRole("ADMIN","ENSEIGNANT","ETUDIANT")

                .requestMatchers("/api/livesessions/student/**").hasAnyRole("ADMIN", "ENSEIGNANT", "ETUDIANT")  // Allow students to access specific live sessions endpoints
                .requestMatchers("/api/livesessions/{id}/join").hasAnyRole("ADMIN", "ENSEIGNANT", "ETUDIANT")  // Allow students to join live sessions
                .requestMatchers("/api/livesessions/{id}").hasAnyRole("ADMIN", "ENSEIGNANT", "ETUDIANT")  // Allow students to view specific live session details

                .anyRequest().authenticated()
            )
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(jwt -> jwt
                    .jwtAuthenticationConverter(jwtAuthenticationConverter())
                )
            );
        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(Arrays.asList("http://localhost:3000", "http://localhost:3036"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization",
            "Content-Type",
            "Access-Control-Allow-Origin",
            "Access-Control-Allow-Headers",
            "Access-Control-Request-Method",
            "Access-Control-Request-Headers",
            "Origin",
            "Accept",
            "X-Requested-With"
        ));
        configuration.setExposedHeaders(Arrays.asList(
            "Access-Control-Allow-Origin",
            "Access-Control-Allow-Credentials"
        ));
        configuration.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}