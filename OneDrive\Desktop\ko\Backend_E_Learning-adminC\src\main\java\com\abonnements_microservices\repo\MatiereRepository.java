package com.abonnements_microservices.repo;

import com.abonnements_microservices.model.Abonnement;
import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.model.Niveau;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MatiereRepository extends JpaRepository<Matiere, Long> {
    List<Matiere> findByNomMatiereContainingIgnoreCase(String nomMatiere);
    List<Matiere> findByDuree(int duree);
    List<Matiere> findByAbonnements(Abonnement abonnement);  // Changer 'findByAbonnement' en 'findByAbonnements'

    // ✅ Requête pour récupérer toutes les matières avec abonnements, niveaux et chapitres
    @Query("SELECT DISTINCT m FROM Matiere m " +
           "LEFT JOIN FETCH m.abonnements a " +
           "LEFT JOIN FETCH m.matiereNiveaux mn " +
           "LEFT JOIN FETCH mn.niveau n " +
           "LEFT JOIN FETCH mn.chapitres c")
    List<Matiere> findAllWithAbonnementsAndNiveauxAndChapitres();

    // ✅ (Facultatif) Récupérer une matière par son id avec tout chargé
    @Query("SELECT m FROM Matiere m " +
           "LEFT JOIN FETCH m.abonnements a " +
           "LEFT JOIN FETCH m.matiereNiveaux mn " +
           "LEFT JOIN FETCH mn.niveau n " +
           "LEFT JOIN FETCH mn.chapitres c " +
           "WHERE m.idMatiere = :id")
    Matiere findByIdWithDetails(Long id);}

