import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosService";

const AjouterNiveau = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    niveau: "",
  });
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState("");

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [id]: value,
    }));
    setErrors((prevErrors) => ({
      ...prevErrors,
      [id]: "",
    }));
  };

  const validateForm = () => {
    let newErrors = {};
    if (!formData.niveau.trim())
      newErrors.niveau = "Le nom du niveau est requis";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await axiosInstance.post("/api/niveaux/add", {
        nom: formData.niveau
      });
      // Gestion du succès
      setSuccessMessage("Niveau ajouté avec succès !");
      setTimeout(() => {
        setSuccessMessage("");
        navigate("/niveaux-etude");
      }, 2000);
    } catch (error) {
      if (error.response?.status === 400) {
        setErrors({ submit: "Ce niveau existe déjà" });
      } else {
        setErrors({ submit: "Erreur serveur" });
      }
    }
  };
  return (
    <div className="container-fluid py-4">
      <div className="row justify-content-center page-titles mx-4 mb-4">
        <div className="col-sm-6 p-md-0">
          <div className="welcome-text">
            <h4 style={{ color: "#37A7DF", fontWeight: "600" }}>Ajouter un niveau</h4>
            <p className="mb-0" style={{ color: "#1D1D1B" }}>
              Remplissez le formulaire ci-dessous pour créer un nouveau niveau.
            </p>
          </div>
        </div>
      </div>

      <div className="row justify-content-center">
        <div className="col-xl-8 col-lg-10">
          <div
            className="card shadow-sm"
            style={{
              backgroundColor: "#EEF9F5",
              border: "1px solid #B7B7B7",
              borderRadius: "16px",
              color: "#1D1D1B",
            }}
          >
            <div className="card-body p-4">
              {successMessage && (
                <div className="alert alert-success rounded" style={{ fontWeight: "500" }}>
                  {successMessage}
                </div>
              )}
              {errors.submit && (
                <div className="alert alert-danger rounded" style={{ fontWeight: "500" }}>
                  {errors.submit}
                </div>
              )}
              <form onSubmit={handleSubmit}>
                <div className="form-group mb-3">
                  <label htmlFor="niveau" style={{ fontWeight: "500" }}>
                    Nom du niveau
                  </label>
                  <input
                    type="text"
                    className={`form-control ${errors.niveau ? "is-invalid" : ""}`}
                    id="niveau"
                    value={formData.niveau}
                    onChange={handleChange}
                    placeholder="Ex : Première Année"
                    style={{
                      borderRadius: "10px",
                      padding: "10px",
                      borderColor: errors.niveau ? "#D9534F" : "#B7B7B7",
                    }}
                  />
                  {errors.niveau && (
                    <div className="invalid-feedback">{errors.niveau}</div>
                  )}
                </div>

                <button
                  type="submit"
                  className="btn"
                  style={{
                    backgroundColor: "#37A7DF",
                    borderColor: "#37A7DF",
                    color: "#fff",
                    borderRadius: "10px",
                    padding: "10px 24px",
                    fontWeight: "500",
                    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.15)",
                    transition: "background-color 0.3s ease",
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.backgroundColor = "#2E8FCA";
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.backgroundColor = "#37A7DF";
                  }}
                >
                  Ajouter
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};


export default AjouterNiveau;