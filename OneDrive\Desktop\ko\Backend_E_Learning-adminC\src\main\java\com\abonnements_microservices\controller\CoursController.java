package com.abonnements_microservices.controller;

import com.abonnements_microservices.model.Cours;
import com.abonnements_microservices.repo.CoursRepository;
import com.abonnements_microservices.services.CoursService;
import com.abonnements_microservices.services.VimeoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/cours")
@CrossOrigin(origins = { "http://localhost:3036", "http://localhost:3000" })
public class CoursController {
	@Autowired
	private CoursService coursService;
	@Autowired
	private CoursRepository coursRepository;
	@Autowired
	private VimeoService vimeoService;

	@GetMapping
	public List<Cours> getAllCours() {
		return coursService.getAllCours();
	}

	@GetMapping("/page")
	@PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT','ETUDIANT')")

	public Page<Cours> getAllCours(@RequestParam(defaultValue = "0") int page,
			@RequestParam(defaultValue = "6") int size) {
		return coursService.getAllCoursParPage(page, size);
	}
	 @GetMapping("/search")
	    public List<Cours> getCoursByTitre(@RequestParam String titre) {
	        return coursService.getCoursByTitre(titre);
	    }

	@GetMapping("/{id}")
	public Optional<Cours> getCoursById(@PathVariable Long id) {
		return coursService.getCoursById(id);
	}

	@GetMapping("/chapitre/{chapitreId}")
	@PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT','ETUDIANT')")
	public List<Cours> getCoursByChapitre(@PathVariable Long chapitreId) {
		return coursService.getCoursByChapitre(chapitreId);
	}

	@PostMapping("/{chapitreId}")
	public Cours createCours(@RequestBody Cours cours, @PathVariable Long chapitreId) {
		return coursService.createCours(cours, chapitreId);
	}

	@PostMapping("/create")
	public ResponseEntity<Cours> createCours(@RequestBody Cours cours) {
		try {
			Cours newCours = coursRepository.save(cours);
			return new ResponseEntity<>(newCours, HttpStatus.CREATED);
		} catch (Exception e) {
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PutMapping("/{id}")
	public ResponseEntity<?> updateCours(@PathVariable Long id, @RequestBody Cours cours) {
		try {
			if (cours == null) {
				return ResponseEntity.badRequest().body("Le cours ne peut pas être null");
			}

			if (id == null || id <= 0) {
				return ResponseEntity.badRequest().body("ID du cours invalide");
			}

			// Validate required fields
			if (cours.getTitre() == null || cours.getTitre().trim().isEmpty()) {
				return ResponseEntity.badRequest().body("Le titre du cours est requis");
			}

			cours.setIdCours(id); // Ensure the ID is set correctly

			Cours updatedCours = coursService.updateCours(id, cours);
			return ResponseEntity.ok(updatedCours);
		} catch (RuntimeException e) {
			return ResponseEntity.badRequest().body(e.getMessage());
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
				.body("Erreur lors de la mise à jour du cours: " + e.getMessage());
		}
	}

	@DeleteMapping("/{id}")
	@PreAuthorize("hasAnyRole('ADMIN')")

	public void deleteCours(@PathVariable Long id) {
		coursService.deleteCours(id);
	}

	@PostMapping("/add")
	@PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT')")
	public ResponseEntity<Cours> addCours(@RequestBody Cours cours) {
		try {
			// Ensure the chapitre relationship is set properly
			if (cours.getChapitre() != null && cours.getChapitre().getId() != null) {
				// Log pour le débogage
				System.out.println("Ajout d'un cours avec chapitre ID: " + cours.getChapitre().getId());
				System.out.println("Titre du cours: " + cours.getTitre());
				System.out.println("Description du cours: " + cours.getDescription());
				System.out.println("Durée du cours: " + cours.getDuree());

				return new ResponseEntity<>(coursService.createCours(cours, cours.getChapitre().getId()),
						HttpStatus.CREATED);
			} else {
				System.out.println("Erreur: Chapitre non spécifié ou ID du chapitre manquant");
				return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
			}
		} catch (Exception e) {
			System.out.println("Erreur lors de l'ajout du cours: " + e.getMessage());
			e.printStackTrace();
			return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
		}
	}

	@GetMapping("/chapitre/{chapitreId}/count")
	@PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT','ETUDIANT')")

	public ResponseEntity<Map<String, Long>> getNombreCoursParChapitre(@PathVariable Long chapitreId) {
		long count = coursService.getNombreCoursParChapitre(chapitreId);
		Map<String, Long> response = new HashMap<>();
		response.put("count", count);
		return ResponseEntity.ok(response);
	}

	@PostMapping("/uploadVideo/{coursId}")
	@PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT')")
	public ResponseEntity<String> uploadVideo(@PathVariable Long coursId, @RequestParam("file") MultipartFile file) {
		try {
			String videoUrl = vimeoService.uploadVideo(file);
			coursService.updateVideoLink(coursId, videoUrl);
			return ResponseEntity.ok("Vidéo ajoutée au cours: " + videoUrl);
		} catch (Exception e) {
			return ResponseEntity.status(500).body("Erreur lors de l'upload: " + e.getMessage());
		}
	}
}
