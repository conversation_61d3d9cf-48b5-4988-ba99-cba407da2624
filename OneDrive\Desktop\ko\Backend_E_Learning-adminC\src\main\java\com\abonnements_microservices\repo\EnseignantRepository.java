package com.abonnements_microservices.repo;

import com.abonnements_microservices.model.Enseignant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface EnseignantRepository extends JpaRepository<Enseignant, Long> {
    boolean existsByUsername(String username);
    boolean existsByEmail(String email);
    Optional<Enseignant> findByUsername(String username);
    Optional<Enseignant> findByEmail(String email);
}