package com.abonnements_microservices.controller;

import com.abonnements_microservices.model.TextToSpeechHistory;
import com.abonnements_microservices.services.TextToSpeechService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/text-to-speech")
@CrossOrigin(origins = {"http://localhost:3036", "http://localhost:3000"})
@Slf4j
public class TextToSpeechController {

    @Autowired
    private TextToSpeechService textToSpeechService;

    @Value("${elevenlabs.api.key:***************************************************}")
    private String elevenLabsApiKey;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * Save a text-to-speech conversion to history
     */
    @PostMapping("/save-history")
    public ResponseEntity<?> saveToHistory(@RequestBody Map<String, Object> request) {
        try {
            String text = (String) request.get("text");
            String voice = (String) request.get("voice");
            Long userId = Long.valueOf(request.get("userId").toString());

            if (text == null || voice == null || userId == null) {
                return ResponseEntity.badRequest().body(Map.of("message", "Missing required fields"));
            }

            // Limit text length to prevent database issues
            if (text.length() > 1000) {
                text = text.substring(0, 997) + "...";
            }

            TextToSpeechHistory history = textToSpeechService.saveToHistory(text, voice, userId);
            return ResponseEntity.ok(history);
        } catch (Exception e) {
            log.error("Error saving text-to-speech history: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("message", "Failed to save history", "error", e.getMessage()));
        }
    }

    /**
     * Get text-to-speech history for a user
     */
    @GetMapping("/history/{userId}")
    public ResponseEntity<?> getHistoryForUser(@PathVariable Long userId) {
        try {
            List<TextToSpeechHistory> history = textToSpeechService.getHistoryForUser(userId);
            return ResponseEntity.ok(history);
        } catch (Exception e) {
            log.error("Error getting text-to-speech history: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("message", "Failed to get history", "error", e.getMessage()));
        }
    }

    /**
     * Proxy endpoint for text-to-speech conversion using ElevenLabs API
     */
    @PostMapping("/convert-proxy")
    public ResponseEntity<?> convertTextToSpeechProxy(@RequestBody Map<String, Object> request) {
        try {
            String text = (String) request.get("text");
            String voiceId = (String) request.get("voiceId");
            Long userId = Long.valueOf(request.get("userId").toString());

            if (text == null || voiceId == null) {
                return ResponseEntity.badRequest().body(Map.of("message", "Missing required fields"));
            }

            log.info("Converting text to speech via ElevenLabs proxy: voiceId={}, text length={}",
                voiceId, text.length());

            // Ensure text is properly encoded
            String encodedText = text;
            if (encodedText.length() > 5000) {
                // Limit text length for API call
                encodedText = encodedText.substring(0, 5000);
                log.info("Text truncated to 5000 characters for API call");
            }

            // Build the URL for ElevenLabs API
            String url = "https://api.elevenlabs.io/v1/text-to-speech/" + voiceId;

            // Prepare the request body for ElevenLabs
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("text", encodedText);

            // Use multilingual model for language support
            Map<String, Object> modelSettings = new HashMap<>();
            modelSettings.put("stability", 0.5);
            modelSettings.put("similarity_boost", 0.5);
            requestBody.put("model_id", "eleven_multilingual_v2");
            requestBody.put("voice_settings", modelSettings);

            // Set up headers with API key
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("xi-api-key", elevenLabsApiKey);
            headers.setAccept(List.of(MediaType.APPLICATION_OCTET_STREAM));

            // Create the HTTP entity with the request body
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // Make the request to ElevenLabs API
            ResponseEntity<byte[]> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                byte[].class
            );

            // Check if the response is successful and contains valid audio data
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                // Check if the response might be an error message instead of audio
                MediaType contentType = response.getHeaders().getContentType();
                if (contentType != null && contentType.includes(MediaType.APPLICATION_JSON)) {
                    // Convert the response body to a string to check for error messages
                    String responseText = new String(response.getBody(), java.nio.charset.StandardCharsets.UTF_8);

                    // Check for specific error messages
                    if (responseText.contains("error") || responseText.contains("invalid")) {
                        log.error("ElevenLabs API returned an error: {}", responseText);
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body("ElevenLabs API error: " + responseText);
                    }
                }

                // If we got here, it's probably valid audio data
                // Limit text length to prevent database issues
                String textToSave = text;
                if (textToSave.length() > 1000) {
                    textToSave = textToSave.substring(0, 997) + "...";
                }

                textToSpeechService.saveToHistory(textToSave, "ElevenLabs-" + voiceId, userId);

                // Return the audio data
                HttpHeaders responseHeaders = new HttpHeaders();
                responseHeaders.setContentType(MediaType.parseMediaType("audio/mpeg"));
                responseHeaders.setContentLength(response.getBody().length);

                return new ResponseEntity<>(response.getBody(), responseHeaders, HttpStatus.OK);
            } else {
                log.error("ElevenLabs API returned non-success status: {}", response.getStatusCode());
                return ResponseEntity.status(response.getStatusCode())
                    .body("Failed to convert text to speech");
            }
        } catch (Exception e) {
            log.error("Error in text-to-speech proxy: {}", e.getMessage(), e);

            // Check for specific error types
            String errorMessage = e.getMessage();
            if (errorMessage != null) {
                if (errorMessage.contains("voice_not_found")) {
                    return ResponseEntity.badRequest()
                        .body(Map.of(
                            "message", "La voix sélectionnée n'a pas été trouvée. Veuillez sélectionner une autre voix.",
                            "error", "Voice ID not found in ElevenLabs"
                        ));
                } else if (errorMessage.contains("invalid_api_key") || errorMessage.contains("unauthorized")) {
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(Map.of(
                            "message", "Clé API ElevenLabs invalide ou expirée. Veuillez contacter l'administrateur.",
                            "error", "Invalid API key"
                        ));
                }
            }

            // Generic error response
            return ResponseEntity.internalServerError()
                .body(Map.of("message", "Failed to convert text to speech", "error", e.getMessage()));
        }
    }
}
