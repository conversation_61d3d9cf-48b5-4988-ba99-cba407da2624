package com.abonnements_microservices.dto;

import com.abonnements_microservices.model.ImageAbonnement;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImageAbonnementDTO {
    private Long idImage;
    private String name;
    private String type;
    private byte[] image;

    // Constructeur à partir de l'entité
    public ImageAbonnementDTO(ImageAbonnement imageAbonnement) {
        this.idImage = imageAbonnement.getIdImage();
        this.name = imageAbonnement.getName();
        this.type = imageAbonnement.getType();
        this.image = imageAbonnement.getImage();
    }

    // Conversion DTO → Entity (optionnel)
    public ImageAbonnement toEntity() {
        return ImageAbonnement.builder()
                .idImage(this.idImage)
                .name(this.name)
                .type(this.type)
                .image(this.image)
                .build();
    }
}
