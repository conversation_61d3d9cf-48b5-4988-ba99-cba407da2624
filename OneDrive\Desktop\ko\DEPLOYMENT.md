# 🚀 E-Learning Platform Deployment Guide

## 📦 Docker Hub Images

Your application is now available on Docker Hub:

- **Backend**: `oussemanassraoui/elearning-backend:latest` (340MB)
- **Frontend User**: `oussemanassraoui/elearning-frontend:latest` (69.2MB)
- **Frontend Vitrine**: `oussemanassraoui/elearning-vitrine:latest` (65MB)

## 🌐 Quick Deployment

### Option 1: Using Docker Compose (Recommended)

```bash
# Clone or download the docker-compose.yml file
curl -O https://raw.githubusercontent.com/your-repo/docker-compose.yml

# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

### Option 2: Manual Container Deployment

```bash
# Create network
docker network create elearning-network

# Start MySQL
docker run -d --name mysql \
  --network elearning-network \
  -e MYSQL_ROOT_PASSWORD=rootpassword \
  -e MYSQL_DATABASE=elearning_db \
  -e MYSQL_USER=elearning_user \
  -e MYSQL_PASSWORD=password123 \
  -p 3306:3306 \
  mysql:8.0

# Start Keycloak
docker run -d --name keycloak \
  --network elearning-network \
  -e KEYCLOAK_ADMIN=admin \
  -e KEYCLOAK_ADMIN_PASSWORD=admin123 \
  -p 8080:8080 \
  quay.io/keycloak/keycloak:latest start-dev

# Start Backend
docker run -d --name backend \
  --network elearning-network \
  -e SPRING_PROFILES_ACTIVE=docker \
  -p 8081:8081 \
  oussemanassraoui/elearning-backend:latest

# Start Frontend User
docker run -d --name frontend-user \
  --network elearning-network \
  -p 3000:80 \
  oussemanassraoui/elearning-frontend:latest

# Start Frontend Vitrine
docker run -d --name frontend-vitrine \
  --network elearning-network \
  -p 3001:80 \
  oussemanassraoui/elearning-vitrine:latest
```

## 🔗 Access URLs

After deployment, access your application at:

- **User Frontend**: http://localhost:3000
- **Vitrine Frontend**: http://localhost:3001
- **Backend API**: http://localhost:8081
- **Keycloak Admin**: http://localhost:8080
- **MySQL**: localhost:3306

## 🛠️ Development vs Production

### Development Mode
Uncomment the `build` sections in docker-compose.yml for local development:

```yaml
backend:
  build:
    context: ./Backend_E_Learning-adminC
    dockerfile: Dockerfile
  # image: oussemanassraoui/elearning-backend:latest
```

### Production Mode
Use the Docker Hub images (current configuration):

```yaml
backend:
  image: oussemanassraoui/elearning-backend:latest
```

## 📊 Monitoring & Health Checks

```bash
# Check container health
docker ps
docker stats

# Check application health
curl http://localhost:8081/actuator/health
curl http://localhost:3000
curl http://localhost:3001

# View logs
docker-compose logs backend
docker-compose logs frontend-user
docker-compose logs frontend-vitrine
```

## 🔄 Updates & Maintenance

### Updating Images

```bash
# Pull latest images
docker pull oussemanassraoui/elearning-backend:latest
docker pull oussemanassraoui/elearning-frontend:latest
docker pull oussemanassraoui/elearning-vitrine:latest

# Restart services
docker-compose down
docker-compose up -d
```

### Backup Database

```bash
# Backup MySQL data
docker exec mysql mysqldump -u elearning_user -p elearning_db > backup.sql

# Restore from backup
docker exec -i mysql mysql -u elearning_user -p elearning_db < backup.sql
```

## 🔧 Troubleshooting

### Common Issues

1. **Port conflicts**: Change ports in docker-compose.yml
2. **Database connection**: Ensure MySQL is running and accessible
3. **Keycloak setup**: Configure realm and client settings
4. **Network issues**: Check Docker network configuration

### Debug Commands

```bash
# Check container logs
docker logs container-name

# Access container shell
docker exec -it container-name /bin/bash

# Check network connectivity
docker network inspect elearning-network
```

## 🌍 Cloud Deployment

### AWS ECS
```bash
# Create task definitions using the Docker Hub images
# Configure load balancers and security groups
```

### Google Cloud Run
```bash
gcloud run deploy elearning-backend \
  --image=oussemanassraoui/elearning-backend:latest \
  --platform=managed
```

### Azure Container Instances
```bash
az container create \
  --resource-group myResourceGroup \
  --name elearning-backend \
  --image oussemanassraoui/elearning-backend:latest
```

## 📝 Environment Variables

Key environment variables to configure:

- `SPRING_PROFILES_ACTIVE`: Set to `docker` for containerized deployment
- `MYSQL_ROOT_PASSWORD`: MySQL root password
- `KEYCLOAK_ADMIN_PASSWORD`: Keycloak admin password
- `REACT_APP_API_URL`: Backend API URL for frontend

## 🔐 Security Considerations

- Change default passwords in production
- Use secrets management for sensitive data
- Configure HTTPS/TLS certificates
- Set up proper firewall rules
- Regular security updates

---

**🎉 Your E-Learning platform is now ready for deployment anywhere Docker runs!**
