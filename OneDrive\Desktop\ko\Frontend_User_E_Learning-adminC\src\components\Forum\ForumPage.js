import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useKeycloak } from '@react-keycloak/web';
import axiosInstance from '../../services/axiosService';

const ForumPage = () => {
  const { keycloak } = useKeycloak();
  const navigate = useNavigate();
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [matieres, setMatieres] = useState([]);
  const [selectedMatiere, setSelectedMatiere] = useState('');
  const [recentQuestions, setRecentQuestions] = useState([]);
  const [popularQuestions, setPopularQuestions] = useState([]);

  useEffect(() => {
    const fetchMatieres = async () => {
      try {
        // Utiliser l'endpoint correct pour récupérer toutes les matières
        const response = await axiosInstance.get('/api/matieres');
        setMatieres(response.data);
      } catch (err) {
        console.error('Erreur lors du chargement des matières:', err);
      }
    };

    const fetchRecentQuestions = async () => {
      try {
        const response = await axiosInstance.get('/api/forum/questions/recent');
        setRecentQuestions(response.data);
      } catch (err) {
        console.error('Erreur lors du chargement des questions récentes:', err);
      }
    };

    const fetchPopularQuestions = async () => {
      try {
        const response = await axiosInstance.get('/api/forum/questions/popular');
        setPopularQuestions(response.data);
      } catch (err) {
        console.error('Erreur lors du chargement des questions populaires:', err);
      }
    };

    fetchMatieres();
    fetchRecentQuestions();
    fetchPopularQuestions();
  }, []);

  useEffect(() => {
    const fetchQuestions = async () => {
      setLoading(true);
      try {
        let url = `/api/forum/questions?page=${currentPage}&size=10`;

        if (searchTerm) {
          url = `/api/forum/questions/search?keyword=${searchTerm}&page=${currentPage}&size=10`;
        } else if (selectedMatiere) {
          url = `/api/forum/questions/matiere/${selectedMatiere}?page=${currentPage}&size=10`;
        }

        const response = await axiosInstance.get(url);
        setQuestions(response.data.questions);
        setTotalPages(response.data.totalPages);
        setError(null);
      } catch (err) {
        console.error('Erreur lors du chargement des questions:', err);
        setError('Erreur lors du chargement des questions. Veuillez réessayer plus tard.');
      } finally {
        setLoading(false);
      }
    };

    fetchQuestions();
  }, [currentPage, searchTerm, selectedMatiere]);

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(0);
  };

  const handleMatiereChange = (e) => {
    setSelectedMatiere(e.target.value);
    setCurrentPage(0);
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Date inconnue';
    }
  };

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0">
        <div className="col-sm-6 p-md-0">
          <div className="welcome-text">
            <h4 style={{ color: "#37A7DF" }}>Forum de Discussion</h4>
            <p className="mb-0">Posez vos questions et obtenez des réponses de la communauté</p>
          </div>
        </div>
        <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex">
          <ol className="breadcrumb">
            <li className="breadcrumb-item"><Link to="/dashboard">Accueil</Link></li>
            <li className="breadcrumb-item active"><Link to="/forum">Forum</Link></li>
          </ol>
        </div>
      </div>

      <div className="row">
        <div className="col-lg-8">
          <div className="card">
            <div className="card-header">
              <h4 className="card-title">Questions</h4>
              <div className="d-flex">
                <Link to="/forum/poser-question" className="btn btn-primary ml-auto" style={{ backgroundColor: "#37A7DF", borderColor: "#37A7DF" }}>
                  Poser une question
                </Link>
              </div>
            </div>
            <div className="card-body">
              <div className="row mb-4">
                <div className="col-md-6">
                  <form onSubmit={handleSearch}>
                    <div className="input-group">
                      <input
                        type="text"
                        className="form-control"
                        placeholder="Rechercher une question..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      <div className="input-group-append">
                        <button className="btn btn-primary" type="submit" style={{ backgroundColor: "#37A7DF", borderColor: "#37A7DF" }}>
                          <i className="fa fa-search"></i>
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
                <div className="col-md-6">
                  <select
                    className="form-control"
                    value={selectedMatiere}
                    onChange={handleMatiereChange}
                  >
                    <option value="">Toutes les matières</option>
                    {matieres.map((matiere) => (
                      <option key={matiere.id} value={matiere.id}>
                        {matiere.nomMatiere}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {loading ? (
                <div className="text-center">
                  <div className="spinner-border text-primary" role="status">
                    <span className="sr-only">Chargement...</span>
                  </div>
                </div>
              ) : error ? (
                <div className="alert alert-danger">{error}</div>
              ) : questions.length === 0 ? (
                <div className="text-center">
                  <p>Aucune question trouvée.</p>
                </div>
              ) : (
                <div>
                  {questions.map((question) => (
                    <div key={question.id} className="card mb-3 border-0 shadow-sm">
                      <div className="card-body">
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <div>
                            <span className="badge badge-primary mr-2" style={{ backgroundColor: "#37A7DF" }}>
                              {question.matiere ? question.matiere.nomMatiere : 'Général'}
                            </span>
                            {question.resolu && (
                              <span className="badge badge-success">Résolu</span>
                            )}
                          </div>
                          <small className="text-muted">{formatDate(question.dateCreation)}</small>
                        </div>
                        <h5 className="card-title">
                          <Link to={`/forum/question/${question.id}`} style={{ color: "#37A7DF" }}>
                            {question.titre}
                          </Link>
                        </h5>
                        <p className="card-text text-truncate">{question.contenu}</p>
                        <div className="d-flex justify-content-between align-items-center">
                          <div>
                            <small className="text-muted">
                              Posée par {question.auteur.firstName} {question.auteur.lastName}
                            </small>
                          </div>
                          <div>
                            <span className="mr-3">
                              <i className="fa fa-eye mr-1"></i> {question.vues}
                            </span>
                            <span>
                              <i className="fa fa-comment mr-1"></i> {question.reponses ? question.reponses.length : 0}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Pagination */}
                  <div className="d-flex justify-content-center mt-4">
                    <nav>
                      <ul className="pagination">
                        <li className={`page-item ${currentPage === 0 ? 'disabled' : ''}`}>
                          <button
                            className="page-link"
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 0}
                          >
                            Précédent
                          </button>
                        </li>
                        {[...Array(totalPages).keys()].map((page) => (
                          <li key={page} className={`page-item ${currentPage === page ? 'active' : ''}`}>
                            <button
                              className="page-link"
                              onClick={() => handlePageChange(page)}
                              style={currentPage === page ? { backgroundColor: "#37A7DF", borderColor: "#37A7DF" } : {}}
                            >
                              {page + 1}
                            </button>
                          </li>
                        ))}
                        <li className={`page-item ${currentPage === totalPages - 1 ? 'disabled' : ''}`}>
                          <button
                            className="page-link"
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage === totalPages - 1}
                          >
                            Suivant
                          </button>
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="col-lg-4">
          {/* Questions récentes */}
          <div className="card mb-4">
            <div className="card-header">
              <h4 className="card-title">Questions récentes</h4>
            </div>
            <div className="card-body">
              {recentQuestions.length === 0 ? (
                <p>Aucune question récente.</p>
              ) : (
                <ul className="list-group list-group-flush">
                  {recentQuestions.map((question) => (
                    <li key={question.id} className="list-group-item px-0">
                      <Link to={`/forum/question/${question.id}`} style={{ color: "#37A7DF" }}>
                        {question.titre}
                      </Link>
                      <div className="d-flex justify-content-between mt-1">
                        <small className="text-muted">
                          {formatDate(question.dateCreation)}
                        </small>
                        <small className="text-muted">
                          {question.reponses ? question.reponses.length : 0} réponses
                        </small>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>

          {/* Questions populaires */}
          <div className="card">
            <div className="card-header">
              <h4 className="card-title">Questions populaires</h4>
            </div>
            <div className="card-body">
              {popularQuestions.length === 0 ? (
                <p>Aucune question populaire.</p>
              ) : (
                <ul className="list-group list-group-flush">
                  {popularQuestions.map((question) => (
                    <li key={question.id} className="list-group-item px-0">
                      <Link to={`/forum/question/${question.id}`} style={{ color: "#37A7DF" }}>
                        {question.titre}
                      </Link>
                      <div className="d-flex justify-content-between mt-1">
                        <small className="text-muted">
                          <i className="fa fa-eye mr-1"></i> {question.vues} vues
                        </small>
                        <small className="text-muted">
                          {question.reponses ? question.reponses.length : 0} réponses
                        </small>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForumPage;
