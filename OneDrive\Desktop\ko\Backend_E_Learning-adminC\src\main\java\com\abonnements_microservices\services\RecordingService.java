package com.abonnements_microservices.services;

import com.abonnements_microservices.model.*;
import com.abonnements_microservices.repo.EnseignantRepository;
import com.abonnements_microservices.repo.MatiereRepository;
import com.abonnements_microservices.repo.NiveauRepository;
import com.abonnements_microservices.repo.RecordingRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

@Service
public class RecordingService {

    @Autowired
    private RecordingRepository recordingRepository;

    @Autowired
    private EnseignantRepository enseignantRepository;

    @Autowired
    private MatiereRepository matiereRepository;

    @Autowired
    private NiveauRepository niveauRepository;

    @Autowired
    private VimeoService vimeoService;

    /**
     * Upload and store a recording for a matière and niveau
     */
    @Transactional
    public Recording uploadRecording(Long matiereId, Long niveauId, Long enseignantId,
                                  String title, String description,
                                  MultipartFile recordingFile) throws IOException {
        // Verify the matière exists
        Matiere matiere = matiereRepository.findById(matiereId)
            .orElseThrow(() -> new EntityNotFoundException("Matiere not found with id: " + matiereId));

        // Verify the niveau exists
        Niveau niveau = niveauRepository.findById(niveauId)
            .orElseThrow(() -> new EntityNotFoundException("Niveau not found with id: " + niveauId));

        // Verify the association between matiere and niveau exists
        // This check should be implemented based on your data model

        // Verify the enseignant exists and get a fallback if needed
        Enseignant enseignant = getEnseignantForRecording(enseignantId, matiereId);

        // Create a descriptive title for the video
        String videoTitle = title + " - " + matiere.getNomMatiere() + " - " + niveau.getNom() +
                          " (" + LocalDateTime.now().toLocalDate() + ")";

        // Upload to Vimeo
        String videoUrl = vimeoService.uploadVideo(recordingFile, videoTitle);

        // Create and save the recording
        Recording recording = new Recording();
        recording.setTitle(title);
        recording.setDescription(description);
        recording.setVideoUrl(videoUrl);
        recording.setRecordingDate(LocalDateTime.now());
        recording.setMatiere(matiere);
        recording.setNiveau(niveau);
        recording.setEnseignant(enseignant);

        return recordingRepository.save(recording);
    }

    /**
     * Get recordings for a specific matière
     */
    public List<Recording> getRecordingsByMatiere(Long matiereId) {
        return recordingRepository.findByMatiereIdMatiere(matiereId);
    }
      /**
     * Get recordings for a specific matière and niveau
     */
    public List<Recording> getRecordingsByMatiereAndNiveau(Long matiereId, Long niveauId) {
        return recordingRepository.findByMatiere_IdMatiereAndNiveau_Id(matiereId, niveauId);
    }

    /**
     * Get recordings by enseignant
     */
    public List<Recording> getRecordingsByEnseignant(Long enseignantId) {
        return recordingRepository.findByEnseignantId(enseignantId);
    }

    /**
     * Get recordings by niveau
     */
    public List<Recording> getRecordingsByNiveau(Long niveauId) {
        return recordingRepository.findByNiveauId(niveauId);
    }

    /**
     * Helper method to get an appropriate enseignant for a recording
     */
    private Enseignant getEnseignantForRecording(Long enseignantId, Long matiereId) {
        // If an enseignant ID is provided, use that
        if (enseignantId != null) {
            return enseignantRepository.findById(enseignantId)
                .orElseThrow(() -> new EntityNotFoundException("Enseignant not found with id: " + enseignantId));
        }

        // Otherwise, try to find an enseignant associated with this matière
        // This logic would need to be adjusted based on your data model
        List<Enseignant> allEnseignants = enseignantRepository.findAll();
        if (!allEnseignants.isEmpty()) {
            return allEnseignants.get(0);
        }

        throw new IllegalStateException("Cannot save recording: No teachers available in the system");
    }
}
