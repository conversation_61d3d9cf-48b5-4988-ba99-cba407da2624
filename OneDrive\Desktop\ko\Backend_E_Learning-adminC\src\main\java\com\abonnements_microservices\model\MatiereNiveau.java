package com.abonnements_microservices.model;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import jakarta.persistence.CascadeType;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.OneToMany;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
@Builder
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
public class MatiereNiveau {

    @EmbeddedId
    private MatiereNiveauId id = new MatiereNiveauId();

    //@ManyToOne(fetch = FetchType.LAZY)
    @ManyToOne
    @MapsId("matiereId") // correspond au champ dans la clé composite
    @JoinColumn(name = "matiereId")
	//@JsonIgnoreProperties("matiereNiveau")
    @JsonBackReference("matiereNiveau")


    private Matiere matiere;

  /*  @ManyToOne(fetch = FetchType.LAZY)
    @MapsId("niveauId") // correspond au champ dans la clé composite
    @JoinColumn(name = "niveauId")
	//@JsonIgnoreProperties("matiereNiveau")
    @JsonBackReference
    private Niveau niveau;
    */
    @ManyToOne
    @MapsId("niveauId") // correspond au champ dans la clé composite
    @JoinColumn(name = "niveauId")
    @JsonBackReference("niveau-matiere")
    private Niveau niveau;
	@OneToMany(mappedBy = "matiereNiveau", cascade = CascadeType.ALL, orphanRemoval = true)
	@JsonManagedReference("matiere-niveau-chapitre")

	private Set<Chapitre> chapitres = new HashSet<>();

	public void addChapitre(Chapitre chapitre) {
		chapitres.add(chapitre);
		chapitre.setMatiereNiveau(this);
	}

	public void removeChapitre(Chapitre chapitre) {
		chapitres.remove(chapitre);
		chapitre.setMatiereNiveau(null);
	}
}