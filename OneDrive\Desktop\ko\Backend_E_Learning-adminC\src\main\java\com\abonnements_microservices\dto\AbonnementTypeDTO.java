package com.abonnements_microservices.dto;

import com.abonnements_microservices.model.AbonnementType;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AbonnementTypeDTO {
    private Long id;

    @NotBlank(message = "Le nom est obligatoire")
    private String nom;

    private String description;
    
    private boolean hasCourses = true;
    private boolean hasRecordings = false;
    private boolean hasLiveSessions = false;
    
    // Convert DTO to Entity
    public AbonnementType toEntity() {
        return AbonnementType.builder()
                .id(this.id)
                .nom(this.nom)
                .description(this.description)
                .hasCourses(this.hasCourses)
                .hasRecordings(this.hasRecordings)
                .hasLiveSessions(this.hasLiveSessions)
                .build();
    }
    
    // Convert Entity to DTO
    public static AbonnementTypeDTO fromEntity(AbonnementType type) {
        if (type == null) return null;
        
        AbonnementTypeDTO dto = new AbonnementTypeDTO();
        dto.setId(type.getId());
        dto.setNom(type.getNom());
        dto.setDescription(type.getDescription());
        dto.setHasCourses(type.isHasCourses());
        dto.setHasRecordings(type.isHasRecordings());
        dto.setHasLiveSessions(type.isHasLiveSessions());
        
        return dto;
    }
}
