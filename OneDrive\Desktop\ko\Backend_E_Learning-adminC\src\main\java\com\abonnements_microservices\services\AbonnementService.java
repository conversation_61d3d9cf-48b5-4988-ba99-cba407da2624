package com.abonnements_microservices.services;

import com.abonnements_microservices.dto.AbonnementDTO;
import com.abonnements_microservices.model.Abonnement;
import com.abonnements_microservices.model.AbonnementType;
import com.abonnements_microservices.model.Etudiant;
import com.abonnements_microservices.model.ImageAbonnement;
import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.repo.AbonnementRepository;
import com.abonnements_microservices.repo.AbonnementTypeRepository;
import com.abonnements_microservices.repo.ImageAbonnementRepository;
import com.abonnements_microservices.repo.MatiereRepository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.PersistenceContext;

import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class AbonnementService {
    @Autowired
    private AbonnementRepository abonnementRepository;

    @Autowired
    private ImageAbonnementRepository imageAbonnementRepository;

    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private MatiereRepository matiereRepository;

    @Autowired
    private AbonnementTypeRepository abonnementTypeRepository;

    @Transactional(readOnly = true)
    public List<Abonnement> getAllAbonnements() {
        List<Abonnement> abonnements = abonnementRepository.findAll();
        // Force initialization of collections and relationships
        abonnements.forEach(abonnement -> {
            Hibernate.initialize(abonnement.getMatieres());
            if (abonnement.getImageAbonnement() != null) {
                Hibernate.initialize(abonnement.getImageAbonnement());
            }
        });
        return abonnements;
    }

    @Transactional(readOnly = true)
    public Abonnement getAbonnementById(Long id) {
        return abonnementRepository.findById(id)
            .map(abonnement -> {
                Hibernate.initialize(abonnement.getMatieres());
                if (abonnement.getImageAbonnement() != null) {
                    Hibernate.initialize(abonnement.getImageAbonnement());
                }
                return abonnement;
            })
            .orElseThrow(() -> new EntityNotFoundException("Abonnement non trouvé avec l'id " + id));
    }

    public List<Abonnement> getAbonnementsByNom(String nom) {
        List<Abonnement> abonnements = abonnementRepository.findByNomContainingIgnoreCase(nom);
        abonnements.forEach(abonnement -> {
            Hibernate.initialize(abonnement.getMatieres());
            if (abonnement.getImageAbonnement() != null) {
                Hibernate.initialize(abonnement.getImageAbonnement());
            }
        });
        return abonnements;
    }

    @Transactional
    public Abonnement createAbonnement(AbonnementDTO dto, MultipartFile imageFile) throws IOException {
        // 1. Gestion de l'image
        ImageAbonnement image = null;
        if (imageFile != null) {
            image = new ImageAbonnement();
            image.setName(imageFile.getOriginalFilename());
            image.setType(imageFile.getContentType());
            image.setImage(imageFile.getBytes());
            image = imageAbonnementRepository.save(image);
        } else if (dto.getId() != null) {
            image = imageAbonnementRepository.findById(dto.getId())
                .orElseThrow(() -> new EntityNotFoundException("Image non trouvée"));
        }

        // 2. Conversion DTO -> Entity
        Abonnement abonnement = new Abonnement();
        abonnement.setNom(dto.getNom());
        abonnement.setDescription(dto.getDescription());
        abonnement.setPrix(dto.getPrix());
        abonnement.setDuree(dto.getDuree());
        abonnement.setImageAbonnement(image);

        // 3. Gestion du type d'abonnement
        if (dto.getTypeId() != null) {
            AbonnementType type = abonnementTypeRepository.findById(dto.getTypeId())
                .orElseThrow(() -> new EntityNotFoundException("Type d'abonnement non trouvé avec l'id: " + dto.getTypeId()));
            abonnement.setType(type);
        }

        // 4. Sauvegarde
        return abonnementRepository.save(abonnement);
    }





    public Abonnement updateAbonnement(Long id, AbonnementDTO dto) {
        return abonnementRepository.findById(id)
            .map(abonnement -> {
                abonnement.setNom(dto.getNom());
                abonnement.setDescription(dto.getDescription());
                abonnement.setPrix(dto.getPrix());
                abonnement.setDuree(dto.getDuree());

                // Gestion de l'image, si présente
                if (dto.getImageAbonnement() != null) {
                	abonnement.setImageAbonnement(dto.getImageAbonnement().toEntity());
                }

                // Gestion du type d'abonnement
                if (dto.getTypeId() != null) {
                    AbonnementType type = abonnementTypeRepository.findById(dto.getTypeId())
                        .orElseThrow(() -> new EntityNotFoundException("Type d'abonnement non trouvé avec l'id: " + dto.getTypeId()));
                    abonnement.setType(type);
                }

                // Gestion des matières
                if (dto.getMatiereIds() != null && !dto.getMatiereIds().isEmpty()) {
                    Set<Matiere> matieres = dto.getMatiereIds().stream()
                        .map(idMatiere -> matiereRepository.findById(idMatiere)
                            .orElseThrow(() -> new RuntimeException("Matière non trouvée: " + idMatiere)))
                        .collect(Collectors.toSet());

                    // On vide les anciennes matières et on ajoute les nouvelles
                    abonnement.getMatieres().clear();
                    matieres.forEach(abonnement::addMatiere);
                }

                return abonnementRepository.save(abonnement);
            })
            .orElseThrow(() -> new RuntimeException("Abonnement non trouvé"));
    }





    @Transactional
    public void deleteAbonnement(Long id) {
        Abonnement abonnement = entityManager.find(Abonnement.class, id);
        if (abonnement == null) {
            throw new EntityNotFoundException("Abonnement non trouvé avec l'id: " + id);
        }

        // 1. Dissocier les relations avec les étudiants
        Set<Etudiant> etudiants = new HashSet<>(abonnement.getEtudiants());
        for (Etudiant etudiant : etudiants) {
            etudiant.getAbonnements().remove(abonnement); // Dissocier la relation côté Etudiant
            abonnement.getEtudiants().remove(etudiant); // Retirer de la collection
            entityManager.merge(etudiant); // Sauvegarder les changements pour l'étudiant
        }

        // 2. Dissocier les relations avec les matières
        Set<Matiere> matieres = new HashSet<>(abonnement.getMatieres());
        for (Matiere matiere : matieres) {
            matiere.getAbonnements().remove(abonnement); // Dissocier la relation côté Matiere
            abonnement.getMatieres().remove(matiere); // Retirer de la collection
            entityManager.merge(matiere); // Sauvegarder les changements pour la matière
        }

        // 3. Gestion de l'image si nécessaire
        if (abonnement.getImageAbonnement() != null) {
            ImageAbonnement image = abonnement.getImageAbonnement();
            abonnement.setImageAbonnement(null);
            entityManager.remove(image); // Supprimer l'image si elle existe
        }

        // 4. Supprimer l'abonnement
        entityManager.flush(); // S'assurer que toutes les modifications sont appliquées
        entityManager.remove(abonnement);
    }


 public long countAbonnements() {
        return abonnementRepository.count();
    }

    @Transactional(readOnly = true)
    public Page<Abonnement> getAllAbonnementParPage(int page, int size) {
        Page<Abonnement> abonnementPage = abonnementRepository.findAll(PageRequest.of(page, size));
        // Force initialization of all lazy relationships within the transaction
        for (Abonnement abonnement : abonnementPage.getContent()) {
            // Initialize matieres collection
            if (abonnement.getMatieres() != null) {
                abonnement.getMatieres().size();
                for (Matiere matiere : abonnement.getMatieres()) {
                    if (matiere.getImage() != null) {
                        Hibernate.initialize(matiere.getImage());
                    }
                }
            }
            // Initialize image
            if (abonnement.getImageAbonnement() != null) {
                Hibernate.initialize(abonnement.getImageAbonnement());
            }
        }
        return abonnementPage;
    }
    public List<Abonnement> getAbonnementsByEtudiantId(Long etudiantId) {
        return abonnementRepository.findByEtudiants_Id(etudiantId);
    }
    public Abonnement saveAbonnement(Abonnement abonnement) {
        return abonnementRepository.save(abonnement);
    }
}