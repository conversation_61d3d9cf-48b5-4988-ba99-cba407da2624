import React, { createContext, useContext, useEffect, useState } from "react";
import Keycloak from "keycloak-js";

const KeycloakContext = createContext();

export const KeycloakProvider = ({ children }) => {
  const [keycloak, setKeycloak] = useState(null);
  const [authenticated, setAuthenticated] = useState(false);
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    const keycloakInstance = new Keycloak({
      url: "http://localhost:8080", // Assure-toi que Keycloak tourne sur cette URL
      realm: "e-learning-realm", // Vérifie le nom exact du realm dans Keycloak
      clientId: "e-learning", // Vérifie que le client existe dans Keycloak
    });

    keycloakInstance.init({ onLoad: "login-required" })
      .then(auth => {
        setKeycloak(keycloakInstance);
        setAuthenticated(auth);
        setInitialized(true);
      })
      .catch(err => {
        console.error("Keycloak initialization failed:", err);
        setInitialized(true); // Éviter un blocage infini
      });
  }, []);

  return (
    <KeycloakContext.Provider value={{ keycloak, authenticated, initialized }}>
      {children}
    </KeycloakContext.Provider>
  );
};

export const useAuth = () => useContext(KeycloakContext);
