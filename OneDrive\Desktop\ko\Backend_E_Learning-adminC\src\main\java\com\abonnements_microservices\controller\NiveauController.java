package com.abonnements_microservices.controller;

import com.abonnements_microservices.dto.NiveauDTO;
import com.abonnements_microservices.model.Enseignant;
import com.abonnements_microservices.model.Niveau;
import com.abonnements_microservices.services.NiveauService;
import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/niveaux")
@CrossOrigin(origins = {"http://localhost:3036", "http://localhost:3000"})
@Slf4j  // <-- Ajoutez cette annotation

public class NiveauController {
    @Autowired
    private NiveauService niveauService;

    // Using simple list instead of pagination
  /*  @GetMapping
    public ResponseEntity<List<Niveau>> getAllNiveaux() {
        try {
            List<Niveau> niveaux = niveauService.getAllNiveaux();
            return ResponseEntity.ok(niveaux);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }*/

    // Additional /all endpoint that maps to the same functionality
    @GetMapping("/all")
    
    public ResponseEntity<List<NiveauDTO>> getAllNiveauxAlternative() {
        try {
            List<Niveau> niveaux = niveauService.getAllNiveaux();
            List<NiveauDTO> dtos = niveaux.stream()
                .map(n -> {
                    NiveauDTO dto = new NiveauDTO();
                    dto.setId(n.getId());
                    dto.setNom(n.getNom());
                    
                    // Optionnel: inclure les relations si nécessaire
                    if(n.getMatiereNiveaux() != null) {
                        dto.setMatiereIds(n.getMatiereNiveaux().stream()
                            .map(mn -> mn.getMatiere().getId())
                            .collect(Collectors.toList()));
                    }
                    
                    return dto;
                })
                .collect(Collectors.toList());
            
            return ResponseEntity.ok(dtos);
        } catch (Exception e) {
            log.error("Error fetching niveaux", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    private NiveauDTO convertToResponseDto(Niveau niveau) {
        NiveauDTO dto = new NiveauDTO();
        dto.setId(niveau.getId());
        dto.setNom(niveau.getNom());
        
        // Utilisez matiereIds pour stocker les IDs des MatiereNiveau
        if(niveau.getMatiereNiveaux() != null) {
            dto.setMatiereIds(niveau.getMatiereNiveaux().stream()
                .map(mn -> mn.getMatiere().getId()) // Supposant que MatiereNiveau a une relation vers Matiere
                .collect(Collectors.toList()));
        }
        
        if(niveau.getEnseignants() != null) {
            dto.setEnseignantIds(niveau.getEnseignants().stream()
                .map(Enseignant::getId)
                .collect(Collectors.toList()));
        }
        
        return dto;
    }
  /*  public ResponseEntity<List<Niveau>> getAllNiveauxAlternative() {
        try {
            List<Niveau> niveaux = niveauService.getAllNiveaux();
            return ResponseEntity.ok(niveaux);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }*/

    @GetMapping("/{id}")
    

    public ResponseEntity<Niveau> getNiveauById(@PathVariable Long id) {
        Optional<Niveau> niveau = niveauService.getNiveauById(id);
        return niveau.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/add")
    @PreAuthorize("hasAnyRole('ADMIN','ETUDIANT','ENSEIGNANT')")

    public ResponseEntity<?> addNiveau(@Valid @RequestBody NiveauDTO niveauDTO) {
        try {
            Niveau niveau = niveauService.createNiveauFromDTO(niveauDTO);
            return ResponseEntity.ok(niveau);
        } catch (DataIntegrityViolationException e) {
            return ResponseEntity.badRequest().body("Erreur: Ce niveau existe déjà");
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Erreur serveur");
        }
    }
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteNiveau(@PathVariable Long id) {
        try {
            niveauService.deleteNiveau(id);
            return ResponseEntity.ok().build();
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<Niveau> updateNiveau(@PathVariable Long id, @RequestBody Niveau niveau) {
        try {
            Niveau updatedNiveau = niveauService.updateNiveau(id, niveau);
            return ResponseEntity.ok(updatedNiveau);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
}