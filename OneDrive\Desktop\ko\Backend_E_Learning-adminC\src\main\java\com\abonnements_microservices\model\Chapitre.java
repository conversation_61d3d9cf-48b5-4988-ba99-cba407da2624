package com.abonnements_microservices.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import jakarta.persistence.*;
import lombok.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Chapitre {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String nomChapitre;
    private String nomDeProf;
    private Long duree;
    private Long nombreDeCours;
    private String description;

    // Relation vers MatiereNiveau (ManyToOne)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({
        @JoinColumn(name = "matiere_id", referencedColumnName = "matiereId"),
        @JoinColumn(name = "niveau_id", referencedColumnName = "niveauId")
    })
    @JsonBackReference("matiere-niveau-chapitre")
    private MatiereNiveau matiereNiveau;
    @OneToMany(mappedBy = "chapitre", cascade = CascadeType.ALL, orphanRemoval = true)
    //@JsonIgnoreProperties("chapitre")
    @JsonManagedReference("chapitre-cours")

    private Set<Cours> cours = new HashSet<>();

    public void addCours(Cours cours) {
        this.cours.add(cours);
        cours.setChapitre(this);
    }

    public void removeCours(Cours cours) {
        this.cours.remove(cours);
        cours.setChapitre(null);
    }
}
