import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import accessControlService from '../../services/accessControlService';

/**
 * Higher-Order Component (HOC) for access control based on subscription type
 * @param {React.Component} Component The component to wrap with access control
 * @param {string} accessType The type of access to check ('courses', 'recordings', 'liveSessions')
 * @returns {React.Component} The wrapped component with access control
 */
const withAccessControl = (Component, accessType) => {
  return (props) => {
    const [loading, setLoading] = useState(true);
    const [hasAccess, setHasAccess] = useState(false);
    const [error, setError] = useState(null);
    const navigate = useNavigate();

    useEffect(() => {
      const checkAccess = async () => {
        try {
          setLoading(true);
          let accessGranted = false;

          switch (accessType) {
            case 'courses':
              accessGranted = await accessControlService.hasAccessToCourses();
              break;
            case 'recordings':
              accessGranted = await accessControlService.hasAccessToRecordings();
              break;
            case 'liveSessions':
              accessGranted = await accessControlService.hasAccessToLiveSessions();
              break;
            default:
              console.error(`Unknown access type: ${accessType}`);
              setError(`Type d'accès inconnu: ${accessType}`);
          }

          setHasAccess(accessGranted);
        } catch (error) {
          console.error(`Error checking access for ${accessType}:`, error);
          setError(`Erreur lors de la vérification des droits d'accès: ${error.message}`);
        } finally {
          setLoading(false);
        }
      };

      checkAccess();
    }, []);

    if (loading) {
      return (
        <div className="text-center py-5">
          <Spinner animation="border" variant="primary" />
          <p className="mt-2">Vérification de vos droits d'accès...</p>
        </div>
      );
    }

    if (error) {
      return <Alert variant="danger">{error}</Alert>;
    }

    if (!hasAccess) {
      const accessTypeLabels = {
        courses: 'aux cours',
        recordings: 'aux enregistrements',
        liveSessions: 'aux sessions live'
      };

      return (
        <Alert variant="warning">
          <h5>Accès limité</h5>
          <p>Votre abonnement actuel ne vous donne pas accès {accessTypeLabels[accessType]}.</p>
          <p>Pour accéder à cette fonctionnalité, veuillez mettre à niveau votre abonnement vers un forfait qui inclut l'accès {accessTypeLabels[accessType]}.</p>
          <button 
            className="btn btn-primary mt-2"
            onClick={() => navigate('/Mes-abonnement')}
          >
            Voir mes abonnements
          </button>
        </Alert>
      );
    }

    return <Component {...props} />;
  };
};

export default withAccessControl;
