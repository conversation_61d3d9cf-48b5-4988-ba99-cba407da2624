p {
  color: red;
}
.menu-link {
  position: relative;
  padding: 8px 12px;
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
}

.menu-link::after {
  content: "";
  position: absolute;
  width: 0%;
  height: 2px;
  left: 0;
  bottom: 0;
  background-color: #007bff;
  transition: width 0.3s ease;
}

.menu-link:hover {
  color: #007bff;
}

.menu-link:hover::after {
  width: 100%;
}
.menu-link {
  position: relative;
  padding: 6px 10px;
  font-weight: 500;
  color: #333;
  transition: all 0.3s ease-in-out;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-link::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  height: 2px;
  width: 0%;
  background-color: #007bff;
  transition: width 0.3s ease;
}

.menu-link:hover {
  color: #007bff;
}

.menu-link:hover::after {
  width: 100%;
}
.navbar-header .logo-image {
  max-height: 60px;
  margin-bottom: 0;
}

.header {
  background-color: #EEF9F5; /* fond clair de la charte */
  border-bottom: 2px solid #37A7DF;
}

.navbar-nav .nav-link {
  color: #1D1D1B;
  font-weight: 500;
  padding: 8px 16px;
  transition: color 0.3s;
}

.navbar-nav .nav-link:hover {
  color: #37A7DF;
}

.navbar-nav .active > .nav-link {
  color: #248E39;
}

@media (max-width: 768px) {
  .navbar-header {
    margin-left: 15px !important;
  }
}


/*# sourceMappingURL=style.css.map */