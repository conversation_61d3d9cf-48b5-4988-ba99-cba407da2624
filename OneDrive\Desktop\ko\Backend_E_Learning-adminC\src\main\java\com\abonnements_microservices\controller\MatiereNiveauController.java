package com.abonnements_microservices.controller;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.abonnements_microservices.model.MatiereNiveau;
import com.abonnements_microservices.model.MatiereNiveauId;
import com.abonnements_microservices.services.MatiereNiveauService;

import lombok.RequiredArgsConstructor;

@Controller
@RequestMapping("/api/matiere-niveau")
@RequiredArgsConstructor
public class MatiereNiveauController {

    private final MatiereNiveauService matiereNiveauService;

    @PostMapping
    public ResponseEntity<MatiereNiveau> create(@RequestParam Long matiereId, @RequestParam Long niveauId) {
        return ResponseEntity.ok(matiereNiveauService.create(matiereId, niveauId));
    }

    @GetMapping
    public ResponseEntity<List<MatiereNiveau>> getAll() {
        return ResponseEntity.ok(matiereNiveauService.getAll());
    }

    @GetMapping("/{id}")
    public ResponseEntity<MatiereNiveau> getById(@PathVariable MatiereNiveauId id) {
        return ResponseEntity.ok(matiereNiveauService.getById(id));
    }
}