package com.abonnements_microservices.repository;

import com.abonnements_microservices.model.TextToSpeechHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TextToSpeechHistoryRepository extends JpaRepository<TextToSpeechHistory, Long> {
    List<TextToSpeechHistory> findByUserIdOrderByCreatedAtDesc(Long userId);
}
