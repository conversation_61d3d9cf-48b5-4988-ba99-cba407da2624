package com.abonnements_microservices.controller;

import com.abonnements_microservices.model.LiveSession;
import com.abonnements_microservices.model.LiveSessionStatus;
import com.abonnements_microservices.model.User;
import com.abonnements_microservices.services.LiveSessionService;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/livesessions")
@CrossOrigin(origins = {"http://localhost:3036", "http://localhost:3000"})
@Slf4j
public class LiveSessionController {

    @Autowired
    private LiveSessionService liveSessionService;

    /**
     * Create a new live session (only by enseignants)
     */
    @PostMapping
    @PreAuthorize("hasRole('ENSEIGNANT')")
    public ResponseEntity<?> createLiveSession(@RequestBody Map<String, Object> request) {
        try {
            log.debug("Creating new live session with data: {}", request);
            
            LiveSession liveSession = new LiveSession();
            liveSession.setTitle((String) request.get("title"));
            liveSession.setDescription((String) request.get("description"));
            liveSession.setRoomName((String) request.get("roomName"));
            
            // Parse dates if provided (frontend should send ISO format)
            String startTimeStr = (String) request.get("scheduledStartTime");
            String endTimeStr = (String) request.get("scheduledEndTime");
            
            if (startTimeStr != null) {
                liveSession.setScheduledStartTime(java.time.LocalDateTime.parse(startTimeStr));
            }
            
            if (endTimeStr != null) {
                liveSession.setScheduledEndTime(java.time.LocalDateTime.parse(endTimeStr));
            }

            // Get the current user ID (enseignant)
            Long enseignantId = Long.parseLong(request.get("enseignantId").toString());
            
            // Get matiere ID if provided
            Long matiereId = null;
            if (request.get("matiereId") != null) {
                matiereId = Long.parseLong(request.get("matiereId").toString());
            }
            
            LiveSession created = liveSessionService.createLiveSession(liveSession, enseignantId, matiereId);
            return ResponseEntity.ok(created);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid input data for live session creation: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
        } catch (EntityNotFoundException e) {
            log.warn("Entity not found during live session creation: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
        } catch (Exception e) {
            log.error("Error creating live session: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "message", "Failed to create live session",
                    "error", e.getMessage()
                ));
        }
    }
    
    /**
     * Start a scheduled live session
     */
    @PostMapping("/{id}/start")
    @PreAuthorize("hasRole('ENSEIGNANT')")
    public ResponseEntity<?> startLiveSession(@PathVariable Long id) {
        try {
            LiveSession session = liveSessionService.startLiveSession(id);
            return ResponseEntity.ok(session);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
        } catch (Exception e) {
            log.error("Error starting live session: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "message", "Failed to start live session",
                    "error", e.getMessage()
                ));
        }
    }
    
    /**
     * End an active live session
     */
    @PostMapping("/{id}/end")
    @PreAuthorize("hasRole('ENSEIGNANT')")
    public ResponseEntity<?> endLiveSession(@PathVariable Long id) {
        try {
            LiveSession session = liveSessionService.endLiveSession(id);
            return ResponseEntity.ok(session);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
        } catch (Exception e) {
            log.error("Error ending live session: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "message", "Failed to end live session",
                    "error", e.getMessage()
                ));
        }
    }
    
    /**
     * Cancel a scheduled live session
     */
    @PostMapping("/{id}/cancel")
    @PreAuthorize("hasRole('ENSEIGNANT')")
    public ResponseEntity<?> cancelLiveSession(@PathVariable Long id) {
        try {
            LiveSession session = liveSessionService.cancelLiveSession(id);
            return ResponseEntity.ok(session);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
        } catch (Exception e) {
            log.error("Error cancelling live session: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "message", "Failed to cancel live session",
                    "error", e.getMessage()
                ));
        }
    }
    
    /**
     * Get session by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getLiveSessionById(@PathVariable Long id) {
        try {
            LiveSession session = liveSessionService.getLiveSessionById(id);
            return ResponseEntity.ok(session);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * Get all live sessions
     */
    @GetMapping
    public ResponseEntity<List<LiveSession>> getAllLiveSessions() {
        List<LiveSession> sessions = liveSessionService.getAllLiveSessions();
        return ResponseEntity.ok(sessions);
    }
    
    /**
     * Get live sessions by enseignant
     */
    @GetMapping("/enseignant/{enseignantId}")
    public ResponseEntity<List<LiveSession>> getLiveSessionsByEnseignant(@PathVariable Long enseignantId) {
        List<LiveSession> sessions = liveSessionService.getLiveSessionsByEnseignant(enseignantId);
        return ResponseEntity.ok(sessions);
    }
    
    /**
     * Get live sessions by matiere
     */
    @GetMapping("/matiere/{matiereId}")
    public ResponseEntity<List<LiveSession>> getLiveSessionsByMatiere(@PathVariable Long matiereId) {
        List<LiveSession> sessions = liveSessionService.getLiveSessionsByMatiere(matiereId);
        return ResponseEntity.ok(sessions);
    }
      /**
     * Get live sessions by status
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<List<LiveSession>> getLiveSessionsByStatus(@PathVariable String status) {
        try {
            LiveSessionStatus sessionStatus = LiveSessionStatus.valueOf(status.toUpperCase());
            List<LiveSession> sessions = liveSessionService.getLiveSessionsByStatus(sessionStatus);
            return ResponseEntity.ok(sessions);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Get available live sessions for students
     * This endpoint doesn't require special permissions to allow students to view sessions
     */
    @GetMapping("/student/available")
    public ResponseEntity<List<LiveSession>> getAvailableSessionsForStudents() {
        try {
            log.debug("Fetching available sessions for students");
            
            // Get live (active) sessions
            List<LiveSession> liveSessions = liveSessionService.getLiveSessionsByStatus(LiveSessionStatus.LIVE);
            
            // Get scheduled sessions
            List<LiveSession> scheduledSessions = liveSessionService.getLiveSessionsByStatus(LiveSessionStatus.SCHEDULED);
            
            // Combine the lists
            java.util.ArrayList<LiveSession> availableSessions = new java.util.ArrayList<>();
            
            if (liveSessions != null) {
                availableSessions.addAll(liveSessions);
            }
            
            if (scheduledSessions != null) {
                availableSessions.addAll(scheduledSessions);
            }
            
            return ResponseEntity.ok(availableSessions);
        } catch (Exception e) {
            log.error("Error fetching available sessions for students: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Generate join information for a user (token, room name, etc.)
     */
    @GetMapping("/{id}/join")
    public ResponseEntity<?> getSessionJoinInfo(@PathVariable Long id, @RequestParam Long userId, @RequestParam String username) {
        try {
            // Check if current user has ENSEIGNANT role
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            boolean isTeacher = auth.getAuthorities().contains(new SimpleGrantedAuthority("ROLE_ENSEIGNANT"));
            
            Map<String, Object> joinInfo = liveSessionService.generateSessionJoinInfo(id, userId, username, isTeacher);
            return ResponseEntity.ok(joinInfo);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
        } catch (Exception e) {
            log.error("Error generating join info: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "message", "Failed to generate join information",
                    "error", e.getMessage()
                ));
        }
    }
}
