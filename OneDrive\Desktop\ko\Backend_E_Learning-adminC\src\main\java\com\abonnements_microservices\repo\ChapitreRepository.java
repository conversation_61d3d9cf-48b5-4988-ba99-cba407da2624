package com.abonnements_microservices.repo;

import com.abonnements_microservices.model.Chapitre;
import com.abonnements_microservices.model.Matiere;

import jakarta.transaction.Transactional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChapitreRepository extends JpaRepository<Chapitre, Long> {

	List<Chapitre> findByNomChapitreContainingIgnoreCase(String nomChapitre);

	// Supprimer les chapitres par matiereId + niveauId
	@Modifying
	@Query("DELETE FROM Chapitre c WHERE c.matiereNiveau.id.matiereId = :matiereId AND c.matiereNiveau.id.niveauId = :niveauId")
	void deleteByMatiereIdAndNiveauId(Long matiereId, Long niveauId);



	// Trouver les chapitres par matiereId + niveauId
	@Query("SELECT c FROM Chapitre c WHERE c.matiereNiveau.matiere.id = :matiereId AND c.matiereNiveau.niveau.id = :niveauId")
	List<Chapitre> findByMatiereNiveau(@Param("matiereId") Long matiereId, @Param("niveauId") Long niveauId);

}