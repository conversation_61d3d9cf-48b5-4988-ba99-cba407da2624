package com.abonnements_microservices.repo;

import com.abonnements_microservices.model.Abonnement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface AbonnementRepository extends JpaRepository<Abonnement, Long> {
    List<Abonnement> findByNomContainingIgnoreCase(String nom);
    List<Abonnement> findByEtudiants_Id(Long etudiantId);
    @Query("SELECT a FROM Abonnement a LEFT JOIN FETCH a.matieres")
    List<Abonnement> findAllWithMatieres();

}
