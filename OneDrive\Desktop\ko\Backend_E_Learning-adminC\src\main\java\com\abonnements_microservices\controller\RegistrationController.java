package com.abonnements_microservices.controller;

import com.abonnements_microservices.model.Etudiant;
import com.abonnements_microservices.model.Niveau;
import com.abonnements_microservices.repo.EtudiantRepository;
import com.abonnements_microservices.repo.NiveauRepository;
import com.abonnements_microservices.services.EtudiantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.persistence.EntityNotFoundException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/registration")
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class RegistrationController {

    @Autowired
    private EtudiantService etudiantService;
    
    @Autowired
    private EtudiantRepository etudiantRepository;
    
    @Autowired
    private NiveauRepository niveauRepository;

    /**
     * Register a new student with pending status
     * The student account will require admin approval before it can be used
     */
    @PostMapping("/etudiant")
    public ResponseEntity<?> registerStudent(@RequestBody Etudiant etudiant) {
        try {
            // Check if email already exists
            Optional<Etudiant> existingEtudiant = etudiantRepository.findByEmail(etudiant.getEmail());
            if (existingEtudiant.isPresent()) {
                Map<String, String> response = new HashMap<>();
                response.put("message", "Email already exists");
                return ResponseEntity.badRequest().body(response);
            }

            // Check if username already exists
            Optional<Etudiant> existingUsername = etudiantRepository.findByUsername(etudiant.getUsername());
            if (existingUsername.isPresent()) {
                Map<String, String> response = new HashMap<>();
                response.put("message", "Username already exists");
                return ResponseEntity.badRequest().body(response);
            }

            // Verify that the niveau exists
            Long niveauId = etudiant.getNiveauId();
            if (niveauId != null) {
                Niveau niveau = niveauRepository.findById(niveauId)
                    .orElseThrow(() -> new EntityNotFoundException("Niveau not found with id: " + niveauId));
                etudiant.setNiveau(niveau);
            }

            // Set the status to PENDING
            etudiant.setStatus("PENDING");
            
            // Create a list with the selected abonnement ID if it exists
            List<Long> abonnementIds = null;
            if (etudiant.getAbonnementTypeId() != null) {
                abonnementIds = List.of(etudiant.getAbonnementTypeId());
            }
            
            // Save the student with the selected abonnement
            Etudiant savedEtudiant = etudiantService.createEtudiantWithoutNotification(etudiant, abonnementIds);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Registration submitted successfully. Waiting for admin approval.");
            response.put("id", savedEtudiant.getId());
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (EntityNotFoundException e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", "Failed to register student: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * Get a list of all pending student registrations
     */
    @GetMapping("/etudiant/pending")
    public ResponseEntity<?> getPendingRegistrations() {
        try {
            return ResponseEntity.ok(etudiantRepository.findByStatus("PENDING"));
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", "Failed to retrieve pending registrations: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * Approve a student registration
     */
    @PostMapping("/etudiant/{id}/approve")
    public ResponseEntity<?> approveRegistration(@PathVariable Long id) {
        try {
            Etudiant etudiant = etudiantRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Student not found with id: " + id));
            
            if (!"PENDING".equals(etudiant.getStatus())) {
                Map<String, String> response = new HashMap<>();
                response.put("message", "Cannot approve a student that is not in PENDING status");
                return ResponseEntity.badRequest().body(response);
            }
            
            // Change status to ACTIVE
            etudiant.setStatus("ACTIVE");
            
            // Save the updated student
            Etudiant updatedEtudiant = etudiantRepository.save(etudiant);
            
            // Now send the welcome email and SMS since the account is approved
            etudiantService.sendWelcomeNotifications(updatedEtudiant);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Student registration approved successfully");
            return ResponseEntity.ok(response);
        } catch (EntityNotFoundException e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", "Failed to approve registration: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * Reject a student registration
     */
    @PostMapping("/etudiant/{id}/reject")
    public ResponseEntity<?> rejectRegistration(@PathVariable Long id) {
        try {
            Etudiant etudiant = etudiantRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Student not found with id: " + id));
            
            if (!"PENDING".equals(etudiant.getStatus())) {
                Map<String, String> response = new HashMap<>();
                response.put("message", "Cannot reject a student that is not in PENDING status");
                return ResponseEntity.badRequest().body(response);
            }
            
            // Change status to REJECTED
            etudiant.setStatus("REJECTED");
            
            // Save the updated student
            etudiantRepository.save(etudiant);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Student registration rejected successfully");
            return ResponseEntity.ok(response);
        } catch (EntityNotFoundException e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("message", "Failed to reject registration: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
