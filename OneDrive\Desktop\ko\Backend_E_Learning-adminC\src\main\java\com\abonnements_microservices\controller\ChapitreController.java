package com.abonnements_microservices.controller;

import com.abonnements_microservices.dto.ChapitreDTO;
import org.springframework.data.domain.Pageable;

import com.abonnements_microservices.model.Chapitre;
import com.abonnements_microservices.services.ChapitreService;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/chapitres")
@CrossOrigin(origins = { "http://localhost:3036", "http://localhost:3000" })
public class ChapitreController {

    @Autowired
    private ChapitreService chapitreService;

    @GetMapping("/page")
    public ResponseEntity<Page<ChapitreDTO>> getAllChapitres(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "8") int size) {

        Page<Chapitre> chapitrePage = chapitreService.getAllChapitreParPage(page, size);
        Page<ChapitreDTO> chapitreDTOPage = chapitrePage.map(chapitre -> {
            ChapitreDTO dto = new ChapitreDTO();
            dto.setId(chapitre.getId());
            dto.setNomChapitre(chapitre.getNomChapitre());
            dto.setNomDeProf(chapitre.getNomDeProf());
            dto.setDuree(chapitre.getDuree());
            dto.setNombreDeCours(chapitre.getNombreDeCours());
            dto.setDescription(chapitre.getDescription());

            // Correctement récupérer les IDs de Matiere et Niveau
            dto.setMatiereId(chapitre.getMatiereNiveau().getMatiere().getId());
            dto.setNiveauId(chapitre.getMatiereNiveau().getNiveau().getId());

            return dto;
        });

        return ResponseEntity.ok(chapitreDTOPage);
    }



    @GetMapping("/all")
    public ResponseEntity<List<Chapitre>> getAllChapitres() {
        try {
            List<Chapitre> chapitres = chapitreService.getAllChapitres();
            return ResponseEntity.ok(chapitres);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<Chapitre> getChapitreById(@PathVariable Long id) {
        return chapitreService.getChapitreById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/matiere/{matiereId}/niveau/{niveauId}")
    public ResponseEntity<List<Chapitre>> getChapitresByMatiereNiveau(
            @PathVariable Long matiereId,
            @PathVariable Long niveauId) {
        try {
            List<Chapitre> chapitres = chapitreService.getChapitresByMatiereNiveau(matiereId, niveauId);
            return ResponseEntity.ok(chapitres);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/matiere/{matiereId}")
    public ResponseEntity<List<Chapitre>> getChapitresByMatiere(@PathVariable Long matiereId) {
        try {
            // This endpoint returns all chapitres for a matiere regardless of niveau
            List<Chapitre> chapitres = chapitreService.getChapitresByMatiere(matiereId);
            return ResponseEntity.ok(chapitres);
        } catch (Exception e) {
            e.printStackTrace(); // Log the exception for debugging
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/{matiereId}/{niveauId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT')")
    public ResponseEntity<Chapitre> addChapitre(
            @PathVariable Long matiereId,
            @PathVariable Long niveauId,
            @Valid @RequestBody ChapitreDTO dto) {
        try {
            // Injecte les IDs depuis le path dans le DTO
            dto.setMatiereId(matiereId);
            dto.setNiveauId(niveauId);

            // Utilise la nouvelle méthode clean
            Chapitre savedChapitre = chapitreService.createChapitre(dto);
            return ResponseEntity.ok(savedChapitre);

        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(null);
        }
    }

    // Nouvel endpoint pour ajouter un chapitre avec seulement l'ID de la matière
    @PostMapping("/{matiereId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT')")
    public ResponseEntity<Chapitre> addChapitreWithMatiere(
            @PathVariable Long matiereId,
            @RequestBody Map<String, Object> requestData) {
        try {
            // Créer un DTO à partir des données de la requête
            ChapitreDTO dto = new ChapitreDTO();
            dto.setMatiereId(matiereId);

            // Récupérer les champs du frontend
            if (requestData.containsKey("nomChapitre")) {
                dto.setNomChapitre((String) requestData.get("nomChapitre"));
            }

            if (requestData.containsKey("nomDeProf")) {
                dto.setNomDeProf((String) requestData.get("nomDeProf"));
            }

            if (requestData.containsKey("duree")) {
                // Convertir la durée en Long
                Object dureeObj = requestData.get("duree");
                if (dureeObj instanceof Integer) {
                    dto.setDuree(((Integer) dureeObj).longValue());
                } else if (dureeObj instanceof String) {
                    try {
                        dto.setDuree(Long.parseLong((String) dureeObj));
                    } catch (NumberFormatException e) {
                        dto.setDuree(0L);
                    }
                }
            }

            if (requestData.containsKey("description")) {
                dto.setDescription((String) requestData.get("description"));
            }

            // Utilise la méthode existante qui gère maintenant le cas où niveauId est null
            Chapitre savedChapitre = chapitreService.createChapitre(dto);
            return ResponseEntity.ok(savedChapitre);

        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body(null);
        }
    }


    @PutMapping(value = "/{id}", consumes = "application/json")
    @PreAuthorize("hasAnyRole('ADMIN','ETUDIANT','ENSEIGNANT')")
    public ResponseEntity<Chapitre> updateChapitre(
            @PathVariable Long id,
            @RequestBody Chapitre updatedChapitre) {
        Chapitre updated = chapitreService.updateChapitre(id, updatedChapitre);
        return ResponseEntity.ok(updated);
    }


    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN','ETUDIANT','ENSEIGNANT')")

    public ResponseEntity<Void> deleteByMatiereNiveau(
            @RequestParam Long matiereId,
            @RequestParam Long niveauId) {
        chapitreService.deleteChapitreByMatiereNiveau(matiereId, niveauId);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/search")
    public ResponseEntity<List<Chapitre>> getChapitresByNom(@RequestParam String nomChapitre) {
        try {
            List<Chapitre> chapitres = chapitreService.getChapitreByNomChapitre(nomChapitre);
            return ResponseEntity.ok(chapitres);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/matiere/{idMatiere}/search")
    public ResponseEntity<List<Chapitre>> getChapitreByNom(@RequestParam String nomChapitre) {
        try {
            List<Chapitre> chapitres = chapitreService.getChapitreByNomChapitre(nomChapitre);
            return ResponseEntity.ok(chapitres);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
    @GetMapping("/page/matiere/{matiereId}/niveau/{niveauId}")
    public ResponseEntity<Page<Chapitre>> getChapitresPageByMatiereNiveau(
        @PathVariable Long matiereId,
        @PathVariable Long niveauId,
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "8") int size) {

        List<Chapitre> chapitres = chapitreService.getChapitresByMatiereNiveau(matiereId, niveauId);
        Pageable pageable = PageRequest.of(page, size);
        int start = Math.min((int) pageable.getOffset(), chapitres.size());
        int end = Math.min((start + pageable.getPageSize()), chapitres.size());
        Page<Chapitre> pageResult = new PageImpl<>(chapitres.subList(start, end), pageable, chapitres.size());

        return ResponseEntity.ok(pageResult);
    }
}
