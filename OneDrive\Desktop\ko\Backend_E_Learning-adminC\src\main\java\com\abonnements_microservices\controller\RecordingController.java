package com.abonnements_microservices.controller;

import com.abonnements_microservices.model.Recording;
import com.abonnements_microservices.model.User;
import com.abonnements_microservices.services.RecordingService;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/recordings")
@CrossOrigin(origins = {"http://localhost:3036", "http://localhost:3000"})
@Slf4j
public class RecordingController {

    @Autowired
    private RecordingService recordingService;

    /**
     * Upload a recording for a matière and niveau
     */
    @PostMapping("/upload")
    @PreAuthorize("hasAnyRole('ENSEIGNANT', 'ADMIN')")
    public ResponseEntity<?> uploadRecording(
            @RequestParam("file") MultipartFile recordingFile,
            @RequestParam("matiereId") Long matiereId,
            @RequestParam("niveauId") Long niveauId,
            @RequestParam("title") String title,
            @RequestParam(value = "description", required = false) String description) {
        try {
            log.info("Uploading recording for matière ID: {} and niveau ID: {}", matiereId, niveauId);

            // Get the current user's authentication details
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            Long enseignantId = null;

            // Log auth information for debugging
            log.debug("Authentication details: principal={}, authorities={}",
                auth.getPrincipal(), auth.getAuthorities());

            boolean isTeacher = auth.getAuthorities().contains(new SimpleGrantedAuthority("ROLE_ENSEIGNANT"));

            // If the current user is a teacher, use their ID
            if (isTeacher && auth.getPrincipal() instanceof User) {
                enseignantId = ((User) auth.getPrincipal()).getId();
                log.debug("Using enseignant ID: {}", enseignantId);
            }

            Recording recording = recordingService.uploadRecording(
                matiereId, niveauId, enseignantId, title, description, recordingFile);

            Map<String, Object> response = new HashMap<>();
            response.put("id", recording.getId());
            response.put("title", recording.getTitle());
            response.put("videoUrl", recording.getVideoUrl());
            response.put("message", "Recording uploaded successfully");

            return ResponseEntity.ok(response);
        } catch (EntityNotFoundException e) {
            log.error("Entity not found: {}", e.getMessage());
            return ResponseEntity.status(404).body("Entity not found: " + e.getMessage());
        } catch (Exception e) {
            log.error("Error uploading recording: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Error uploading recording: " + e.getMessage());
        }
    }

    /**
     * Get recordings for a matière
     */
    @GetMapping("/matiere/{matiereId}")
    public ResponseEntity<List<Recording>> getRecordingsByMatiere(@PathVariable Long matiereId) {
        List<Recording> recordings = recordingService.getRecordingsByMatiere(matiereId);
        return ResponseEntity.ok(recordings);
    }

    /**
     * Get recordings for a matière and niveau
     */
    @GetMapping("/matiere/{matiereId}/niveau/{niveauId}")
    public ResponseEntity<List<Recording>> getRecordingsByMatiereAndNiveau(
            @PathVariable Long matiereId, @PathVariable Long niveauId) {
        List<Recording> recordings = recordingService.getRecordingsByMatiereAndNiveau(matiereId, niveauId);
        return ResponseEntity.ok(recordings);
    }

    /**
     * Get recordings by enseignant
     */
    @GetMapping("/enseignant/{enseignantId}")
    public ResponseEntity<List<Recording>> getRecordingsByEnseignant(@PathVariable Long enseignantId) {
        List<Recording> recordings = recordingService.getRecordingsByEnseignant(enseignantId);
        return ResponseEntity.ok(recordings);
    }

    /**
     * Get recordings by niveau
     */
    @GetMapping("/niveau/{niveauId}")
    public ResponseEntity<List<Recording>> getRecordingsByNiveau(@PathVariable Long niveauId) {
        List<Recording> recordings = recordingService.getRecordingsByNiveau(niveauId);
        return ResponseEntity.ok(recordings);
    }
}
