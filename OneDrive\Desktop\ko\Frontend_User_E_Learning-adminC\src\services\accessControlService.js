import axiosInstance from './axiosService';
import keycloak from '../keycloak';

/**
 * Service for checking access permissions based on subscription type
 */
class AccessControlService {
  /**
   * Get all access permissions for the current student
   * @returns {Promise<Object>} Object with access permissions (courses, recordings, liveSessions)
   */
  async getCurrentStudentPermissions() {
    try {
      await keycloak.updateToken(5);
      const response = await axiosInstance.get('/api/etudiants/me/access', {
        headers: {
          'Authorization': `Bearer ${keycloak.token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching access permissions:', error);
      // Default to no permissions if there's an error
      return {
        courses: false,
        recordings: false,
        liveSessions: false
      };
    }
  }

  /**
   * Check if the current student has access to courses
   * @returns {Promise<boolean>} True if the student has access to courses
   */
  async hasAccessToCourses() {
    try {
      const permissions = await this.getCurrentStudentPermissions();
      return permissions.courses;
    } catch (error) {
      console.error('Error checking course access:', error);
      return false;
    }
  }

  /**
   * Check if the current student has access to recordings
   * @returns {Promise<boolean>} True if the student has access to recordings
   */
  async hasAccessToRecordings() {
    try {
      const permissions = await this.getCurrentStudentPermissions();
      return permissions.recordings;
    } catch (error) {
      console.error('Error checking recordings access:', error);
      return false;
    }
  }

  /**
   * Check if the current student has access to live sessions
   * @returns {Promise<boolean>} True if the student has access to live sessions
   */
  async hasAccessToLiveSessions() {
    try {
      const permissions = await this.getCurrentStudentPermissions();
      return permissions.liveSessions;
    } catch (error) {
      console.error('Error checking live sessions access:', error);
      return false;
    }
  }

  /**
   * Check if the current student has access to a specific matiere
   * @param {number} matiereId The ID of the matiere to check
   * @returns {Promise<boolean>} True if the student has access to the matiere
   */
  async hasAccessToMatiere(matiereId) {
    try {
      await keycloak.updateToken(5);
      const userId = keycloak.tokenParsed.sub;
      const response = await axiosInstance.get(`/api/etudiants/${userId}/access/matiere/${matiereId}`, {
        headers: {
          'Authorization': `Bearer ${keycloak.token}`
        }
      });
      return response.data.hasAccess;
    } catch (error) {
      console.error(`Error checking access to matiere ${matiereId}:`, error);
      return false;
    }
  }
}

export default new AccessControlService();
