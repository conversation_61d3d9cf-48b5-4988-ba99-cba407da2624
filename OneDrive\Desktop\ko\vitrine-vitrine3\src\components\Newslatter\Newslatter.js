import React from 'react'

const SubmitHandler = (e) => {
    e.preventDefault()
}


const Newslatter = (props) => {
    return (
        <section className="wpo-subscribe-section section-padding">
            <div className="container">
                <div className="wpo-subscribe-wrap">
                    <div className="subscribe-text">
                        <h3>Abonnez-vous à notre lettre d'information pour rester informé de nos nouveautés et services.</h3>
                    </div>
                    <div className="subscribe-form">
                        <form onSubmit={SubmitHandler}>
                            <div className="input-field">
                                <input type="email" placeholder="Enter your email" required/>
                                    <button type="submit">S’abonner</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default Newslatter;