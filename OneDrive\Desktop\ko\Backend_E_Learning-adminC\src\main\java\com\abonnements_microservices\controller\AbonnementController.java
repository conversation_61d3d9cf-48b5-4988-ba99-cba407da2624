package com.abonnements_microservices.controller;

import com.abonnements_microservices.dto.AbonnementDTO;
import com.abonnements_microservices.dto.ImageAbonnementDTO;
import com.abonnements_microservices.dto.MatiereDTO;
import com.abonnements_microservices.model.Abonnement;
import com.abonnements_microservices.model.ImageAbonnement;
import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.repo.AbonnementRepository;
import com.abonnements_microservices.repo.ImageAbonnementRepository;
import com.abonnements_microservices.repo.MatiereRepository;
import com.abonnements_microservices.services.AbonnementService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/abonnements")
@CrossOrigin(origins = { "http://localhost:3036", "http://localhost:3000" })
@Slf4j  // <-- Ajoutez cette annotation

public class AbonnementController {
    @Autowired
    private AbonnementService abonnementService;
    @Autowired
    private AbonnementRepository abonnementRepository;
    @Autowired
    private MatiereRepository matiereRepository;
    @Autowired
    private ImageAbonnementRepository imageAbonnementRepository;

    @GetMapping("/page")
    public ResponseEntity<Page<AbonnementDTO>> getAllAbonnements(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "3") int size) {
        try {
            Page<Abonnement> pageResult = abonnementService.getAllAbonnementParPage(page, size);

            // Conversion correcte en DTO
            Page<AbonnementDTO> dtoPage = pageResult.map(abonnement -> {
                AbonnementDTO dto = new AbonnementDTO();
                dto.setId(abonnement.getId());
                dto.setNom(abonnement.getNom());
                dto.setDescription(abonnement.getDescription());
                dto.setPrix(abonnement.getPrix());
                dto.setDuree(abonnement.getDuree());

                // ✅ Conversion de l'image
                if (abonnement.getImageAbonnement() != null) {
                    ImageAbonnement image = abonnement.getImageAbonnement();
                    dto.setImageAbonnement(new ImageAbonnementDTO(image.getIdImage(), image.getName(), image.getType(), null));
                }

                // ✅ Conversion des matières
                if (abonnement.getMatieres() != null) {
                    Set<Long> matiereIds = abonnement.getMatieres().stream()
                        .map(Matiere::getIdMatiere)
                        .collect(Collectors.toSet());
                    dto.setMatiereIds(matiereIds);
                }

                return dto;
            });

            return ResponseEntity.ok().body(dtoPage);
        } catch (Exception e) {
            log.error("Error fetching abonnements", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    @GetMapping("/all")


    public ResponseEntity<List<Abonnement>> getAllAbonnements() {
        try {
            List<Abonnement> abonnements = abonnementService.getAllAbonnements();
            return ResponseEntity.ok().body(abonnements);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<Abonnement> getAbonnementById(@PathVariable Long id) {
        try {
            Abonnement abonnement = abonnementService.getAbonnementById(id);
            return ResponseEntity.ok(abonnement);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/{id}/matieres")
    public ResponseEntity<Set<MatiereDTO>> getMatieresByAbonnement(@PathVariable Long id) {
        try {
            Abonnement abonnement = abonnementService.getAbonnementById(id); // pas Optional ici
            Set<MatiereDTO> matiereDTOs = abonnement.getMatieres().stream()
                .map(this::convertToMatiereDTO)
                .collect(Collectors.toSet());
            return ResponseEntity.ok(matiereDTOs);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    private MatiereDTO convertToMatiereDTO(Matiere matiere) {
        return new MatiereDTO(matiere); // Utilisez le constructeur existant
    }
    @GetMapping("/search")
    public ResponseEntity<List<Abonnement>> getAbonnementsByNom(@RequestParam String nom) {
        try {
            List<Abonnement> abonnements = abonnementService.getAbonnementsByNom(nom);
            return ResponseEntity.ok().body(abonnements);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping(value = "/add", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> addAbonnement(
            @RequestPart("abonnement") String abonnementJson,
            @RequestPart(value = "image", required = false) MultipartFile imageFile) {

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            AbonnementDTO abonnementDTO = objectMapper.readValue(abonnementJson, AbonnementDTO.class);

            Abonnement savedAbonnement = abonnementService.createAbonnement(abonnementDTO, imageFile);
            return ResponseEntity.ok(savedAbonnement);

        } catch (JsonProcessingException e) {
            return ResponseEntity.badRequest().body("Format JSON invalide");
        } catch (IOException e) {
            return ResponseEntity.internalServerError().body("Erreur de traitement");
        }
    }



    @PutMapping(value = "/update/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> updateAbonnement(
            @PathVariable Long id,
            @RequestParam("nom") String nom,
            @RequestParam("description") String description,
            @RequestParam("prix") double prix,
            @RequestParam("duree") Integer duree,
            @RequestParam(value = "image", required = false) MultipartFile imageFile,
            @RequestParam(value = "matiereIds", required = false) List<Long> matiereIds
    ) {
        // récupère l'abonnement existant
        Optional<Abonnement> abonnementOpt = abonnementRepository.findById(id);
        if (abonnementOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        Abonnement abonnement = abonnementOpt.get();
        abonnement.setNom(nom);
        abonnement.setDescription(description);
        abonnement.setPrix(prix);
        abonnement.setDuree(duree);

        // update image si présente
        if (imageFile != null && !imageFile.isEmpty()) {
            try {
                ImageAbonnement image = new ImageAbonnement();
                image.setName(imageFile.getOriginalFilename());
                image.setType(imageFile.getContentType());
                image.setImage(imageFile.getBytes()); // Cette méthode peut lancer une IOException
                abonnement.setImageAbonnement(image);
            } catch (IOException e) {
                e.printStackTrace(); // Log l'erreur, ou utiliser un mécanisme de gestion des erreurs
                // Traiter l'erreur comme il faut
            }
        }

        // update matieres
        if (matiereIds != null) {
            Set<Matiere> matieres = new HashSet<>(matiereRepository.findAllById(matiereIds));
            abonnement.setMatieres(matieres);
        }

        abonnementRepository.save(abonnement);
        return ResponseEntity.ok(abonnement);
    }


    @DeleteMapping("/delete/{id}")
    public ResponseEntity<String> deleteAbonnement(@PathVariable Long id) {
        try {
            abonnementService.deleteAbonnement(id);
            return ResponseEntity.ok("Abonnement supprimé avec succès !");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Erreur lors de la suppression: " + e.getMessage());
        }
    }
}
