package com.abonnements_microservices.services;

import com.abonnements_microservices.model.User;
import com.abonnements_microservices.repo.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.DiscriminatorValue;

@Service
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private KeycloakUserService keycloakUserService;    @Transactional
    public User createUser(User user) {
        // Get role from discriminator value of the actual entity class
        String role = user.getClass().getAnnotation(DiscriminatorValue.class).value();
        if (role == null || (!role.equals("ETUDIANT") && !role.equals("ENSEIGNANT"))) {
            throw new IllegalArgumentException("Role must be either ETUDIANT or ENSEIGNANT");
        }

        // Check if username or email already exists
        if (userRepository.existsByUsername(user.getUsername())) {
            throw new IllegalArgumentException("Username already exists");
        }
        if (userRepository.existsByEmail(user.getEmail())) {
            throw new IllegalArgumentException("Email already exists");
        }

        // Create user in Keycloak first
        try {
            keycloakUserService.createUser(
                user.getUsername(),
                user.getFirstName(),
                user.getLastName(),                user.getEmail(),
                user.getPassword(),
                role // Use the role we got from the discriminator
            );
        } catch (Exception e) {
            throw new RuntimeException("Failed to create user in Keycloak: " + e.getMessage());
        }

        // If Keycloak creation was successful, save to database
        return userRepository.save(user);
    }
}