import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useKeycloak } from "@react-keycloak/web";


const API_COURS = "http://localhost:8084/api/cours";
const API_CHAPITRES = "http://localhost:8084/api/chapitres/all";

const AjouterCours = () => {
  const { keycloak } = useKeycloak();

  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    titre: "",
    description: "",
    duree: "",
    niveau: "",
    chapitreId: "",
  });
  const [chapitres, setChapitres] = useState([]);
  const [videoFile, setVideoFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    const fetchChapitres = async () => {
      try {
        const response = await fetch(API_CHAPITRES, {
          headers: {
            'Authorization': `Bearer ${keycloak.token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch chapters');
        }
        
        const data = await response.json();
        setChapitres(data);
      } catch (error) {
        console.error("Erreur chargement chapitres :", error);
      }
    };

    if (keycloak.authenticated) {
      fetchChapitres();
    }
  }, [keycloak.authenticated, keycloak.token]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const handleVideoChange = (e) => {
    setVideoFile(e.target.files[0]);
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.titre) newErrors.titre = "Le titre est requis";
    if (!formData.description) newErrors.description = "La description est requise";
    if (!formData.duree) newErrors.duree = "La durée est requise";
    if (!formData.chapitreId) newErrors.chapitreId = "Le chapitre est requis";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setUploading(true);
    try {
      const newCours = {
        titre: formData.titre,
        description: formData.description,
        duree: parseInt(formData.duree),
        dateCreation: new Date().toISOString(),
        chapitre: {
          id: parseInt(formData.chapitreId)
        }
      };

      const createResponse = await fetch(`${API_COURS}/add`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
          "Authorization": `Bearer ${keycloak.token}`


        },
        body: JSON.stringify(newCours)
      });

      if (!createResponse.ok) {
        const errorData = await createResponse.json().catch(() => null);
        throw new Error(errorData?.message || "Erreur lors de la création du cours");
      }

      const createdCours = await createResponse.json();

      // Upload the video if present
      if (videoFile) {
        const formDataVideo = new FormData();
        formDataVideo.append("file", videoFile);
        
        const uploadResponse = await fetch(
          `${API_COURS}/uploadVideo/${createdCours.idCours}`,
          {headers: {
            
            "Authorization": `Bearer ${keycloak.token}`
  
  
          },
            method: "POST",
            body: formDataVideo
          }
        );

        if (!uploadResponse.ok) {
          throw new Error("Erreur lors du téléchargement de la vidéo");
        }
      }

      setSuccessMessage("Cours créé avec succès!");
      setTimeout(() => {
        navigate("/cours");
      }, 2000);
    } catch (error) {
      setErrors({ submit: error.message });
    } finally {
      setUploading(false);
    }
  };

  return (
    
    <div className="container-fluid py-4">
   <div className="row justify-content-center">
      <div className="col-lg-10 col-md-12">
        <div className="row page-titles mx-0 mb-4">
          <div className="col">
          <h4
  className="title-section text-uppercase fw-bold mb-0"
  style={{ color: "#007bff" }}
>Ajouter un cours</h4>
            </div>
          </div>
        </div>
        </div>

        <div className="row justify-content-center">
      <div className="col-lg-10 col-md-12">
        <div className="card border-0 shadow rounded-4">
          <div className="card-header bg-warning text-dark rounded-top-4 py-3 px-4">
            <h5 className="mb-0 fw-semibold">📝 Détails du cours</h5>
          </div>
          <div className="card-body p-4 bg-light-subtle">
                {successMessage && (
                  <div className="alert alert-success">{successMessage}</div>
                )}
                {errors.submit && (
                  <div className="alert alert-danger">{errors.submit}</div>
                )}
                <form onSubmit={handleSubmit} className="form-style"> 
                  <div className="row">
                    <div className="col-lg-6 col-md-6 col-sm-12">
                    <div className="mb-3">
                    <label className="form-label fw-medium">Titre</label>
                        <input
                          type="text"
                          name="titre"
                          className={`form-control form-control-lg rounded-3 ${errors.titre ? "is-invalid" : ""}`}
                          value={formData.titre}
                          onChange={handleChange}
                        />
                        {errors.titre && (
                          <div className="invalid-feedback">{errors.titre}</div>
                        )}
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6 col-sm-12">
                    <div className="mb-3">
                    <label className="form-label fw-medium">Durée (minutes)</label>
                        <input
                          type="number"
                          name="duree"
                          className={`form-control form-control-lg rounded-3 ${errors.duree ? "is-invalid" : ""}`}
                          value={formData.duree}
                          onChange={handleChange}
                        />
                        {errors.duree && (
                          <div className="invalid-feedback">{errors.duree}</div>
                        )}
                      </div>
                    </div>
                    <div className="col-lg-12 col-md-12 col-sm-12">
                    <div className="mb-3">
                    <label className="form-label fw-medium">Description</label>
                        <textarea
                          name="description"
                          className={`form-control form-control-lg rounded-3 ${
                            errors.description ? "is-invalid" : ""
                          }`}
                          value={formData.description}
                          onChange={handleChange}
                        ></textarea>
                        {errors.description && (
                          <div className="invalid-feedback">
                            {errors.description}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6 col-sm-12">
                      <div className="form-group">
                        <label className="form-label fw-medium">Chapitre</label>
                        <select
                          name="chapitreId"
                          className={`form-control form-control-lg rounded-3 ${
                            errors.chapitreId ? "is-invalid" : ""
                          }`}
                          value={formData.chapitreId}
                          onChange={handleChange}
                        >
                          <option value="">Sélectionner un chapitre</option>
                          {chapitres.map((chapitre) => (
                            <option key={chapitre.id} value={chapitre.id}>
                              {chapitre.nomChapitre}
                            </option>
                          ))}
                        </select>
                        {errors.chapitreId && (
                          <div className="invalid-feedback">
                            {errors.chapitreId}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6 col-sm-12">
                    <div className="mb-3">
                    <label className="form-label fw-medium">Vidéo du cours</label>
                        <input
                          type="file"
                          accept="video/*"
                          className="form-control form-control-lg rounded-3"
                          onChange={handleVideoChange}
                        />
                      </div>
                    </div>
                    <div className="d-flex justify-content-end gap-3">
                    <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={uploading}
                        style={{
                          backgroundColor: "# #000080",
                          borderColor: "# #000080",
                        }}
                      >
                        {uploading ? "Ajout en cours..." : "Ajouter le cours"}
                      </button>
                      <button type="reset" className="btn btn-outline-secondary px-4">
                  Annuler
                </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
   
  );
};

export default AjouterCours;