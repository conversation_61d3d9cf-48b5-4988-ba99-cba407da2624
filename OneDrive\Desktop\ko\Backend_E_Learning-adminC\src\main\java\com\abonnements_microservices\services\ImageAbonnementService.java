package com.abonnements_microservices.services;

import java.io.IOException;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;
import com.abonnements_microservices.model.ImageAbonnement;

public interface ImageAbonnementService {
	ImageAbonnement uplaodImage(MultipartFile file) throws IOException;

	ImageAbonnement getImageDetails(Long id) throws IOException;

	ResponseEntity<byte[]> getImage(Long id) throws IOException;

	void deleteImage(Long id);

	ImageAbonnement uplaodImageAbonnement(MultipartFile file, Long idAbon) throws IOException;
	//List<Image> getImagesParMatiere(Long matId);

}