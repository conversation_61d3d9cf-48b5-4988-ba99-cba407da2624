import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useKeycloak } from '@react-keycloak/web';

const RedirectToDashboard = () => {
  const { keycloak, initialized } = useKeycloak();
  const navigate = useNavigate();

  useEffect(() => {
    if (initialized) {
      if (keycloak.authenticated) {
        // If already authenticated, redirect to dashboard
        navigate('/dashboard');
      } else {
        // If not authenticated, initiate login with dashboard as the target
        keycloak.login({ redirectUri: window.location.origin + '/dashboard' });
      }
    }
  }, [keycloak, initialized, navigate]);

  // Show a loading indicator while redirecting
  return (
    <div style={{ 
      height: '100vh', 
      width: '100vw', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      backgroundColor: '#f5f5f5'
    }}>
      <div className="text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <div className="mt-2">Redirecting to dashboard...</div>
      </div>
    </div>
  );
};

export default RedirectToDashboard;
