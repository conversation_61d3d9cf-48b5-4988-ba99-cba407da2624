package com.abonnements_microservices.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.*;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Cours {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idCours;

    @Column(nullable = false)
    private String titre;
    
    @Column(nullable = false)
    private String description;
    
    private String lien;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Temporal(TemporalType.DATE)
    private Date dateCreation;
    
    @Column(nullable = false)
    private Long duree;
    
    @Lob
    @Column(columnDefinition = "LONGBLOB")
    private byte[] pdf;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chapitre_id", nullable = false)
    //@JsonIgnoreProperties({"cours", "matiere"})
    @JsonBackReference("chapitre-cours")
    private Chapitre chapitre;
}