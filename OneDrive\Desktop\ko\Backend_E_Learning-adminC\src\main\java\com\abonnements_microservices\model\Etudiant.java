package com.abonnements_microservices.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@Table(name = "etudiant")
@DiscriminatorValue("ETUDIANT")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Etudiant extends User {
    private String phoneNumber;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Temporal(TemporalType.DATE)
    private Date dateNaissance;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "etudiant_abonnements",
        joinColumns = @JoinColumn(name = "etudiant_id"),
        inverseJoinColumns = @JoinColumn(name = "abonnement_id")
    )
    @JsonIgnoreProperties({"etudiants", "matieres", "imageAbonnement"})
    private List<Abonnement> abonnements = new ArrayList<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "niveau_id")
    @JsonIgnoreProperties({"etudiants"})
    private Niveau niveau;

    @Transient
    private Long niveauId;

    @Transient
    private Long abonnementTypeId; // Added for subscription selection during registration

    // Status field for pending/active/rejected states
    private String status;

    // Getter and setter for status
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    // Getter and setter for niveauId (transient field)
    public Long getNiveauId() {
        return niveauId;
    }

    public void setNiveauId(Long niveauId) {
        this.niveauId = niveauId;
    }

    // Getter and setter for abonnementTypeId
    public Long getAbonnementTypeId() {
        return abonnementTypeId;
    }

    public void setAbonnementTypeId(Long abonnementTypeId) {
        this.abonnementTypeId = abonnementTypeId;
    }

    public void addAbonnement(Abonnement abonnement) {
        if (abonnements == null) {
            abonnements = new ArrayList<>();
        }
        if (!abonnements.contains(abonnement)) {
            abonnements.add(abonnement);
        }
    }

    public void removeAbonnement(Abonnement abonnement) {
        if (abonnements != null) {
            abonnements.remove(abonnement);
        }
    }

    /**
     * Check if the student has access to courses based on their subscription type
     * @return true if the student has access to courses
     */
    public boolean hasAccessToCourses() {
        if (abonnements == null || abonnements.isEmpty()) {
            return false;
        }

        // Check if any of the student's subscriptions has a type that allows access to courses
        return abonnements.stream()
                .anyMatch(abonnement -> abonnement.getType() != null && abonnement.getType().isHasCourses());
    }

    /**
     * Check if the student has access to recordings based on their subscription type
     * @return true if the student has access to recordings
     */
    public boolean hasAccessToRecordings() {
        if (abonnements == null || abonnements.isEmpty()) {
            return false;
        }

        // Check if any of the student's subscriptions has a type that allows access to recordings
        return abonnements.stream()
                .anyMatch(abonnement -> abonnement.getType() != null && abonnement.getType().isHasRecordings());
    }

    /**
     * Check if the student has access to live sessions based on their subscription type
     * @return true if the student has access to live sessions
     */
    public boolean hasAccessToLiveSessions() {
        if (abonnements == null || abonnements.isEmpty()) {
            return false;
        }

        // Check if any of the student's subscriptions has a type that allows access to live sessions
        return abonnements.stream()
                .anyMatch(abonnement -> abonnement.getType() != null && abonnement.getType().isHasLiveSessions());
    }

    /**
     * Check if the student has access to a specific matiere based on their subscriptions
     * @param matiereId the ID of the matiere to check
     * @return true if the student has access to the matiere
     */
    public boolean hasAccessToMatiere(Long matiereId) {
        if (abonnements == null || abonnements.isEmpty()) {
            return false;
        }

        // Check if any of the student's subscriptions includes the matiere
        return abonnements.stream()
                .anyMatch(abonnement -> abonnement.getMatieres().stream()
                        .anyMatch(matiere -> matiere.getIdMatiere().equals(matiereId)));
    }


}