package com.abonnements_microservices.services;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.model.Question;
import com.abonnements_microservices.model.Reponse;
import com.abonnements_microservices.model.User;
import com.abonnements_microservices.repo.MatiereRepository;
import com.abonnements_microservices.repo.QuestionRepository;
import com.abonnements_microservices.repo.ReponseRepository;
import com.abonnements_microservices.repo.UserRepository;

import jakarta.persistence.EntityNotFoundException;

@Service
public class QuestionService {

    @Autowired
    private QuestionRepository questionRepository;

    @Autowired
    private ReponseRepository reponseRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private MatiereRepository matiereRepository;

    public Page<Question> getAllQuestions(Pageable pageable) {
        return questionRepository.findAllByOrderByDateCreationDesc(pageable);
    }

    public Page<Question> getQuestionsByUser(Long userId, Pageable pageable) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new EntityNotFoundException("Utilisateur non trouvé avec l'ID: " + userId));
        return questionRepository.findByAuteurOrderByDateCreationDesc(user, pageable);
    }

    public Page<Question> getQuestionsByMatiere(Long matiereId, Pageable pageable) {
        return questionRepository.findByMatiereIdOrderByDateCreationDesc(matiereId, pageable);
    }

    public Page<Question> searchQuestions(String keyword, Pageable pageable) {
        return questionRepository.searchQuestions(keyword, pageable);
    }

    public Question getQuestionById(Long id) {
        Optional<Question> question = questionRepository.findById(id);
        if (question.isPresent()) {
            // Incrémenter le compteur de vues
            Question q = question.get();
            q.setVues(q.getVues() + 1);
            return questionRepository.save(q);
        }
        throw new EntityNotFoundException("Question non trouvée avec l'ID: " + id);
    }

    @Transactional
    public Question createQuestion(Question question, String username, Long matiereId) {
        // Rechercher l'utilisateur par son nom d'utilisateur au lieu de son ID
        User auteur = userRepository.findByUsername(username)
                .orElseThrow(() -> new EntityNotFoundException("Utilisateur non trouvé avec le nom d'utilisateur: " + username));

        Matiere matiere = null;
        if (matiereId != null) {
            matiere = matiereRepository.findById(matiereId)
                    .orElseThrow(() -> new EntityNotFoundException("Matière non trouvée avec l'ID: " + matiereId));
        }

        question.setAuteur(auteur);
        question.setMatiere(matiere);
        question.setDateCreation(new Date());
        question.setVues(0);
        question.setResolu(false);
        question.setReponseAccepteeId(null);

        return questionRepository.save(question);
    }

    @Transactional
    public Question updateQuestion(Long id, Question questionDetails) {
        Question question = questionRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Question non trouvée avec l'ID: " + id));

        question.setTitre(questionDetails.getTitre());
        question.setContenu(questionDetails.getContenu());

        if (questionDetails.getMatiere() != null) {
            Matiere matiere = matiereRepository.findById(questionDetails.getMatiere().getId())
                    .orElseThrow(() -> new EntityNotFoundException("Matière non trouvée avec l'ID: " + questionDetails.getMatiere().getId()));
            question.setMatiere(matiere);
        }

        return questionRepository.save(question);
    }

    @Transactional
    public void deleteQuestion(Long id) {
        Question question = questionRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Question non trouvée avec l'ID: " + id));

        questionRepository.delete(question);
    }

    @Transactional
    public Question marquerCommeResolu(Long questionId, Long reponseId) {
        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new EntityNotFoundException("Question non trouvée avec l'ID: " + questionId));

        Reponse reponse = reponseRepository.findById(reponseId)
                .orElseThrow(() -> new EntityNotFoundException("Réponse non trouvée avec l'ID: " + reponseId));

        // Vérifier que la réponse appartient bien à cette question
        if (!reponse.getQuestion().getId().equals(questionId)) {
            throw new IllegalArgumentException("La réponse n'appartient pas à cette question");
        }

        // Réinitialiser toutes les réponses acceptées pour cette question
        List<Reponse> reponses = reponseRepository.findByQuestionIdOrderByDateCreationAsc(questionId);
        for (Reponse r : reponses) {
            r.setAcceptee(false);
            reponseRepository.save(r);
        }

        // Marquer la réponse comme acceptée
        reponse.setAcceptee(true);
        reponseRepository.save(reponse);

        // Mettre à jour la question
        question.setResolu(true);
        question.setReponseAccepteeId(reponseId);

        return questionRepository.save(question);
    }

    public List<Question> getRecentQuestions() {
        return questionRepository.findTop5ByOrderByDateCreationDesc();
    }

    public List<Question> getPopularQuestions() {
        return questionRepository.findTop5ByOrderByVuesDesc();
    }
}
