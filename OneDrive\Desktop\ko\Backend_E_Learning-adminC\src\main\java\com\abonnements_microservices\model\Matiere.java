package com.abonnements_microservices.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import jakarta.persistence.*;
import lombok.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity

@Getter
@Setter
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@NoArgsConstructor
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
public class Matiere {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@EqualsAndHashCode.Include

	private Long idMatiere;

	private String nomMatiere;
	private String description;
	private Integer duree;

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "matiere_abonnements", joinColumns = @JoinColumn(name = "matiereId"), inverseJoinColumns = @JoinColumn(name = "abonnement_id"))
	@JsonIgnoreProperties("matieres")
	private Set<Abonnement> abonnements = new HashSet<>();

	@OneToMany(mappedBy = "matiere", cascade = CascadeType.ALL, orphanRemoval = true)
	  @JsonManagedReference("matiereNiveau")
	private Set<MatiereNiveau> matiereNiveaux = new HashSet<>();

	@OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
	@JoinColumn(name = "image_id", referencedColumnName = "idImage")
	@JsonIgnoreProperties("matiere")
	private Image image;

	@ManyToMany(mappedBy = "matieres", fetch = FetchType.LAZY)
	@JsonIgnoreProperties({ "matieres", "niveaux", "password" })
	private Set<Enseignant> enseignants = new HashSet<>();

	// ✅ AJOUTE CES GETTERS
	public String getNom() {
		return nomMatiere;
	}

	public void setNom(String nomMatiere) {
		this.nomMatiere = nomMatiere;
	}

	public Long getId() {
		// TODO Auto-generated method stub
		return idMatiere;
	}

	public void addAbonnement(Abonnement abonnement) {
		if (abonnements == null) {
			abonnements = new HashSet<Abonnement>(); // ou TreeSet selon besoin
		}
		abonnements.add(abonnement); // pas besoin de contains() avec un Set
	}

	public void removeAbonnement(Abonnement abonnement) {
		abonnements.remove(abonnement);
	}

	public void addNiveau(Niveau niveau) {
		if (niveau != null) {
			MatiereNiveau matiereNiveau = new MatiereNiveau();
			matiereNiveau.setMatiere(this);
			matiereNiveau.setNiveau(niveau);

			// Initialiser l'ID composite
			MatiereNiveauId id = new MatiereNiveauId(this.getId(), niveau.getId());
			matiereNiveau.setId(id);

			this.matiereNiveaux.add(matiereNiveau);
			niveau.getMatiereNiveaux().add(matiereNiveau);
		}
	}

}
