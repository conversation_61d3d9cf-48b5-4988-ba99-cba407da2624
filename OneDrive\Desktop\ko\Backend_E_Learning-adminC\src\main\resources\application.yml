spring:
  application:
    name: abonnements-microservices
  datasource:
    #url: *********************************************************************
    url: ****************************************************************

    username: root
    password:
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
    hibernate:
      ddl-auto: update
    show-sql: true
    generate-ddl: true
    database-platform: org.hibernate.dialect.MySQLDialect
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB
      max-request-size: 500MB
      file-size-threshold: 2MB
  mvc:
    contentnegotiation:
      favor-parameter: true
      media-types:
        multipart: multipart/form-data  # Explicitly support multipart/form-data
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8080/realms/e-learning-realm
          jwk-set-uri: http://localhost:8080/realms/e-learning-realm/protocol/openid-connect/certs
  jackson:
    serialization:
      fail-on-empty-beans: false
      write-dates-as-timestamps: false
server:
  port: 8084
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

keycloak:
  realm: e-learning-realm
  auth-server-url: http://localhost:8080
  resource: e-learning
  credentials:
    secret: aRjZ2PB2jnt6NVBypwy4dooa9Fx4lBku
  bearer-only: true
  admin:
    username: admin
    password: 0000

vimeo:
  api:
    url: https://api.vimeo.com
  access:
    token: bc62f0f9c6876ae2d656a0c2f9e77728
logging:
  level:
    org.springframework.web: DEBUG
    org.springframework.web.servlet: DEBUG
    org.springframework.http: DEBUG
    org.hibernate: INFO

