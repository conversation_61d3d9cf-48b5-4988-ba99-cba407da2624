# Production Environment Variables

# Domain Configuration
API_DOMAIN=api.yourdomain.com
KEYCLOAK_HOSTNAME=auth.yourdomain.com
KEYCLOAK_PORT=8080

# MySQL Configuration
MYSQL_ROOT_PASSWORD=your_secure_root_password_here
MYSQL_DATABASE=elearning_db
MYSQL_USER=elearning_user
MYSQL_PASSWORD=your_secure_db_password_here

# Keycloak Configuration
KEYCLOAK_ADMIN=admin
KEYCLOAK_ADMIN_PASSWORD=your_secure_admin_password_here
KEYCLOAK_DB_USER=keycloak_user
KEYCLOAK_DB_PASSWORD=your_secure_keycloak_db_password_here
KEYCLOAK_REALM=e-learning-realm
KEYCLOAK_CLIENT_ID=e-learning
KEYCLOAK_CLIENT_SECRET=your_keycloak_client_secret_here

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password_here

# Application Configuration
SPRING_PROFILES_ACTIVE=production

# SSL Configuration (if using custom certificates)
SSL_CERTIFICATE_PATH=./ssl/server.crt
SSL_PRIVATE_KEY_PATH=./ssl/server.key

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Monitoring
ENABLE_MONITORING=true
GRAFANA_ADMIN_PASSWORD=your_grafana_password_here

# Email Configuration (Production SMTP)
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_smtp_password_here

# Security
JWT_SECRET=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here
