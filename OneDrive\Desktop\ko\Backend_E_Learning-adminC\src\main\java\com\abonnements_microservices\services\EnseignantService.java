package com.abonnements_microservices.services;

import com.abonnements_microservices.model.Enseignant;
import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.model.Niveau;
import com.abonnements_microservices.repo.EnseignantRepository;
import com.abonnements_microservices.repo.MatiereRepository;
import com.abonnements_microservices.repo.NiveauRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.EntityNotFoundException;
import java.util.List;
import java.util.Optional;

@Service
public class EnseignantService {
    
    @Autowired
    private EnseignantRepository enseignantRepository;
    
    @Autowired
    private NiveauRepository niveauRepository;
    
    @Autowired
    private MatiereRepository matiereRepository;

    @Autowired
    private KeycloakUserService keycloakUserService;
    
    @Autowired
    private SmsService smsService;
    
    @Autowired
    private EmailService emailService;

    @Transactional
    public Enseignant createEnseignant(Enseignant enseignant, List<Long> niveauIds, List<Long> matiereIds) {
        // Validate input
        if (enseignant.getUsername() == null || enseignant.getEmail() == null) {
            throw new IllegalArgumentException("Username and email are required");
        }

        try {
            // Create user in Keycloak first
            keycloakUserService.createUser(
                enseignant.getUsername(),
                enseignant.getFirstName(),
                enseignant.getLastName(),
                enseignant.getEmail(),
                enseignant.getPassword(),
                "ENSEIGNANT"
            );

            // Save the enseignant entity first to get an ID
            Enseignant savedEnseignant = enseignantRepository.save(enseignant);

            // Set relationships
            if (niveauIds != null) {
                niveauIds.forEach(niveauId -> {
                    Niveau niveau = niveauRepository.findById(niveauId)
                        .orElseThrow(() -> new EntityNotFoundException("Niveau not found with id: " + niveauId));
                    savedEnseignant.addNiveau(niveau);
                });
            }

            if (matiereIds != null) {
                matiereIds.forEach(matiereId -> {
                    Matiere matiere = matiereRepository.findById(matiereId)
                        .orElseThrow(() -> new EntityNotFoundException("Matiere not found with id: " + matiereId));
                    savedEnseignant.addMatiere(matiere);
                });
            }
            
            // Send SMS notification if phone number is provided
            String phoneNumber = enseignant.getPhoneNumber();
            System.out.println("Attempting to send SMS to teacher: " + enseignant.getFirstName());
            System.out.println("Original phone number: " + phoneNumber);
            
            if (phoneNumber != null && !phoneNumber.isEmpty()) {
                boolean isValid = isValidPhoneNumber(phoneNumber);
                System.out.println("Is phone number valid: " + isValid);
                
                if (isValid) {
                    try {
                        String formattedPhone = formatPhoneNumber(phoneNumber);
                        System.out.println("Formatted phone number: " + formattedPhone);
                        System.out.println("Sending SMS to: " + formattedPhone);
                        
                        smsService.sendSms(
                            formattedPhone, 
                            "Bonjour " + enseignant.getFirstName() + ", votre compte enseignant a été créé avec succès ! Vous pouvez maintenant vous connecter à notre plateforme."
                        );
                        System.out.println("SMS sent successfully to teacher");
                    } catch (Exception e) {
                        // Log error but don't fail the entire operation
                        System.err.println("Failed to send SMS to teacher: " + e.getMessage());
                        e.printStackTrace();
                    }
                } else {
                    System.out.println("Teacher's phone number validation failed - skipping SMS");
                }
            } else {
                System.out.println("Teacher's phone number is null or empty - skipping SMS");
            }
            
            // Send welcome email with credentials
            try {
                System.out.println("Sending welcome email to teacher: " + enseignant.getEmail());
                emailService.sendCredentialsEmail(
                    enseignant.getEmail(),
                    enseignant.getFirstName() + " " + enseignant.getLastName(),
                    enseignant.getUsername(),
                    enseignant.getPassword()
                );
                System.out.println("Welcome email sent successfully to teacher");
            } catch (Exception e) {
                System.err.println("Failed to send welcome email to teacher: " + e.getMessage());
                e.printStackTrace();
            }

            return enseignantRepository.save(savedEnseignant);
        } catch (Exception e) {
            // Handle any errors and rollback
            throw new RuntimeException("Failed to create enseignant: " + e.getMessage(), e);
        }
    }

    public List<Enseignant> getAllEnseignants() {
        return enseignantRepository.findAll();
    }

    public Optional<Enseignant> getEnseignantById(Long id) {
        return enseignantRepository.findById(id);
    }

    @Transactional
    public Enseignant updateEnseignant(Long id, Enseignant enseignant, List<Long> niveauIds, List<Long> matiereIds) {
        Enseignant existingEnseignant = enseignantRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Enseignant not found with id: " + id));

        // Update basic info
        if (enseignant.getFirstName() != null) {
            existingEnseignant.setFirstName(enseignant.getFirstName());
        }
        if (enseignant.getLastName() != null) {
            existingEnseignant.setLastName(enseignant.getLastName());
        }
        if (enseignant.getEmail() != null) {
            existingEnseignant.setEmail(enseignant.getEmail());
        }

        // Update niveaux if provided
        if (niveauIds != null) {
            existingEnseignant.getNiveaux().clear();
            niveauIds.forEach(niveauId -> {
                Niveau niveau = niveauRepository.findById(niveauId)
                    .orElseThrow(() -> new EntityNotFoundException("Niveau not found with id: " + niveauId));
                existingEnseignant.getNiveaux().add(niveau);
            });
        }

        // Update matieres if provided
        if (matiereIds != null) {
            existingEnseignant.getMatieres().clear();
            matiereIds.forEach(matiereId -> {
                Matiere matiere = matiereRepository.findById(matiereId)
                    .orElseThrow(() -> new EntityNotFoundException("Matiere not found with id: " + matiereId));
                existingEnseignant.getMatieres().add(matiere);
            });
        }

        return enseignantRepository.save(existingEnseignant);
    }

    @Transactional
    public void deleteEnseignant(Long id) {
        enseignantRepository.deleteById(id);
    }

    public List<Matiere> getMatieresByEnseignantId(Long enseignantId) {
        Enseignant enseignant = enseignantRepository.findById(enseignantId)
                .orElseThrow(() -> new EntityNotFoundException("Enseignant non trouvé avec id " + enseignantId));
        return enseignant.getMatieres();
    }
    
    public Optional<Enseignant> getEnseignantByUsername(String username) {
        return enseignantRepository.findByUsername(username);
    }
    
    public Optional<Enseignant> getEnseignantByEmail(String email) {
        return enseignantRepository.findByEmail(email);
    }
    

    
    public List<Niveau> getNiveauByEnseignantId(Long enseignantId) {
        Enseignant enseignant = enseignantRepository.findById(enseignantId)
                .orElseThrow(() -> new EntityNotFoundException("Enseignant non trouvé avec id " + enseignantId));
        return enseignant.getNiveaux();
    }
    
    /**
     * Validates if the phone number is in a valid format for SMS sending
     * @param phoneNumber The phone number to validate
     * @return true if the phone number is valid, false otherwise
     */
    private boolean isValidPhoneNumber(String phoneNumber) {
        // Basic validation - can be enhanced based on your requirements
        // This checks if the phone number has at least 8 digits
        return phoneNumber != null && 
               phoneNumber.replaceAll("[^0-9]", "").length() >= 8;
    }
    
    /**
     * Formats the phone number to E.164 format for SMS service
     * @param phoneNumber The phone number to format
     * @return The formatted phone number
     */
    private String formatPhoneNumber(String phoneNumber) {
        // Strip all non-numeric characters
        String digitsOnly = phoneNumber.replaceAll("[^0-9]", "");
        
        // Handle Tunisian numbers (country code +216)
        if (phoneNumber.startsWith("+216")) {
            // Number already has country code with + prefix
            return phoneNumber;
        } else if (phoneNumber.startsWith("216")) {
            // Number has country code without + prefix
            return "+" + phoneNumber;
        } else if (digitsOnly.length() == 8) {
            // 8-digit Tunisian number without country code
            return "+216" + digitsOnly;
        } else {
            // Generic case - just add + if missing
            if (!phoneNumber.startsWith("+")) {
                return "+" + digitsOnly;
            }
            return "+" + digitsOnly;
        }
    }
}