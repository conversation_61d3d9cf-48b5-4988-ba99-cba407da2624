import React, { Fragment, useEffect } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import Navbar from '../../components/Navbar/Navbar';
import PageTitle from '../../components/pagetitle/PageTitle';
import AbonnementDetail from '../../components/AbonnementDetail/AbonnementDetail';
import Footer from '../../components/footer/Footer';
import Scrollbar from '../../components/scrollbar/scrollbar';

const AbonnementDetailPage = () => {
    const { id } = useParams();
    const location = useLocation();

    useEffect(() => {
        console.log("AbonnementDetailPage - ID:", id);
        console.log("AbonnementDetailPage - Location:", location);
    }, [id, location]);

    return (
        <Fragment>
            <Navbar />
            <PageTitle pageTitle={`Détails de l'abonnement ${id}`} pagesub={'Abonnement'} />
            <AbonnementDetail />
            <Footer />
            <Scrollbar />
        </Fragment>
    );
};

export default AbonnementDetailPage;
