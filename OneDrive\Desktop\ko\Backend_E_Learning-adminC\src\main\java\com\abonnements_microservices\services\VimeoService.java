package com.abonnements_microservices.services;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.IOException;
import java.util.Map;

@Service
public class VimeoService {
    private final WebClient webClient;

    public VimeoService(WebClient.Builder webClientBuilder, @Value("${vimeo.access.token}") String token) {
        this.webClient = webClientBuilder.baseUrl("https://api.vimeo.com")
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                .defaultHeader("Accept", "application/vnd.vimeo.*+json;version=3.4")
                .defaultHeader(HttpHeaders.CONTENT_TYPE, "application/json")
                .build();
    }

    public String uploadVideo(MultipartFile file) throws IOException {
        return uploadVideo(file, file.getOriginalFilename());
    }
    
    public String uploadVideo(MultipartFile file, String title) throws IOException {
        Map<String, Object> uploadData = Map.of(
                "upload", Map.of("approach", "tus", "size", file.getSize()),
                "name", title != null ? title : file.getOriginalFilename(),
                "privacy", Map.of("view", "anybody"),
                "description", "Recorded live session"
        );

        Map<String, Object> response = webClient.post()
                .uri("/me/videos")
                .bodyValue(uploadData)
                .retrieve()
                .bodyToMono(new org.springframework.core.ParameterizedTypeReference<Map<String, Object>>() {})
                .block();

        // Safe cast with null check
        String uploadUrl = null;
        if (response != null && response.get("upload") instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, String> uploadMap = (Map<String, String>) response.get("upload");
            uploadUrl = uploadMap.get("upload_link");
        }
        
        if (uploadUrl == null) {
            throw new IOException("Failed to get upload link from Vimeo");
        }

        webClient.patch()
                .uri(uploadUrl)
                .header("Tus-Resumable", "1.0.0")
                .header("Upload-Offset", "0")
                .header(HttpHeaders.CONTENT_TYPE, "application/offset+octet-stream")
                .bodyValue(file.getBytes())
                .retrieve()
                .toBodilessEntity()
                .block();

        return response.get("uri").toString();
    }
}