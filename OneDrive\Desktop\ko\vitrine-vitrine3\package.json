{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "homepage": "http://eduko-react.wpolive.com", "private": true, "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/material": "^5.11.0", "@mui/styled-engine-sc": "^5.11.0", "@react-keycloak/web": "^3.4.0", "axios": "^1.8.4", "bootstrap": "^5.1.3", "keycloak-js": "^26.1.0", "memfs": "^4.6.0", "react": "^18.0.0", "react-anchor-link-smooth-scroll": "^1.0.12", "react-awesome-reveal": "^4.2.5", "react-countup": "^6.4.2", "react-datepicker": "^4.20.0", "react-dom": "^18.0.0", "react-medium-image-zoom": "^5.1.5", "react-modal-video": "^2.0.1", "react-redux": "^8.1.3", "react-responsive-masonry": "^2.1.7", "react-router-dom": "^6.3.0", "react-scripts": "^5.0.1", "react-scroll": "^1.9.0", "react-scroll-parallax": "^3.4.2", "react-slick": "^0.29.0", "react-toastify": "^9.1.2", "react-tooltip": "^5.21.5", "reactstrap": "^9.1.9", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "sass": "^1.62.1", "simple-react-validator": "^1.6.2", "slick-carousel": "^1.8.1", "swiper": "^9.3.0", "web-vitals": "^2.1.4", "yet-another-react-lightbox": "^3.12.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}