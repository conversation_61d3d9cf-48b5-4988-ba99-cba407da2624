import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosService";
import Select from "react-select";

const AjoutEtud = () => {
  const navigate = useNavigate();
  const [abonnements, setAbonnements] = useState([]);
  const [formData, setFormData] = useState({
    username: "",
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    phoneNumber: "",
    dateNaissance: "",
    abonnementIds: [], // Un tableau pour plusieurs abonnements

  });
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    fetchAbonnements();
  }, []);

  const fetchAbonnements = async () => {
    try {
      const response = await axiosInstance.get("/api/abonnements/all");
      setAbonnements(response.data);
    } catch (err) {
      console.error("Error fetching abonnements:", err);
      setError("Erreur lors du chargement des abonnements");
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name === "abonnementIds") {
      setFormData((prev) => ({
        ...prev,
        [name]: Array.from(e.target.selectedOptions, (option) =>
          Number(option.value)
        ),
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };
  const handleSelectChange = (selectedOptions, field) => {
    const selectedValues = selectedOptions
      ? selectedOptions.map((option) => option.value)
      : [];
    setFormData((prev) => ({
      ...prev,
      [field]: selectedValues,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await axiosInstance.post("/api/etudiants", formData);
      setSuccess(true);
      setError(null);

      // Reset form
      setFormData({
        username: "",
        firstName: "",
        lastName: "",
        email: "",
        password: "",
        phoneNumber: "",
        dateNaissance: "",
        niveauId: "",

        abonnementIds: [],
      });

      // Redirect after successful creation
      setTimeout(() => {
        navigate("/afficher-etudiant");
      }, 2000);
    } catch (err) {
      console.error("Error creating student:", err);
      setError(
        err.response?.data?.message ||
          "Erreur lors de la création de l'étudiant"
      );
      setSuccess(false);
    }
  };

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0">
        <div className="col-sm-6 p-md-0">
          <div className="welcome-text">
            <h4>Ajouter un étudiant</h4>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-xl-12 col-xxl-12">
          <div className="card">
            <div className="card-body">
              {error && (
                <div className="alert alert-danger" role="alert">
                  {error}
                </div>
              )}
              {success && (
                <div className="alert alert-success" role="alert">
                  Étudiant créé avec succès!
                </div>
              )}
              <form onSubmit={handleSubmit}>
                <div className="row">
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">Login de l'étudiant*</label>
                      <input
                        type="text"
                        name="username"
                        className="form-control"
                        placeholder="Entrez le login"
                        value={formData.username}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">Mot de passe*</label>
                      <input
                        type="password"
                        name="password"
                        className="form-control"
                        placeholder="Entrez le mot de passe"
                        value={formData.password}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">Prénom*</label>
                      <input
                        type="text"
                        name="firstName"
                        className="form-control"
                        placeholder="Prénom de l'étudiant"
                        value={formData.firstName}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">Nom*</label>
                      <input
                        type="text"
                        name="lastName"
                        className="form-control"
                        placeholder="Nom de l'étudiant"
                        value={formData.lastName}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">Email*</label>
                      <input
                        type="email"
                        name="email"
                        className="form-control"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">Téléphone*</label>
                      <input
                        type="tel"
                        name="phoneNumber"
                        className="form-control"
                        placeholder="Numéro de téléphone"
                        value={formData.phoneNumber}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">Abonnements*</label>
                      <Select
                        isMulti
                        value={formData.abonnementIds.map(
                          (id) =>
                            abonnements.find(
                              (abonnement) => abonnement.id === id
                            ) && {
                              value: id,
                              label: abonnements.find(
                                (abonnement) => abonnement.id === id
                              ).nom,
                            }
                        )}
                        options={abonnements.map((abonnement) => ({
                          value: abonnement.id,
                          label: `${abonnement.nom} - ${abonnement.prix} DT`,
                        }))}
                        onChange={(selectedOptions) =>
                          handleSelectChange(selectedOptions, "abonnementIds")
                        }
                        placeholder="Sélectionner plusieurs abonnements"
                        styles={{
                          control: (provided) => ({
                            ...provided,
                            width: "210%",
                            borderColor: "#ccc",
                            borderRadius: "4px",
                            padding: "5px",
                          }),
                          menu: (provided) => ({
                            ...provided,
                            borderRadius: "4px",
                            boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                          }),
                        }}
                      />
                    </div>
                  </div>
                </div>
                <button
                  type="submit"
                  className="btn btn-primary"
                  style={{ backgroundColor: "#37A7DF", borderColor: "#37A7DF" }}
                >
                  Ajouter l'étudiant
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AjoutEtud;
