import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { useKeycloak } from "@react-keycloak/web";
import { <PERSON><PERSON>, Spinner } from "react-bootstrap";
import abonnementTypeService from "../../services/abonnementTypeService";

const AjoutAbonnement = () => {
  const API_ABONNEMENTS = "http://localhost:8084/api/abonnements"; // API Abonnements
  const API_Images = "http://localhost:8084/api/imageAbonnement";

  const navigate = useNavigate();
  const { keycloak } = useKeycloak();
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [subscriptionTypes, setSubscriptionTypes] = useState([]);
  const [loadingTypes, setLoadingTypes] = useState(true);
  const [typeError, setTypeError] = useState(null);

  const [formData, setFormData] = useState({
    nom: "",
    description: "",
    prix: "",
    duree: "",
    image: null,
    typeId: "",
  });

  // Vérifie si l'utilisateur a le rôle 'ADMIN'
  const isAdmin = keycloak.tokenParsed?.realm_access?.roles.includes("ADMIN");

  // Fetch subscription types on component mount
  useEffect(() => {
    const fetchSubscriptionTypes = async () => {
      try {
        setLoadingTypes(true);
        const types = await abonnementTypeService.getAllTypes();
        setSubscriptionTypes(types);
        setTypeError(null);
      } catch (err) {
        console.error("Error fetching subscription types:", err);
        setTypeError("Erreur lors du chargement des types d'abonnement");
      } finally {
        setLoadingTypes(false);
      }
    };

    fetchSubscriptionTypes();
  }, []);
  // Fonction pour gérer les changements dans le formulaire
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  // Fonction pour gérer le changement du fichier photo
  const handleFileChange = (e) => {
    setFormData({ ...formData, image: e.target.files[0] });
  };
  const validateForm = () => {
    let newErrors = {};

    if (!formData.nom.trim()) newErrors.nom = "Le nom est requis";
    if (!formData.description.trim())
      newErrors.description = "Description requise";
    if (!formData.prix.trim() || isNaN(Number(formData.prix)))
      newErrors.prix = "Prix invalide";
    if (!formData.duree.trim() || isNaN(Number(formData.duree)))
      newErrors.duree = "Durée invalide";
    if (!formData.typeId)
      newErrors.typeId = "Le type d'abonnement est requis";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);

    try {
      await keycloak.updateToken(5);
      const token = keycloak.token;

      // Création du FormData
      const requestFormData = new FormData();

      // Ajout des données JSON
      requestFormData.append('abonnement', JSON.stringify({
        nom: formData.nom,
        description: formData.description,
        prix: parseFloat(formData.prix),
        duree: parseInt(formData.duree),
        typeId: parseInt(formData.typeId)
      }));

      // Ajout de l'image si elle existe
      if (formData.image) {
        requestFormData.append('image', formData.image);
      }

      // Envoi de la requête
      const response = await axios.post(`${API_ABONNEMENTS}/add`, requestFormData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        }
      });

      setSuccessMessage("Abonnement ajouté avec succès !");
      setTimeout(() => navigate("/Affichage"), 2000);

    } catch (error) {
      console.error("Erreur:", error.response?.data || error.message);
      setErrors({
        general: error.response?.data?.message ||
                "Erreur lors de l'ajout de l'abonnement"
      });
    } finally {
      setLoading(false);
    }
  };
  return (
    <div className="container-fluid py-4">

    {/* Titre de la page */}
    <div className="row justify-content-center">
      <div className="col-lg-10 col-md-12">
        <div className="row page-titles mx-0 mb-4">
          <div className="col">
            <h4
              className="title-section text-uppercase fw-bold mb-0"
              style={{ color: "#007bff" }}
            >
              Ajouter Abonnement
            </h4>
          </div>
        </div>
      </div>
    </div>

    {/* Messages */}
    {successMessage && (
      <div className="row justify-content-center mb-4">
        <div className="col-lg-10 col-md-12">
          <Alert variant="success">{successMessage}</Alert>
        </div>
      </div>
    )}

    {errors.general && (
      <div className="row justify-content-center mb-4">
        <div className="col-lg-10 col-md-12">
          <Alert variant="danger">{errors.general}</Alert>
        </div>
      </div>
    )}

    {/* Formulaire */}
    <div className="row justify-content-center">
      <div className="col-lg-10 col-md-12">
        <div className="card border-0 shadow rounded-4">
          <div className="card-header bg-warning text-dark rounded-top-4 py-3 px-4">
            <h5 className="mb-0 fw-semibold">📝 Détails de l'abonnement</h5>
          </div>

          <div className="card-body p-4 bg-light-subtle">
            <form onSubmit={handleSubmit} className="form-style">
              {/* Nom */}
              <div className="mb-3">
                <label className="form-label fw-medium">Nom de l'abonnement</label>
                <input
                  type="text"
                  name="nom"
                  className={`form-control form-control-lg rounded-3 ${errors.nom ? 'is-invalid' : ''}`}
                  required
                  value={formData.nom}
                  onChange={handleChange}
                />
                {errors.nom && <div className="invalid-feedback">{errors.nom}</div>}
              </div>

              {/* Description */}
              <div className="mb-3">
                <label className="form-label fw-medium">Description</label>
                <input
                  type="text"
                  name="description"
                  className={`form-control form-control-lg rounded-3 ${errors.description ? 'is-invalid' : ''}`}
                  required
                  value={formData.description}
                  onChange={handleChange}
                />
                {errors.description && <div className="invalid-feedback">{errors.description}</div>}
              </div>

              {/* Prix */}
              <div className="mb-3">
                <label className="form-label fw-medium">Prix (DT)</label>
                <input
                  type="number"
                  name="prix"
                  className={`form-control form-control-lg rounded-3 ${errors.prix ? 'is-invalid' : ''}`}
                  required
                  value={formData.prix}
                  onChange={handleChange}
                />
                {errors.prix && <div className="invalid-feedback">{errors.prix}</div>}
              </div>

              {/* Durée */}
              <div className="mb-3">
                <label className="form-label fw-medium">Durée (mois)</label>
                <input
                  type="number"
                  name="duree"
                  className={`form-control form-control-lg rounded-3 ${errors.duree ? 'is-invalid' : ''}`}
                  required
                  value={formData.duree}
                  onChange={handleChange}
                />
                {errors.duree && <div className="invalid-feedback">{errors.duree}</div>}
              </div>

              {/* Type d'abonnement */}
              <div className="mb-3">
                <label className="form-label fw-medium">Type d'abonnement</label>
                {loadingTypes ? (
                  <div className="d-flex align-items-center">
                    <Spinner animation="border" size="sm" className="me-2" />
                    <span>Chargement des types d'abonnement...</span>
                  </div>
                ) : typeError ? (
                  <div className="text-danger">{typeError}</div>
                ) : (
                  <select
                    name="typeId"
                    className={`form-select form-select-lg rounded-3 ${errors.typeId ? 'is-invalid' : ''}`}
                    required
                    value={formData.typeId}
                    onChange={handleChange}
                  >
                    <option value="">Sélectionnez un type d'abonnement</option>
                    {subscriptionTypes.map(type => (
                      <option key={type.id} value={type.id}>
                        {type.nom} - {type.description}
                      </option>
                    ))}
                  </select>
                )}
                {errors.typeId && <div className="invalid-feedback">{errors.typeId}</div>}

                {formData.typeId && subscriptionTypes.length > 0 && (
                  <div className="mt-2 p-3 border rounded bg-light">
                    <h6>Caractéristiques du type sélectionné:</h6>
                    {subscriptionTypes.filter(type => type.id == formData.typeId).map(type => (
                      <div key={type.id} className="features-list">
                        <div className={`feature-badge ${type.hasCourses ? 'text-success' : 'text-danger'}`}>
                          <i className={`fa ${type.hasCourses ? 'fa-check' : 'fa-times'} me-2`}></i>
                          Cours: {type.hasCourses ? 'Oui' : 'Non'}
                        </div>
                        <div className={`feature-badge ${type.hasRecordings ? 'text-success' : 'text-danger'}`}>
                          <i className={`fa ${type.hasRecordings ? 'fa-check' : 'fa-times'} me-2`}></i>
                          Enregistrements: {type.hasRecordings ? 'Oui' : 'Non'}
                        </div>
                        <div className={`feature-badge ${type.hasLiveSessions ? 'text-success' : 'text-danger'}`}>
                          <i className={`fa ${type.hasLiveSessions ? 'fa-check' : 'fa-times'} me-2`}></i>
                          Sessions Live: {type.hasLiveSessions ? 'Oui' : 'Non'}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Image */}
              <div className="mb-4">
                <label className="form-label fw-medium">Photo</label>
                <input
                  type="file"
                  name="image"
                  className="form-control form-control-lg rounded-3"
                  required
                  onChange={handleFileChange}
                />
              </div>

              {/* Boutons */}
              <div className="d-flex justify-content-end gap-3">
                <button
                  type="reset"
                  className="btn btn-outline-secondary px-4"
                  disabled={loading}
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="btn btn-dark px-4"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" />
                      <span className="ms-2">Ajout en cours...</span>
                    </>
                  ) : (
                    "Ajouter"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  );
};

export default AjoutAbonnement;
