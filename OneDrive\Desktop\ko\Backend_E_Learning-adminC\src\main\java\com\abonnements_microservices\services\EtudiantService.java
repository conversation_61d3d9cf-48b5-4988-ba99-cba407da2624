package com.abonnements_microservices.services;

import com.abonnements_microservices.model.Abonnement;
import com.abonnements_microservices.model.Enseignant;
import com.abonnements_microservices.model.Etudiant;
import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.model.Niveau;
import com.abonnements_microservices.model.User;
import com.abonnements_microservices.repo.AbonnementRepository;
import com.abonnements_microservices.repo.EtudiantRepository;
import com.abonnements_microservices.repo.NiveauRepository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.PersistenceContext;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class EtudiantService {
    
    @Autowired
    private EtudiantRepository etudiantRepository;    
    @Autowired
    private AbonnementRepository abonnementRepository;
    @Autowired
    private KeycloakUserService keycloakUserService;
    @Autowired
    private SmsService smsService;
    
    @Autowired
    private EmailService emailService;

    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private NiveauRepository niveauRepository;

    @Transactional
    public Etudiant createEtudiant(Etudiant etudiant, List<Long> abonnementIds, Long niveauId) {
        if (etudiant.getUsername() == null || etudiant.getEmail() == null) {
            throw new IllegalArgumentException("Username and email are required");
        }

        try {
            // Création dans Keycloak
            keycloakUserService.createUser(
                etudiant.getUsername(),
                etudiant.getFirstName(),
                etudiant.getLastName(),
                etudiant.getEmail(),
                etudiant.getPassword(),
                "ETUDIANT"
            );

            // Associer le niveau si fourni
            if (niveauId != null) {
                Niveau niveau = niveauRepository.findById(niveauId)
                    .orElseThrow(() -> new EntityNotFoundException("Niveau non trouvé avec id: " + niveauId));
                etudiant.setNiveau(niveau);
            }

            final Etudiant savedEtudiant = etudiantRepository.save(etudiant);

            // Gestion des abonnements
            if (abonnementIds != null) {
                abonnementIds.forEach(abonnementId -> {
                    Abonnement abonnement = abonnementRepository.findById(abonnementId)
                        .orElseThrow(() -> new EntityNotFoundException("Abonnement non trouvé avec id: " + abonnementId));
                    savedEtudiant.addAbonnement(abonnement);
                });
                // Envoi SMS with enhanced logging
                System.out.println("Attempting to send SMS to student: " + etudiant.getFirstName());
                String phoneNumber = etudiant.getPhoneNumber();
                System.out.println("Original phone number: " + phoneNumber);
                
                if (phoneNumber == null || phoneNumber.isEmpty()) {
                    System.out.println("Phone number is null or empty - skipping SMS");
                } else {
                    boolean isValid = isValidPhoneNumber(phoneNumber);
                    System.out.println("Is phone number valid: " + isValid);
                    
                    if (isValid) {
                        try {
                            String formattedPhone = formatPhoneNumber(phoneNumber);
                            System.out.println("Formatted phone number: " + formattedPhone);
                            System.out.println("Sending SMS to: " + formattedPhone);
                            
                            smsService.sendSms(
                                formattedPhone, "Bonjour " + etudiant.getFirstName() + ", votre compte a été créé avec succès !"
                            );
                            System.out.println("SMS sent successfully");
                        } catch (Exception e) {
                            // Log error but don't fail the entire operation
                            System.err.println("Failed to send SMS: " + e.getMessage());
                            e.printStackTrace();
                        }
                    } else {
                        System.out.println("Phone number validation failed - skipping SMS");
                    }
                }
                
                // Send welcome email with credentials
                try {
                    System.out.println("Sending welcome email to: " + etudiant.getEmail());
                    emailService.sendCredentialsEmail(
                        etudiant.getEmail(),
                        etudiant.getFirstName() + " " + etudiant.getLastName(),
                        etudiant.getUsername(),
                        etudiant.getPassword()
                    );
                    System.out.println("Welcome email sent successfully");
                } catch (Exception e) {
                    System.err.println("Failed to send welcome email: " + e.getMessage());
                    e.printStackTrace();
                }
                return etudiantRepository.save(savedEtudiant);
            }

            return savedEtudiant;
        } catch (Exception e) {
            throw new RuntimeException("Échec de la création de l'étudiant : " + e.getMessage(), e);
        }
    }

    public List<Etudiant> getAllEtudiants() {
        List<Etudiant> etudiants = etudiantRepository.findAll();
        etudiants.forEach(etudiant -> {
            Hibernate.initialize(etudiant.getAbonnements());
        });
        return etudiants;
    }

    public Optional<Etudiant> getEtudiantById(Long id) {
        return etudiantRepository.findById(id)
            .map(etudiant -> {
                Hibernate.initialize(etudiant.getAbonnements());
                return etudiant;
            });
    }

    @Transactional
    public Etudiant updateEtudiant(Long id, Etudiant etudiant, List<Long> abonnementIds, Long niveauId) {
        Etudiant existingEtudiant = etudiantRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Étudiant non trouvé avec id: " + id));

        // Mise à jour des infos de base
        if (etudiant.getFirstName() != null) {
            existingEtudiant.setFirstName(etudiant.getFirstName());
        }
        if (etudiant.getLastName() != null) {
            existingEtudiant.setLastName(etudiant.getLastName());
        }
        if (etudiant.getEmail() != null) {
            existingEtudiant.setEmail(etudiant.getEmail());
        }
        if (etudiant.getPhoneNumber() != null) {
            existingEtudiant.setPhoneNumber(etudiant.getPhoneNumber());
        }
        if (etudiant.getDateNaissance() != null) {
            existingEtudiant.setDateNaissance(etudiant.getDateNaissance());
        }

        // Mise à jour du niveau
        if (niveauId != null) {
            Niveau niveau = niveauRepository.findById(niveauId)
                .orElseThrow(() -> new EntityNotFoundException("Niveau non trouvé avec id: " + niveauId));
            existingEtudiant.setNiveau(niveau);
        }

        // Mise à jour des abonnements
        if (abonnementIds != null) {
            existingEtudiant.getAbonnements().clear();
            abonnementIds.forEach(abonnementId -> {
                Abonnement abonnement = abonnementRepository.findById(abonnementId)
                    .orElseThrow(() -> new EntityNotFoundException("Abonnement non trouvé avec id: " + abonnementId));
                existingEtudiant.addAbonnement(abonnement);
            });
        }

        return etudiantRepository.save(existingEtudiant);
    }


    @Transactional
    public void deleteEtudiant(Long id) {
        Etudiant etudiant = entityManager.find(Etudiant.class, id);
        if (etudiant != null) {
            // Remove all abonnement associations
            etudiant.getAbonnements().forEach(abonnement -> 
                etudiant.removeAbonnement(abonnement)
            );
            
            // Remove the etudiant first
            entityManager.remove(etudiant);
            
            // Then remove the base user
            User user = entityManager.find(User.class, id);
            if (user != null) {
                entityManager.remove(user);
            }
        }
    }

    public List<Etudiant> searchEtudiants(String query) {
        return etudiantRepository.findByFirstNameContainingIgnoreCaseOrLastNameContainingIgnoreCase(query, query);
    }
    public List<Abonnement> getAbonnementByEtudiantId(Long etudiantId) {
        Etudiant etudiant = etudiantRepository.findById(etudiantId)
                .orElseThrow(() -> new EntityNotFoundException("Etudiant non trouvé avec id " +etudiantId));
        return etudiant.getAbonnements();
    }
   
    public List<Abonnement> getAbonnementByUsername(String username) {
        Etudiant etudiant = etudiantRepository.findByUsername(username)
            .orElseThrow(() -> new EntityNotFoundException("Étudiant non trouvé"));
        return etudiant.getAbonnements();
    }

     /**
     * Validates if the phone number is in a valid format for Twilio
     * @param phoneNumber The phone number to validate
     * @return true if the phone number is valid, false otherwise
     */
    private boolean isValidPhoneNumber(String phoneNumber) {
        // Basic validation - can be enhanced based on your requirements
        // This checks if the phone number has at least 8 digits
        return phoneNumber != null && 
               phoneNumber.replaceAll("[^0-9]", "").length() >= 8;
    }
    
    /**
     * Formats the phone number to E.164 format for Twilio
     * @param phoneNumber The phone number to format
     * @return The formatted phone number
     */
    private String formatPhoneNumber(String phoneNumber) {
        // Strip all non-numeric characters
        String digitsOnly = phoneNumber.replaceAll("[^0-9]", "");
        
        // Handle Tunisian numbers (country code +216)
        if (phoneNumber.startsWith("+216")) {
            // Number already has country code with + prefix
            return phoneNumber;
        } else if (phoneNumber.startsWith("216")) {
            // Number has country code without + prefix
            return "+" + phoneNumber;
        } else if (digitsOnly.length() == 8) {
            // 8-digit Tunisian number without country code
            return "+216" + digitsOnly;
        } else {
            // Generic case - just add + if missing
            if (!phoneNumber.startsWith("+")) {
                return "+" + digitsOnly;
            }
            return "+" + digitsOnly;
        }
    }

    /**
     * Creates a student account without sending welcome notifications
     * Used for the initial registration when status is PENDING
     */
    @Transactional
    public Etudiant createEtudiantWithoutNotification(Etudiant etudiant, List<Long> abonnementIds) {
        if (etudiant.getUsername() == null || etudiant.getEmail() == null) {
            throw new IllegalArgumentException("Username and email are required");
        }
        try {
            // Create the Keycloak user with a temporary status
            keycloakUserService.createUser(
                etudiant.getUsername(),
                etudiant.getFirstName(),
                etudiant.getLastName(),
                etudiant.getEmail(),
                etudiant.getPassword(),
                "PENDING_ETUDIANT"  // Special role for pending accounts
            );
            
            // Associate the niveau if provided
            if (etudiant.getNiveauId() != null && etudiant.getNiveau() == null) {
                Niveau niveau = niveauRepository.findById(etudiant.getNiveauId())
                    .orElseThrow(() -> new EntityNotFoundException("Niveau not found with id: " + etudiant.getNiveauId()));
                etudiant.setNiveau(niveau);
            }
            
            // Save the student first to get the ID
            Etudiant savedEtudiant = etudiantRepository.save(etudiant);
            
            // Associate the abonnements if provided
            if (abonnementIds != null && !abonnementIds.isEmpty()) {
                for (Long abonnementId : abonnementIds) {
                    Abonnement abonnement = abonnementRepository.findById(abonnementId)
                        .orElseThrow(() -> new EntityNotFoundException("Abonnement not found with id: " + abonnementId));
                    savedEtudiant.addAbonnement(abonnement);
                }
                // Save again with the abonnements
                savedEtudiant = etudiantRepository.save(savedEtudiant);
            }
            
            return savedEtudiant;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create student account: " + e.getMessage(), e);
        }
    }

    /**
     * Sends welcome notifications (email and SMS) to a student after approval
     */
    @Transactional
    public void sendWelcomeNotifications(Etudiant etudiant) {
        try {
            // Update Keycloak role to full student
            keycloakUserService.updateUserRole(etudiant.getUsername(), "ETUDIANT");
            
            // Send welcome email
            String emailSubject = "Bienvenue sur notre plateforme d'apprentissage";
            String emailBody = "Bonjour " + etudiant.getFirstName() + ",\n\n" +
                "Votre compte a été approuvé. Vous pouvez maintenant vous connecter avec les identifiants suivants:\n" +
                "Nom d'utilisateur: " + etudiant.getUsername() + "\n" +
                "Mot de passe: (celui que vous avez défini lors de l'inscription)\n\n" +
                "Cordialement,\n" +
                "L'équipe éducative";
            
            emailService.sendEmail(etudiant.getEmail(), emailSubject, emailBody);
            System.out.println("Welcome email sent to: " + etudiant.getEmail());
            
            // Send welcome SMS if phone number is valid
            String phoneNumber = etudiant.getPhoneNumber();
            if (phoneNumber != null && !phoneNumber.isEmpty() && isValidPhoneNumber(phoneNumber)) {
                try {
                    String formattedPhone = formatPhoneNumber(phoneNumber);
                    smsService.sendSms(
                        formattedPhone, 
                        "Félicitations " + etudiant.getFirstName() + "! Votre compte a été approuvé. Vous pouvez maintenant vous connecter à notre plateforme."
                    );
                    System.out.println("Welcome SMS sent to: " + formattedPhone);
                } catch (Exception e) {
                    System.err.println("Failed to send welcome SMS: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to send welcome notifications: " + e.getMessage(), e);
        }
    }
}