package com.abonnements_microservices.controller;

import org.springframework.beans.factory.annotation.Autowired; 

import org.springframework.http.ResponseEntity; 
import org.springframework.web.bind.annotation.*; 
import org.springframework.web.multipart.MultipartFile; 
import com.abonnements_microservices.model.Image;
import com.abonnements_microservices.model.ImageAbonnement;
import com.abonnements_microservices.services.ImageAbonnementService;
import com.abonnements_microservices.services.ImageService; 
import java.io.IOException; 
 
@RestController 
@RequestMapping("/api/imageAbonnement") 
@CrossOrigin(origins = "*")
public class ImageAbonnementRestController { 
	 
    @Autowired 
    private ImageAbonnementService imageAbonnementService;
     
    @PostMapping("/upload")
    public ImageAbonnement uploadImage(@RequestParam("image") MultipartFile file) throws IOException { 
        return imageAbonnementService.uplaodImage(file); 
    }

    @GetMapping("/get/info/{id}")
    public ImageAbonnement getImageDetails(@PathVariable("id") Long id) throws IOException { 
        return imageAbonnementService.getImageDetails(id); 
    }

    @GetMapping("/load/{id}")
    public ResponseEntity<byte[]> getImage(@PathVariable("id") Long id) throws IOException { 
        return imageAbonnementService.getImage(id); 
    }
     
    @DeleteMapping("/delete/{id}")
    public void deleteImage(@PathVariable("id") Long id) { 
    	imageAbonnementService.deleteImage(id); 
    }

    @PutMapping("/update")
    public ImageAbonnement updateImage(@RequestParam("image") MultipartFile file) throws IOException { 
        return imageAbonnementService.uplaodImage(file); 
    }

    @PostMapping("/upload/abonnement/{idAbon}")
    public ImageAbonnement uploadImageForAbonnement(
            @RequestParam("image") MultipartFile file,
            @PathVariable("idAbon") Long idAbon) throws IOException {
        return imageAbonnementService.uplaodImageAbonnement(file, idAbon);
    }
}
