package com.abonnements_microservices.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Data
@NoArgsConstructor
@Table(name = "recordings")
public class Recording {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String title;
    
    private String description;
    
    @Column(nullable = false)
    private String videoUrl;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime recordingDate;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "enseignant_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private Enseignant enseignant;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "matiere_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "abonnements"})
    private Matiere matiere;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "niveau_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private Niveau niveau;
    
    // Optional: link to a live session if this recording is from a live session
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "live_session_id")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private LiveSession liveSession;
}
