import React, { useState, useEffect } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosService";

const ChapitreParMatiere = () => {
  const { idMatiere } = useParams();
  const navigate = useNavigate();
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState("");

  const [nomChapitre, setNomChapitre] = useState("");
  const [nomDeProf, setNomDeProf] = useState("");
  const [duree, setDuree] = useState("");
  const [matiereNom, setMatiereNom] = useState("");

  useEffect(() => {
    axiosInstance.get(`/api/matieres/${idMatiere}`).then((res) => {
      setMatiereNom(res.data.nom);
    });
  }, [idMatiere]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    const chapitreData = {
      nomChapitre,
      nomDeProf,
      duree,
    };

    try {
      await axiosInstance.post(`/api/chapitres/${idMatiere}`, chapitreData);
      navigate(`/chapitres/matiere/${idMatiere}`);
    } catch (error) {
      console.error("Erreur lors de l'ajout du chapitre :", error);
    }
  };

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0">
        <div className="col-sm-6 p-md-0">
          <h4 style={{ color: "#37A7DF" }}> Ajouter un Chapitre </h4>
        </div>
        <div className="col-sm-6 p-md-0 d-flex justify-content-end">
          <ol className="breadcrumb">
            <li className="breadcrumb-item">
              <Link to="/AfficherChapitre" className="nav-text" style={{ color: "#37A7DF" }}>
                Chapitre
              </Link>
            </li>
            <li className="breadcrumb-item active">
              <a href="#" style={{ color: "#37A7DF" }}>
                Ajouter
              </a>
            </li>
          </ol>
        </div>
      </div>

      <div className="row">
        <div className="col-lg-12">
          <div className="card shadow-lg p-4">
            <div className="card-header" style={{ backgroundColor: "#EEF9F5", color: "black" }}>
              <h4 className="card-title">Détails du Chapitre</h4>
            </div>
            <div className="card-body">
              {successMessage && (
                <div className="alert alert-success d-flex justify-content-between">
                  {successMessage}
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setSuccessMessage("")}
                  ></button>
                </div>
              )}

              <div className="container mt-4">
                <h3 style={{ color: "#37A7DF" }}>
                  Ajouter un chapitre pour la matière : {matiereNom}
                </h3>
                <form onSubmit={handleSubmit}>
                  <div className="mb-3">
                    <label>Nom du chapitre</label>
                    <input
                      type="text"
                      className="form-control"
                      value={nomChapitre}
                      onChange={(e) => setNomChapitre(e.target.value)}
                      required
                    />
                  </div>
                  <div className="mb-3">
                    <label>Nom du professeur</label>
                    <input
                      type="text"
                      className="form-control"
                      value={nomDeProf}
                      onChange={(e) => setNomDeProf(e.target.value)}
                      required
                    />
                  </div>
                  <div className="mb-3">
                    <label>Durée (en heures)</label>
                    <input
                      type="number"
                      className="form-control"
                      value={duree}
                      onChange={(e) => setDuree(e.target.value)}
                      required
                    />
                  </div>
                  <button type="submit" className="btn btn-primary">
                    Ajouter le chapitre
                  </button>
                </form>
              </div> {/* container mt-4 */}
            </div> {/* card-body */}
          </div> {/* card */}
        </div> {/* col-lg-12 */}
      </div> {/* row */}
    </div> // container-fluid
  );
};

export default ChapitreParMatiere;
