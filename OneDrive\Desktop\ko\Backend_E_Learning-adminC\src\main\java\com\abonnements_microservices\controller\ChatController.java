package com.abonnements_microservices.controller;

import com.abonnements_microservices.model.ChatHistory;
import com.abonnements_microservices.services.ChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/chat")
@CrossOrigin(origins = {"http://localhost:3036", "http://localhost:3000"})
@Slf4j
public class ChatController {

    @Autowired
    private ChatService chatService;

    /**
     * Send a message to the chat model
     */
    @PostMapping("/send")
    public ResponseEntity<?> sendMessage(@RequestBody Map<String, Object> request) {
        try {
            String message = (String) request.get("message");
            Long userId = Long.valueOf(request.get("userId").toString());

            if (message == null || userId == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Missing required fields",
                    "friendlyMessage", "Veuillez fournir un message et un identifiant utilisateur valides."
                ));
            }

            log.info("Sending message to chat model for user {}: {}", userId, message);
            Map<String, Object> response = chatService.sendMessage(message, userId);

            // If the service indicates an error but returns a 200 status
            if (response.containsKey("success") && response.get("success").equals(false)) {
                // Still return 200 but with error details in the body
                return ResponseEntity.ok(response);
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error sending message to chat model: {}", e.getMessage(), e);

            // Create a more user-friendly error response
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "success", false,
                    "message", "Failed to process chat request",
                    "errorType", "server_error",
                    "friendlyMessage", "Une erreur s'est produite lors du traitement de votre demande. Veuillez réessayer plus tard.",
                    "error", e.getMessage()
                ));
        }
    }

    /**
     * Get chat history for a user
     */
    @GetMapping("/history/{userId}")
    public ResponseEntity<?> getHistoryForUser(@PathVariable Long userId) {
        try {
            List<ChatHistory> history = chatService.getHistoryForUser(userId);
            return ResponseEntity.ok(history);
        } catch (Exception e) {
            log.error("Error getting chat history: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("message", "Failed to get chat history", "error", e.getMessage()));
        }
    }
}
