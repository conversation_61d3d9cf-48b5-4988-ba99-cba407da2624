package com.abonnements_microservices.dto;

import com.abonnements_microservices.model.MatiereNiveauId;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Set;

@Data
@NoArgsConstructor
public class ChapitreDTO {
    private Long id;  // Identifiant du chapitre

    private String nomChapitre;  // Nom du chapitre
    private String nomDeProf;    // Nom du professeur
    private Long duree;          // Durée du chapitre (en heures, minutes, etc.)
    private Long nombreDeCours;  // Nombre total de cours dans ce chapitre
    private String description;  // Description du chapitre

    // ID pour récupérer la bonne combinaison Matière + Niveau
    private Long matiereId;      // ID de la matière
    private Long niveauId;       // ID du niveau

    // Ajout des IDs des cours associés à ce chapitre
    private Set<Long> coursIds;  // Set des IDs des cours associés

    // Constructeur pour initialiser l'objet avec tous ses champs
    public ChapitreDTO(Long id, String nomChapitre, String nomDeProf, Long duree, Long nombreDeCours,
                       String description, Long matiereId, Long niveauId, Set<Long> coursIds) {
        this.id = id;
        this.nomChapitre = nomChapitre;
        this.nomDeProf = nomDeProf;
        this.duree = duree;
        this.nombreDeCours = nombreDeCours;
        this.description = description;
        this.matiereId = matiereId;
        this.niveauId = niveauId;
        this.coursIds = coursIds;
    }
}

