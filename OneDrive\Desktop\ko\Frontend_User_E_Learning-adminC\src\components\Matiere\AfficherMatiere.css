/* AfficherMatiere Component Styling - Updated for better responsiveness */

/* Card Styling */
.matiere-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.125);
  background-color: #fff;
}

.matiere-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15) !important;
}

.matiere-card .card-img-top {
  height: 180px;
  object-fit: cover;
  transition: all 0.3s ease;
}

.matiere-card .card-body {
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.matiere-card h4 {
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #333;  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  min-height: 2.5rem;
}

/* Description container with fade effect */
.description-container {
  position: relative;
  min-height: 60px;
  max-height: 60px;
  overflow: hidden;
  margin-bottom: 15px;
}

.description-container p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
}

.description-fade {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
}

/* Badge Styling */
.badge {
  padding: 5px 8px;
  margin-right: 5px;
  margin-bottom: 5px;
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.75rem;
  display: inline-block;
}

.badge-primary {
  background-color: #37A7DF;
  color: white;
}

.badge-light {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.badge-info {
  background-color: #17a2b8;
  color: white;
}

.badge-warning {
  background-color: #F2BC00;
  color: #212529;
}

/* Button Styling */
.btn-action {
  transition: all 0.3s ease;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 16px;
}

.btn-action i {
  margin-right: 5px;
}

.btn-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Loading and empty states */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 10px;
  border: 1px dashed #dee2e6;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .matiere-card .card-img-top {
    height: 160px;
  }
  
  .matiere-card h4 {
    font-size: 1.1rem;
  }
  
  .description-container {
    min-height: 50px;
    max-height: 50px;
  }
  
  .btn-action {
    padding: 6px 12px;
    font-size: 0.9rem;
  }
}

@media (min-width: 577px) and (max-width: 768px) {
  .matiere-card .card-img-top {
    height: 170px;
  }
}

@media (min-width: 769px) and (max-width: 992px) {
  .matiere-card .card-img-top {
    height: 180px;
  }
}

@media (min-width: 993px) {
  .matiere-card .card-img-top {
    height: 200px;
  }
}

/* Modal adjustments for mobile */
@media (max-width: 576px) {
  .modal-dialog {
    margin: 0.5rem;
  }
  
  .modal-header {
    padding: 0.75rem;
  }
  
  .modal-body {
    padding: 1rem;
  }
  
  .modal-footer {
    padding: 0.75rem;
    flex-direction: column;
  }
  
  .modal-footer button {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}
