# E-Learning Platform Docker Setup

This Docker setup provides a complete containerized environment for the E-Learning platform with all necessary services.

## 🏗️ Architecture

The platform consists of the following services:

- **MySQL Database** (Port 3306): Main database for application data
- **Keycloak** (Port 8080): Authentication and authorization server
- **Backend API** (Port 8081): Spring Boot application
- **User Frontend** (Port 3000): React application for students/users
- **Vitrine Frontend** (Port 3001): React application for marketing/showcase

## 📋 Prerequisites

- Docker Desktop installed and running
- Docker Compose (usually included with Docker Desktop)
- At least 4GB of available RAM
- Ports 3000, 3001, 8080, 8081, and 3306 available

## 🚀 Quick Start

### Windows
```bash
# Run the startup script
docker-start.bat
```

### Linux/macOS
```bash
# Make the script executable
chmod +x docker-start.sh

# Run the startup script
./docker-start.sh
```

### Manual Start
```bash
# Build and start all services
docker-compose up --build -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 🔧 Configuration

### Environment Variables
The `.env` file contains all configurable environment variables:

- Database credentials
- Keycloak settings
- API URLs
- Port configurations

### Service Configuration
Each service has its own configuration:

- **Backend**: `application-docker.properties` for Docker-specific settings
- **Frontend**: Environment variables for API endpoints
- **MySQL**: Initialization scripts in `mysql/init/`

## 📊 Monitoring and Logs

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend-user
docker-compose logs -f keycloak
docker-compose logs -f mysql
```

### Health Checks
All services include health checks:
```bash
# Check service status
docker-compose ps

# View health status
docker inspect --format='{{.State.Health.Status}}' elearning-backend
```

## 🔐 Default Credentials

### Keycloak Admin
- **URL**: http://localhost:8080
- **Username**: admin
- **Password**: 0000

### MySQL Root
- **Host**: localhost:3306
- **Username**: root
- **Password**: rootpassword

### Application Database
- **Database**: elearning_db
- **Username**: elearning_user
- **Password**: elearning_password

## 🛠️ Development

### Rebuilding Services
```bash
# Rebuild specific service
docker-compose build backend
docker-compose up -d backend

# Rebuild all services
docker-compose build
docker-compose up -d
```

### Database Management
```bash
# Access MySQL shell
docker-compose exec mysql mysql -u root -p

# Backup database
docker-compose exec mysql mysqldump -u root -p elearning_db > backup.sql

# Restore database
docker-compose exec -T mysql mysql -u root -p elearning_db < backup.sql
```

## 🔄 Updates and Maintenance

### Updating Images
```bash
# Pull latest base images
docker-compose pull

# Rebuild with latest changes
docker-compose build --no-cache
docker-compose up -d
```

### Cleaning Up
```bash
# Stop and remove containers
docker-compose down

# Remove containers and volumes (⚠️ This will delete all data)
docker-compose down -v

# Remove unused Docker resources
docker system prune -a
```

## 🐛 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check what's using the port
   netstat -tulpn | grep :8080
   
   # Kill the process or change port in docker-compose.yml
   ```

2. **Services Not Starting**
   ```bash
   # Check logs for errors
   docker-compose logs [service-name]
   
   # Restart specific service
   docker-compose restart [service-name]
   ```

3. **Database Connection Issues**
   ```bash
   # Ensure MySQL is healthy
   docker-compose ps mysql
   
   # Check MySQL logs
   docker-compose logs mysql
   ```

4. **Keycloak Not Accessible**
   ```bash
   # Wait for Keycloak to fully start (can take 2-3 minutes)
   docker-compose logs -f keycloak
   ```

### Performance Optimization

1. **Increase Docker Resources**
   - Allocate more RAM and CPU in Docker Desktop settings
   - Recommended: 4GB RAM, 2 CPU cores

2. **Volume Optimization**
   - Use named volumes for better performance
   - Avoid bind mounts for large directories

## 📁 File Structure

```
ko/
├── docker-compose.yml              # Main orchestration file
├── .env                           # Environment variables
├── .dockerignore                  # Docker ignore patterns
├── docker-start.sh/.bat           # Startup scripts
├── mysql/
│   └── init/
│       └── 01-create-databases.sql # Database initialization
├── Backend_E_Learning-adminC/
│   ├── Dockerfile                 # Backend container definition
│   └── src/main/resources/
│       └── application-docker.properties # Docker config
├── Frontend_User_E_Learning-adminC/
│   ├── Dockerfile                 # User frontend container
│   └── nginx.conf                 # Nginx configuration
└── vitrine-vitrine3/
    ├── Dockerfile                 # Vitrine frontend container
    └── nginx.conf                 # Nginx configuration
```

## 🔗 Useful Commands

```bash
# Start services in background
docker-compose up -d

# Start with rebuild
docker-compose up --build

# Scale a service
docker-compose up -d --scale backend=2

# Execute command in container
docker-compose exec backend bash

# View resource usage
docker stats

# Export/Import images
docker save -o elearning-backup.tar $(docker-compose config --services | xargs -I {} echo "elearning_{}")
docker load -i elearning-backup.tar
```
