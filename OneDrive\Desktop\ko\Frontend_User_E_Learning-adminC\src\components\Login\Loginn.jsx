import React, { useState } from "react";
import { useNavigate } from "react-router-dom"; // Utilisé pour la navigation
import myImage from "../../images/pattern/output-onlinepngtools.png";
import "./login.css";

const Login = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const navigate = useNavigate();

  const handleLogin = (e) => {
    e.preventDefault();
    console.log("Username:", username);
    console.log("Password:", password);
    console.log("Remember Me:", rememberMe);

    // Exemple : Rediriger vers une autre page après la connexion
    navigate("/dashboard");
  };

  return (
    <div className="fix-wrapper">
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-lg-5 col-md-6">
            <div className="card mb-0 h-auto">
              <div className="card-body">
                <div className="text-center mb-2">
                  <a href="/">
                    <svg width="250" height="56" viewBox="0 0 250 56">
                      <g transform="translate(20, 10)">
                        <text
                          x="70"
                          y="35"
                          fontSize="32"
                          fontFamily="'Inter', sans-serif"
                          fontWeight="bold"
                        >
                          DEUTZA
                        </text>
                      </g>
                      <image
                        className="image"
                        href={myImage}
                        x="0"
                        y="-7"
                        width="80"
                        height="70"
                      />
                    </svg>
                  </a>
                </div>
                <h4 className="text-center mb-4">Sign in to your account</h4>
                <form onSubmit={handleLogin}>
                  <div className="form-group">
                    <label className="form-label" htmlFor="username">
                      Username
                    </label>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="username"
                      id="username"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                    />
                  </div>
                  <div className="mb-4 position-relative">
                    <label className="form-label" htmlFor="password">
                      Password
                    </label>
                    <input
                      type="password"
                      id="password"
                      className="form-control"
                      placeholder="Enter your password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                  </div>
                  <div className="form-row d-flex flex-wrap justify-content-between mt-4 mb-2">
                    <div className="form-group">
                      <div className="form-check custom-checkbox ms-1">
                        <input
                          type="checkbox"
                          className="form-check-input"
                          id="rememberMe"
                          checked={rememberMe}
                          onChange={(e) => setRememberMe(e.target.checked)}
                        />
                        <label
                          className="form-check-label"
                          htmlFor="rememberMe"
                        >
                          Remember Me
                        </label>
                      </div>
                    </div>
                    <div className="form-group ms-2">
                      <a className="btn-link" href="/forgot-password">
                        Forgot Password?
                      </a>
                    </div>
                  </div>
                  <div className="text-center">
                    <button
                      type="submit"
                      className="btn btn-primary btn-block"
                    >
                      Sign Me In
                    </button>
                  </div>
                </form>
                <div className="new-account mt-3">
                  <p>
                    Don't have an account?{" "}
                    <a className="text-primary" href="/register">
                      Sign up
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
