package com.abonnements_microservices.repo;

import com.abonnements_microservices.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    boolean existsByUsername(String username);
    boolean existsByEmail(String email);

    // Ajouter une méthode pour trouver un utilisateur par son nom d'utilisateur
    java.util.Optional<User> findByUsername(String username);

    // Ajouter une méthode pour trouver un utilisateur par son email
    java.util.Optional<User> findByEmail(String email);
}