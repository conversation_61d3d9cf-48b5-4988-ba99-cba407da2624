import React, { Fragment, useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import axiosInstance from '../../services/axiosService';
import { useKeycloak } from '@react-keycloak/web';
import './LessonPage.css';

const LessonPage = () => {
    const { chapitreId, coursId } = useParams();
    const navigate = useNavigate();
    const { keycloak } = useKeycloak();
    const [expanded, setExpanded] = useState('panel1');
    const [chapitre, setChapitre] = useState(null);
    const [cours, setCours] = useState([]);
    const [currentCours, setCurrentCours] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [matiereId, setMatiereId] = useState(null);

    // Fetch chapitre and cours data
    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                // Fetch chapitre details
                const chapitreResponse = await axiosInstance.get(`/api/chapitres/${chapitreId}`);
                setChapitre(chapitreResponse.data);

                // Get the matiere ID from the chapitre
                if (chapitreResponse.data && chapitreResponse.data.matiereNiveau &&
                    chapitreResponse.data.matiereNiveau.matiere &&
                    chapitreResponse.data.matiereNiveau.matiere.id) {
                    setMatiereId(chapitreResponse.data.matiereNiveau.matiere.id);
                }

                // Fetch cours for this chapitre
                const coursResponse = await axiosInstance.get(`/api/cours/chapitre/${chapitreId}`);
                const coursData = coursResponse.data.content || coursResponse.data;
                setCours(coursData);

                // Set current cours based on coursId parameter or default to first cours
                if (coursId && coursData.length > 0) {
                    const selectedCours = coursData.find(c => c.id === parseInt(coursId) || c.idCours === parseInt(coursId));
                    setCurrentCours(selectedCours || coursData[0]);
                } else if (coursData.length > 0) {
                    setCurrentCours(coursData[0]);
                }

                setLoading(false);
            } catch (err) {
                console.error("Error fetching data:", err);
                setError("Une erreur s'est produite lors du chargement des données");
                setLoading(false);
            }
        };

        fetchData();
    }, [chapitreId, coursId]);

    const handleChange = (panel) => (event, isExpanded) => {
        setExpanded(isExpanded ? panel : false);
    };

    const handleCoursClick = (cours) => {
        setCurrentCours(cours);
        navigate(`/lesson/${chapitreId}/${cours.id}`);
    };

    // Group cours by chapitre for sidebar
    const renderVideoContent = () => {
        if (!currentCours) return null;

        if (currentCours.lien) {
            // Handle Vimeo videos
            const vimeoMatch = currentCours.lien.match(/(?:vimeo\.com\/|\/videos\/|video\/)?(\d+)/);
            if (vimeoMatch && vimeoMatch[1]) {
                return (
                    <div className="embed-responsive embed-responsive-16by9">
                        <iframe
                            src={`https://player.vimeo.com/video/${vimeoMatch[1]}`}
                            width="100%"
                            height="500"
                            frameBorder="0"
                            allow="autoplay; fullscreen"
                            allowFullScreen
                            title="Vidéo du cours"
                        ></iframe>
                    </div>
                );
            }

            // Handle YouTube videos
            const youtubeMatch = currentCours.lien.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);
            if (youtubeMatch && youtubeMatch[1]) {
                return (
                    <div className="embed-responsive embed-responsive-16by9">
                        <iframe
                            src={`https://www.youtube.com/embed/${youtubeMatch[1]}`}
                            width="100%"
                            height="500"
                            frameBorder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowFullScreen
                            title="Vidéo du cours"
                        ></iframe>
                    </div>
                );
            }

            // Handle direct video files
            if (currentCours.lien.match(/\.(mp4|webm|ogg)$/i)) {
                return (
                    <video controls width="100%" height="auto">
                        <source src={currentCours.lien} type={`video/${currentCours.lien.split('.').pop()}`} />
                        Votre navigateur ne supporte pas la lecture de vidéos.
                    </video>
                );
            }

            // Default fallback for other links
            return (
                <div className="video-fallback">
                    <a href={currentCours.lien} target="_blank" rel="noopener noreferrer" className="btn btn-primary">
                        Voir la vidéo
                    </a>
                </div>
            );
        }

        return (
            <div className="no-video-message">
                <p>Aucune vidéo disponible pour ce cours.</p>
            </div>
        );
    };

    // Find previous and next cours
    const findPrevNextCours = () => {
        if (!currentCours || !cours.length) return { prev: null, next: null };

        const currentIndex = cours.findIndex(c => c.id === currentCours.id);
        const prev = currentIndex > 0 ? cours[currentIndex - 1] : null;
        const next = currentIndex < cours.length - 1 ? cours[currentIndex + 1] : null;

        return { prev, next };
    };

    const { prev, next } = findPrevNextCours();

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner-border text-primary" role="status">
                    <span className="sr-only">Chargement...</span>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="error-container">
                <div className="alert alert-danger">{error}</div>
                <button className="btn btn-primary" onClick={() => navigate(-1)}>Retour</button>
            </div>
        );
    }

    return (
        <Fragment>
            <section className="wpo-lesson-section">
                <h2 className="hidden"></h2>
                <div className="container-fluid">
                    <div className="row">
                        <div className="col col-xl-3 col-lg-4 col-12">
                            <div className="wpo-lesson-sidebar">
                                <div className="accordion-item">
                                    <Accordion expanded={expanded === 'panel1'} onChange={handleChange('panel1')}>
                                        <AccordionSummary
                                            aria-controls="panel1bh-content"
                                            id="panel1bh-header"
                                        >
                                            <Typography className="accordion-title">
                                                {chapitre?.nomChapitre || 'Chapitre'}
                                                <span className="course-count">{cours.length > 0 ? `${cours.length} cours` : 'Aucun cours  '}</span>
                                            </Typography>
                                        </AccordionSummary>
                                        <AccordionDetails>
                                            <Typography>
                                                <div className="accordion-body">
                                                    <ul className="item">
                                                        {cours.map((item, index) => (
                                                            <li key={item.id}>
                                                                <Link
                                                                    to="#"
                                                                    onClick={() => handleCoursClick(item)}
                                                                    className={currentCours && currentCours.id === item.id ? 'active' : ''}
                                                                >
                                                                    <span>
                                                                        {index + 1}.{index < 9 ? '0' : ''}{index + 1}
                                                                        <i className="fi flaticon-play-button"></i>
                                                                        {item.titre}
                                                                    </span>
                                                                    <span>
                                                                        {item.duree} min
                                                                        <i className={currentCours && currentCours.id === item.id ? "fa fa-check-circle" : "fa fa-circle-thin"}
                                                                           aria-hidden="true"></i>
                                                                    </span>
                                                                </Link>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            </Typography>
                                        </AccordionDetails>
                                    </Accordion>
                                </div>
                            </div>
                        </div>
                        <div className="col col-xl-9 col-lg-8 col-12">
                            <div className="video-area">
                                <div className="video-heading">
                                    <h2>{currentCours?.titre || 'Cours'}</h2>
                                    <div className="button-group">
                                        {matiereId && (
                                            <button
                                                className="theme-btn"
                                                onClick={() => navigate(`/chapitres/matiere/${matiereId}`)}
                                            >
                                                Retour aux chapitres
                                            </button>
                                        )}
                                        <button
                                            className="theme-btn secondary"
                                            onClick={() => navigate('/')}
                                        >
                                            Accueil
                                        </button>
                                    </div>
                                </div>
                                {renderVideoContent()}
                                <div className="video-details">
                                    <h2>À propos de ce cours</h2>
                                    <p>{currentCours?.description || 'Aucune description disponible.'}</p>

                                    {currentCours?.pdf && (
                                        <div className="mt-4">
                                            <h4>Documents supplémentaires</h4>
                                            <a
                                                href={currentCours.pdf}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="btn btn-outline-primary mt-2"
                                            >
                                                <i className="fa fa-file-pdf-o mr-2"></i> Télécharger le PDF
                                            </a>
                                        </div>
                                    )}
                                </div>
                                <div className="video-details-pagination">
                                    <ul>
                                        <li>
                                            {prev ? (
                                                <Link to="#" onClick={() => handleCoursClick(prev)}>Précédent</Link>
                                            ) : (
                                                <span className="disabled">Précédent</span>
                                            )}
                                        </li>
                                        <li>
                                            {next ? (
                                                <Link to="#" onClick={() => handleCoursClick(next)}>Suivant</Link>
                                            ) : (
                                                <span className="disabled">Suivant</span>
                                            )}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </Fragment>
    );
};

export default LessonPage;
