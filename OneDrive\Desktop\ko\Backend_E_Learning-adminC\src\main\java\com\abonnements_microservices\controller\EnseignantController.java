package com.abonnements_microservices.controller;

import com.abonnements_microservices.model.Enseignant;
import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.model.Niveau;
import com.abonnements_microservices.services.EnseignantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.persistence.EntityNotFoundException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/enseignants")
@CrossOrigin(origins = {"http://localhost:3036", "http://localhost:3000"})
@Slf4j
public class EnseignantController {

    @Autowired
    private EnseignantService enseignantService;

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createEnseignant(@RequestBody Map<String, Object> request) {
        try {
            log.debug("Creating new enseignant with data: {}", request);
            
            Enseignant enseignant = new Enseignant();
            enseignant.setUsername((String) request.get("username"));
            enseignant.setFirstName((String) request.get("firstName"));
            enseignant.setLastName((String) request.get("lastName"));
            enseignant.setEmail((String) request.get("email"));
            enseignant.setPassword((String) request.get("password"));
            
            // Convert IDs from Integer to Long
            @SuppressWarnings("unchecked")
            List<Integer> rawNiveauIds = (List<Integer>) request.get("niveauIds");
            List<Long> niveauIds = rawNiveauIds != null ? 
                rawNiveauIds.stream().map(Integer::longValue).collect(Collectors.toList()) : 
                null;

            @SuppressWarnings("unchecked")
            List<Integer> rawMatiereIds = (List<Integer>) request.get("matiereIds");
            List<Long> matiereIds = rawMatiereIds != null ? 
                rawMatiereIds.stream().map(Integer::longValue).collect(Collectors.toList()) : 
                null;

            try {
                Enseignant created = enseignantService.createEnseignant(enseignant, niveauIds, matiereIds);
                log.info("Successfully created enseignant with username: {}", enseignant.getUsername());
                return ResponseEntity.ok(created);
            } catch (RuntimeException e) {
                log.error("Failed to create enseignant: {}", e.getMessage(), e);
                return ResponseEntity.internalServerError()
                    .body(Map.of(
                        "message", "Failed to create enseignant",
                        "error", e.getMessage(),
                        "details", e.getCause() != null ? e.getCause().getMessage() : "No additional details"
                    ));
            }
        } catch (IllegalArgumentException e) {
            log.warn("Invalid input data for enseignant creation: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
        } catch (EntityNotFoundException e) {
            log.warn("Entity not found during enseignant creation: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Unexpected error during enseignant creation: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "message", "An unexpected error occurred",
                    "error", e.getMessage(),
                    "details", e.getCause() != null ? e.getCause().getMessage() : "No additional details"
                ));
        }
    }

    @GetMapping
    public ResponseEntity<List<Enseignant>> getAllEnseignants() {
        return ResponseEntity.ok(enseignantService.getAllEnseignants());
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getEnseignantById(@PathVariable Long id) {
        return enseignantService.getEnseignantById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateEnseignant(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request) {
        try {
            log.debug("Updating enseignant with id: {} and data: {}", id, request);
            
            Enseignant enseignant = new Enseignant();
            enseignant.setFirstName((String) request.get("firstName"));
            enseignant.setLastName((String) request.get("lastName"));
            enseignant.setEmail((String) request.get("email"));
            
            @SuppressWarnings("unchecked")
            List<Integer> rawMatiereIds = (List<Integer>) request.get("matiereIds");
            List<Long> matiereIds = rawMatiereIds != null ? 
                rawMatiereIds.stream().map(Integer::longValue).collect(Collectors.toList()) : 
                null;

            @SuppressWarnings("unchecked")
            List<Integer> rawNiveauIds = (List<Integer>) request.get("niveauIds");
            List<Long> niveauIds = rawNiveauIds != null ? 
                rawNiveauIds.stream().map(Integer::longValue).collect(Collectors.toList()) : 
                null;

            Enseignant updated = enseignantService.updateEnseignant(id, enseignant, niveauIds, matiereIds);
            return ResponseEntity.ok(updated);
        } catch (EntityNotFoundException e) {
            log.error("Enseignant not found: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error updating enseignant: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "message", "Failed to update enseignant",
                    "error", e.getMessage()
                ));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteEnseignant(@PathVariable Long id) {
        try {
            enseignantService.deleteEnseignant(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "message", "Failed to delete enseignant",
                    "error", e.getMessage()
                ));
        }
    }

    @GetMapping("/{id}/matieres")
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT')")
public ResponseEntity<List<Matiere>> getMatieresByEnseignantId(@PathVariable Long id) {
    try {
        List<Matiere> matieres = enseignantService.getMatieresByEnseignantId(id);
        return ResponseEntity.ok(matieres);
    } catch (EntityNotFoundException e) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
    }
}
@GetMapping("/{id}/niveau")
@PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT')")
public ResponseEntity<List<Niveau>> getNiveauByEnseignantId(@PathVariable Long id) {
    try {
        List<Niveau> Niveau = enseignantService.getNiveauByEnseignantId(id);
        return ResponseEntity.ok(Niveau);
    } catch (EntityNotFoundException e) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
    }
}

@GetMapping("/find")
public ResponseEntity<?> findEnseignantByUsernameOrEmail(
        @RequestParam(required = false) String username,
        @RequestParam(required = false) String email) {
    try {
        log.info("Finding enseignant with username: {} or email: {}", username, email);
        Optional<Enseignant> enseignant = Optional.empty();
        
        if (username != null && !username.isEmpty()) {
            enseignant = enseignantService.getEnseignantByUsername(username);
            log.info("Search by username result: {}", enseignant.isPresent());
        }
        
        if (!enseignant.isPresent() && email != null && !email.isEmpty()) {
            enseignant = enseignantService.getEnseignantByEmail(email);
            log.info("Search by email result: {}", enseignant.isPresent());
        }
        
        return enseignant.map(ResponseEntity::ok)
                .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND).body(null));
    } catch (Exception e) {
        log.error("Error finding enseignant by username or email", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("message", "Failed to find enseignant", "error", e.getMessage()));
    }
}

}