import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useKeycloak } from "@react-keycloak/web";
import { <PERSON><PERSON>, Spinner } from "react-bootstrap";
import abonnementTypeService from "../../services/abonnementTypeService";

const AjouterTypeAbonnement = () => {
  const navigate = useNavigate();
  const { keycloak } = useKeycloak();

  const [formData, setFormData] = useState({
    nom: "",
    description: "",
    hasCourses: true,
    hasRecordings: false,
    hasLiveSessions: false
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);

  const handleChange = (e) => {
    const { id, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [id]: type === 'checkbox' ? checked : value,
    });
  };

  const handleCancel = () => {
    // Reset form and errors
    setFormData({
      nom: "",
      description: "",
      hasCourses: true,
      hasRecordings: false,
      hasLiveSessions: false
    });
    setErrors({});
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Reset errors
    setErrors({});
    setError(null);

    // Validate form
    let validationErrors = {};

    if (!formData.nom.trim()) {
      validationErrors.nom = "Le nom est requis.";
    }

    // Update error state
    setErrors(validationErrors);

    // If there are errors, don't submit the form
    if (Object.keys(validationErrors).length > 0) {
      return;
    }

    // Submit the form
    try {
      setLoading(true);

      // Prepare data for API
      const typeData = {
        nom: formData.nom,
        description: formData.description,
        hasCourses: formData.hasCourses,
        hasRecordings: formData.hasRecordings,
        hasLiveSessions: formData.hasLiveSessions
      };

      // Call API to create subscription type
      await abonnementTypeService.createType(typeData);

      // Show success message
      setSuccess(true);

      // Reset form
      setFormData({
        nom: "",
        description: "",
        hasCourses: true,
        hasRecordings: false,
        hasLiveSessions: false
      });

      // Redirect after a delay
      setTimeout(() => {
        navigate("/type-abonnements");
      }, 2000);
    } catch (err) {
      console.error("Error creating subscription type:", err);
      setError("Une erreur s'est produite lors de la création du type d'abonnement.");
    } finally {
      setLoading(false);
    }
  };

  return (
   
      <div className="container-fluid">
        <div className="row page-titles mx-0">
          <div className="col-sm-6 p-md-0">
            <div className="welcome-text">
              <h4>Ajouter un Type d'abonnement</h4>
            </div>
          </div>
          <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex">
            <button
              className="btn btn-secondary"
              onClick={() => navigate('/type-abonnements')}
            >
              Retour à la liste
            </button>
          </div>
        </div>

        {/* Success message */}
        {success && (
          <Alert variant="success" className="mb-4">
            Le type d'abonnement a été ajouté avec succès ! Redirection en cours...
          </Alert>
        )}

        {/* Error message */}
        {error && (
          <Alert variant="danger" className="mb-4">
            {error}
          </Alert>
        )}

        <div className="row">
          <div className="col-lg-12">
            <div className="card shadow-lg p-4">
              <div
                className="card-header"
                style={{ backgroundColor: "#EEF9F5", color: "#333" }}
              >
                <h4 className="card-title">Détails du Type d'abonnement</h4>
              </div>
              <div className="card-body">
                <form onSubmit={handleSubmit}>
                  <div className="row">
                    {/* Section 1: Informations générales */}
                    <div className="col-sm-6 mb-3">
                      <div className="form-group">
                        <label className="form-label" htmlFor="nom">
                          Nom d'abonnement <span className="text-danger">*</span>
                        </label>
                        <input
                          placeholder="Nom d'abonnement"
                          id="nom"
                          type="text"
                          className={`form-control ${errors.nom ? 'is-invalid' : ''}`}
                          value={formData.nom}
                          onChange={handleChange}
                        />
                        {errors.nom && (
                          <div className="invalid-feedback">{errors.nom}</div>
                        )}
                      </div>
                    </div>
                    <div className="col-sm-6 mb-3">
                      <div className="form-group">
                        <label className="form-label" htmlFor="description">
                          Description
                        </label>
                        <textarea
                          placeholder="Description"
                          id="description"
                          className="form-control"
                          value={formData.description}
                          onChange={handleChange}
                          rows="3"
                        ></textarea>
                      </div>
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-12 mb-4">
                      <h5 className="mb-3">Accès aux fonctionnalités</h5>
                      <div className="form-check form-switch mb-2">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="hasCourses"
                          checked={formData.hasCourses}
                          onChange={handleChange}
                        />
                        <label className="form-check-label" htmlFor="hasCourses">
                          Accès aux cours
                        </label>
                      </div>
                      <div className="form-check form-switch mb-2">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="hasRecordings"
                          checked={formData.hasRecordings}
                          onChange={handleChange}
                        />
                        <label className="form-check-label" htmlFor="hasRecordings">
                          Accès aux enregistrements
                        </label>
                      </div>
                      <div className="form-check form-switch mb-2">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="hasLiveSessions"
                          checked={formData.hasLiveSessions}
                          onChange={handleChange}
                        />
                        <label className="form-check-label" htmlFor="hasLiveSessions">
                          Accès aux sessions live
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="row mt-3">
                    <div className="col-12 d-flex justify-content-end">
                      <button
                        type="button"
                        className="btn btn-danger light me-2"
                        onClick={handleCancel}
                        disabled={loading}
                      >
                        Annuler
                      </button>
                      <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={loading}
                      >
                        {loading ? (
                          <>
                            <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" />
                            <span className="ms-2">Création en cours...</span>
                          </>
                        ) : (
                          "Ajouter"
                        )}
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
   
  );
};

export default AjouterTypeAbonnement;
