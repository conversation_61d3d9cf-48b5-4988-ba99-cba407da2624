import React, {useState} from 'react';
import Grid from "@mui/material/Grid";
import SimpleReactValidator from "simple-react-validator";
import {toast} from "react-toastify";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import {Link, useNavigate} from "react-router-dom";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import axios from "axios";

import './style.scss';

const SignUpPage = (props) => {

    const push = useNavigate()
    const [isSubmitting, setIsSubmitting] = useState(false);

    const [value, setValue] = useState({
        email: '',
        firstName: '',
        lastName: '',
        phoneNumber: '',
        password: '',
        confirm_password: '',
        dateNaissance: '',
        niveauId: '', // This will store the selected education level ID
    });

    const [niveaux, setNiveaux] = useState([]);
    
    // Fetch education levels on component mount
    React.useEffect(() => {
        const fetchNiveaux = async () => {
            try {
                const response = await axios.get('http://localhost:8084/api/niveaux');
                setNiveaux(response.data);
            } catch (error) {
                console.error('Error fetching niveaux:', error);
                toast.error('Error loading education levels. Please try again later.');
            }
        };
        
        fetchNiveaux();
    }, []);

    const changeHandler = (e) => {
        setValue({...value, [e.target.name]: e.target.value});
        validator.showMessages();
    };

    const [validator] = React.useState(new SimpleReactValidator({
        className: 'errorMessage'
    }));

    const submitForm = async (e) => {
        e.preventDefault();
        if (validator.allValid()) {
            if (value.password !== value.confirm_password) {
                toast.error('Passwords do not match!');
                return;
            }
            
            setIsSubmitting(true);
            
            try {
                // Create the student registration request
                const studentData = {
                    firstName: value.firstName,
                    lastName: value.lastName,
                    email: value.email,
                    phoneNumber: value.phoneNumber,
                    password: value.password,
                    dateNaissance: value.dateNaissance,
                    niveauId: value.niveauId,
                    status: 'PENDING', // Set the initial status to pending
                    username: value.email // Using email as username for simplicity
                };
                
                // Send the registration request to the backend
                await axios.post('http://localhost:8084/api/registration/etudiant', studentData);
                
                // Reset the form
                setValue({
                    email: '',
                    firstName: '',
                    lastName: '',
                    phoneNumber: '',
                    password: '',
                    confirm_password: '',
                    dateNaissance: '',
                    niveauId: '',
                });
                
                validator.hideMessages();
                toast.success('Registration submitted successfully! Your account will be reviewed by an administrator.');
                push('/login');
            } catch (error) {
                console.error('Registration error:', error);
                if (error.response && error.response.data) {
                    toast.error(`Registration failed: ${error.response.data.message || 'Please try again later.'}`);
                } else {
                    toast.error('Registration failed. Please try again later.');
                }
            } finally {
                setIsSubmitting(false);
            }
        } else {
            validator.showMessages();
            toast.error('Please correct the errors in the form!');
        }
    };
    
    return (
        <Grid className="loginWrapper">
            <Grid className="loginForm">
                <h2>Student Registration</h2>
                <p>Create your student account</p>
                <form onSubmit={submitForm}>
                    <Grid container spacing={3}>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                className="inputOutline"
                                fullWidth
                                placeholder="First Name"
                                value={value.firstName}
                                variant="outlined"
                                name="firstName"
                                label="First Name"
                                InputLabelProps={{
                                    shrink: true,
                                }}
                                onBlur={(e) => changeHandler(e)}
                                onChange={(e) => changeHandler(e)}
                            />
                            {validator.message('firstName', value.firstName, 'required|alpha')}
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                className="inputOutline"
                                fullWidth
                                placeholder="Last Name"
                                value={value.lastName}
                                variant="outlined"
                                name="lastName"
                                label="Last Name"
                                InputLabelProps={{
                                    shrink: true,
                                }}
                                onBlur={(e) => changeHandler(e)}
                                onChange={(e) => changeHandler(e)}
                            />
                            {validator.message('lastName', value.lastName, 'required|alpha')}
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                className="inputOutline"
                                fullWidth
                                placeholder="E-mail"
                                value={value.email}
                                variant="outlined"
                                name="email"
                                label="E-mail"
                                InputLabelProps={{
                                    shrink: true,
                                }}
                                onBlur={(e) => changeHandler(e)}
                                onChange={(e) => changeHandler(e)}
                            />
                            {validator.message('email', value.email, 'required|email')}
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                className="inputOutline"
                                fullWidth
                                placeholder="Phone Number"
                                value={value.phoneNumber}
                                variant="outlined"
                                name="phoneNumber"
                                label="Phone Number"
                                InputLabelProps={{
                                    shrink: true,
                                }}
                                onBlur={(e) => changeHandler(e)}
                                onChange={(e) => changeHandler(e)}
                            />
                            {validator.message('phoneNumber', value.phoneNumber, 'required')}
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                className="inputOutline"
                                fullWidth
                                type="date"
                                value={value.dateNaissance}
                                variant="outlined"
                                name="dateNaissance"
                                label="Date of Birth"
                                InputLabelProps={{
                                    shrink: true,
                                }}
                                onBlur={(e) => changeHandler(e)}
                                onChange={(e) => changeHandler(e)}
                            />
                            {validator.message('dateNaissance', value.dateNaissance, 'required')}
                        </Grid>
                        <Grid item xs={12}>
                            <FormControl fullWidth variant="outlined">
                                <InputLabel id="niveau-label">Education Level</InputLabel>
                                <Select
                                    className="inputOutline"
                                    labelId="niveau-label"
                                    value={value.niveauId}
                                    name="niveauId"
                                    label="Education Level"
                                    onChange={(e) => changeHandler(e)}
                                >
                                    {niveaux.map((niveau) => (
                                        <MenuItem key={niveau.id} value={niveau.id}>
                                            {niveau.nomNiveau}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                            {validator.message('niveauId', value.niveauId, 'required')}
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                className="inputOutline"
                                fullWidth
                                placeholder="Password"
                                value={value.password}
                                variant="outlined"
                                name="password"
                                label="Password"
                                type="password"
                                InputLabelProps={{
                                    shrink: true,
                                }}
                                onBlur={(e) => changeHandler(e)}
                                onChange={(e) => changeHandler(e)}
                            />
                            {validator.message('password', value.password, 'required|min:6')}
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                className="inputOutline"
                                fullWidth
                                placeholder="Confirm Password"
                                value={value.confirm_password}
                                variant="outlined"
                                name="confirm_password"
                                label="Confirm Password"
                                type="password"
                                InputLabelProps={{
                                    shrink: true,
                                }}
                                onBlur={(e) => changeHandler(e)}
                                onChange={(e) => changeHandler(e)}
                            />
                            {validator.message('confirm password', value.confirm_password, 'required|min:6')}
                        </Grid>
                        <Grid item xs={12}>
                            <div className="formFooter">
                                <Button 
                                    fullWidth 
                                    className="cBtn cBtnLarge cBtnTheme" 
                                    type="submit"
                                    disabled={isSubmitting}
                                >
                                    {isSubmitting ? 'Submitting...' : 'Register'}
                                </Button>
                            </div>
                            <p className="noteHelp">Already have an account? <Link to="/login">Return to Sign In</Link>
                            </p>
                            <div className="noteHelp">
                                <p><strong>Note:</strong> After registration, your account will need to be approved by an administrator before you can log in.</p>
                            </div>
                        </Grid>
                    </Grid>
                </form>
                <div className="shape-img">
                    <i className="fi flaticon-honeycomb"></i>
                </div>
            </Grid>
        </Grid>
    );
};

export default SignUpPage;
