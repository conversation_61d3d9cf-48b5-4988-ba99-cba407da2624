import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

const Inscription = () => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    name: "",
    cin: "",
    email: "",
    telephone: "",
    parentName: "",
    parentCin: "",
    parentTelephone: "",
    abonnement: "1",
  });

  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const newErrors = {};

    // Validation des champs
    if (!formData.name) newErrors.name = "Le nom de l'étudiant est requis.";
    if (!formData.cin) newErrors.cin = "Le CIN est requis.";
    if (!formData.email) newErrors.email = "L'email est requis.";
    if (!formData.telephone) newErrors.telephone = "Le numéro de téléphone est requis.";
    if (!formData.parentName) newErrors.parentName = "Le nom du parent est requis.";
    if (!formData.parentCin) newErrors.parentCin = "Le CIN du parent est requis.";
    if (!formData.parentTelephone) newErrors.parentTelephone = "Le téléphone du parent est requis.";

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      console.log("Formulaire soumis :", formData);
      navigate("/Affichage");
    }
  };

  return (
    <div className="content-body">
    <div className="container-fluid">
      <div className="row page-titles mx-0">
        <div className="col-sm-6 p-md-0">
          <h4>Remplir formulaire</h4>
        </div>
      </div>

      <div className="row">
        <div className="col-lg-12">
          <div className="card">
            <div className="card-header">
              <h4 className="card-title">Détails du formulaire</h4>
            </div>
            <div className="card-body">

          <form onSubmit={handleSubmit} className="mt-4">

            {/* Nom de l'étudiant */}
            <div className="form-group">
              <label htmlFor="name">Nom de l'Étudiant :</label>
              <input type="text" id="name" name="name" value={formData.name} onChange={handleChange} className="form-control" />
              {errors.name && <small className="text-danger">{errors.name}</small>}
            </div>

            {/* CIN de l'étudiant */}
            <div className="form-group">
              <label htmlFor="cin">CIN de l'Étudiant :</label>
              <input type="text" id="cin" name="cin" value={formData.cin} onChange={handleChange} className="form-control" />
              {errors.cin && <small className="text-danger">{errors.cin}</small>}
            </div>

            {/* Email de l'étudiant */}
            <div className="form-group">
              <label htmlFor="email">Email :</label>
              <input type="email" id="email" name="email" value={formData.email} onChange={handleChange} className="form-control" />
              {errors.email && <small className="text-danger">{errors.email}</small>}
            </div>

            {/* Numéro de téléphone de l'étudiant */}
            <div className="form-group">
              <label htmlFor="telephone">Numéro de Téléphone :</label>
              <input type="tel" id="telephone" name="telephone" value={formData.telephone} onChange={handleChange} className="form-control" />
              {errors.telephone && <small className="text-danger">{errors.telephone}</small>}
            </div>

            {/* Nom du parent */}
            <div className="form-group">
              <label htmlFor="parentName">Nom du Parent :</label>
              <input type="text" id="parentName" name="parentName" value={formData.parentName} onChange={handleChange} className="form-control" />
              {errors.parentName && <small className="text-danger">{errors.parentName}</small>}
            </div>

            {/* CIN du parent */}
            <div className="form-group">
              <label htmlFor="parentCin">CIN du Parent :</label>
              <input type="text" id="parentCin" name="parentCin" value={formData.parentCin} onChange={handleChange} className="form-control" />
              {errors.parentCin && <small className="text-danger">{errors.parentCin}</small>}
            </div>

            {/* Numéro de téléphone du parent */}
            <div className="form-group">
              <label htmlFor="parentTelephone">Numéro de Téléphone du Parent :</label>
              <input type="tel" id="parentTelephone" name="parentTelephone" value={formData.parentTelephone} onChange={handleChange} className="form-control" />
              {errors.parentTelephone && <small className="text-danger">{errors.parentTelephone}</small>}
            </div>

            {/* Choix de l'abonnement */}
            <div className="form-group">
              <label htmlFor="abonnement">Sélectionner un Abonnement :</label>
              <select id="abonnement" name="abonnement" value={formData.abonnement} onChange={handleChange} className="form-control">
                <option value="1">Abonnement 1: Vidéos et Exercices (10$)</option>
                <option value="2">Abonnement 2: Vidéos, Exercices, et Sessions Enregistrées (25$)</option>
                <option value="3">Abonnement 3: Accès complet (50$)</option>
              </select>
            </div>

            {/* Boutons de soumission */}
            <div className="form-group mt-4">
              <button type="submit" className="btn btn-primary">
                S'inscrire
              </button>
            </div>
            
          </form>
          </div>
        </div>
      </div>
    </div>
    </div>
    </div>
  );
};

export default Inscription;
