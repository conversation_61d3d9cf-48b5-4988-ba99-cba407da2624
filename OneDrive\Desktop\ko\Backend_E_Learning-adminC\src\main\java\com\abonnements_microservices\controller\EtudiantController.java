package com.abonnements_microservices.controller;

import com.abonnements_microservices.dto.MatiereDTO;
import com.abonnements_microservices.model.Abonnement;
import org.springframework.security.oauth2.jwt.Jwt;

import com.abonnements_microservices.model.Etudiant;
import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.repo.AbonnementRepository;
import com.abonnements_microservices.repo.EtudiantRepository;
import com.abonnements_microservices.services.AbonnementService;
import com.abonnements_microservices.services.EtudiantService;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.Map;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/etudiants")
@CrossOrigin(origins = {"http://localhost:3036", "http://localhost:3000"})
@Slf4j
public class EtudiantController {

    @Autowired
    private EtudiantService etudiantService;
    @Autowired
    private EtudiantRepository etudiantRepository;

    @Autowired
    private AbonnementService abonnementService;
    @Autowired
    private AbonnementRepository abonnementRepository;
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createEtudiant(@RequestBody Map<String, Object> request) {
        try {
            log.debug("Creating new etudiant with data: {}", request);

            Etudiant etudiant = new Etudiant();
            etudiant.setUsername((String) request.get("username"));
            etudiant.setFirstName((String) request.get("firstName"));
            etudiant.setLastName((String) request.get("lastName"));
            etudiant.setEmail((String) request.get("email"));
            etudiant.setPassword((String) request.get("password"));
            etudiant.setPhoneNumber((String) request.get("phoneNumber"));

            Long niveauId = request.get("niveauId") != null ? ((Number) request.get("niveauId")).longValue() : null;

            @SuppressWarnings("unchecked")
            List<Integer> rawAbonnementIds = (List<Integer>) request.get("abonnementIds");
            List<Long> abonnementIds = rawAbonnementIds != null ?
                    rawAbonnementIds.stream().map(Integer::longValue).collect(Collectors.toList()) :
                    null;

            Etudiant created = etudiantService.createEtudiant(etudiant, abonnementIds, niveauId);
            return ResponseEntity.ok(created);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid input data for etudiant creation: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("message", e.getMessage()));
        } catch (EntityNotFoundException e) {
            log.warn("Entity not found during etudiant creation: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error creating etudiant: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("message", "Failed to create etudiant", "error", e.getMessage()));
        }
    }

    @GetMapping
    public ResponseEntity<List<Etudiant>> getAllEtudiants() {
        return ResponseEntity.ok(etudiantService.getAllEtudiants());
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getEtudiantById(@PathVariable Long id) {
        return etudiantService.getEtudiantById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateEtudiant(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        try {
            Etudiant etudiant = new Etudiant();
            etudiant.setFirstName((String) request.get("firstName"));
            etudiant.setLastName((String) request.get("lastName"));
            etudiant.setEmail((String) request.get("email"));
            etudiant.setPhoneNumber((String) request.get("phoneNumber"));

            Long niveauId = request.get("niveauId") != null ? ((Number) request.get("niveauId")).longValue() : null;

            @SuppressWarnings("unchecked")
            List<Integer> rawAbonnementIds = (List<Integer>) request.get("abonnementIds");
            List<Long> abonnementIds = rawAbonnementIds != null ?
                    rawAbonnementIds.stream().map(Integer::longValue).collect(Collectors.toList()) :
                    null;

            Etudiant updated = etudiantService.updateEtudiant(id, etudiant, abonnementIds, niveauId);
            return ResponseEntity.ok(updated);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error updating etudiant: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of("message", "Failed to update etudiant", "error", e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteEtudiant(@PathVariable Long id) {
        try {
            etudiantService.deleteEtudiant(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error deleting etudiant: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                .body(Map.of(
                    "message", "Failed to delete etudiant",
                    "error", e.getMessage()
                ));
        }
    }

    @GetMapping("/search")
    public ResponseEntity<List<Etudiant>> searchEtudiants(@RequestParam String query) {
        return ResponseEntity.ok(etudiantService.searchEtudiants(query));
    }
 /*   @GetMapping("/{id}/abonnement")
    @PreAuthorize("hasAnyRole('ADMIN', 'ETUDIANT')")
public ResponseEntity<List<Abonnement>> getAbonnementsByEtudiantId(@PathVariable Long id) {
    try {
        List<Abonnement> abonnements = etudiantService.getAbonnementByEtudiantId(id);
        return ResponseEntity.ok(abonnements);
    } catch (EntityNotFoundException e) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
    }
}*/
    @GetMapping("/{id}/abonnement")
    @PreAuthorize("hasAnyRole('ADMIN', 'ETUDIANT')")
public ResponseEntity<List<Abonnement>> getAbonnementByEtudiantId(@PathVariable Long id) {
    try {
        List<Abonnement> abonnement = etudiantService.getAbonnementByEtudiantId(id);
        return ResponseEntity.ok(abonnement);
    } catch (EntityNotFoundException e) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
    }
}
    @GetMapping("/me")
    @PreAuthorize("hasAnyRole('ADMIN', 'ETUDIANT')")
    public ResponseEntity<?> getCurrentEtudiant(Authentication authentication) {
        try {
            if (authentication == null || authentication.getPrincipal() == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of("message", "Non authentifié"));
            }

            // Get username from JWT token
            Jwt jwt = (Jwt) authentication.getPrincipal();
            String username = jwt.getClaimAsString("preferred_username");
            String email = jwt.getClaimAsString("email");

            // Try to find by username first
            Optional<Etudiant> etudiantOpt = etudiantRepository.findByUsername(username);

            // If not found by username, try by email
            if (!etudiantOpt.isPresent() && email != null) {
                etudiantOpt = etudiantRepository.findByEmail(email);
            }

            if (etudiantOpt.isPresent()) {
                Etudiant etudiant = etudiantOpt.get();
                // Initialize collections to avoid lazy loading issues
                Hibernate.initialize(etudiant.getAbonnements());
                if (etudiant.getNiveau() != null) {
                    Hibernate.initialize(etudiant.getNiveau());
                }
                return ResponseEntity.ok(etudiant);
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Étudiant non trouvé pour l'utilisateur connecté"));
            }
        } catch (Exception e) {
            log.error("Error retrieving current etudiant: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("message", "Erreur lors de la récupération de l'étudiant", "error", e.getMessage()));
        }
    }

    @GetMapping("/me/abonnements")
    @PreAuthorize("hasAnyRole('ADMIN', 'ETUDIANT')")
    public ResponseEntity<?> getMyAbonnements(Authentication authentication) {
    	Long etudiantId = Long.parseLong(((Jwt) authentication.getPrincipal()).getSubject());
    	List<Abonnement> abonnements = abonnementService.getAbonnementsByEtudiantId(etudiantId);
        if (abonnements == null || abonnements.isEmpty()) {
            return ResponseEntity.notFound().build(); // ❌ Ceci déclenche 404
        }

        return ResponseEntity.ok(abonnements); // ✅ Avoir au moins une liste vide
    }


    @GetMapping("/{id}/abonnements-matieres")
    @PreAuthorize("hasAnyRole('ADMIN', 'ETUDIANT')")
    public ResponseEntity<?> getAbonnementsEtMatieres(@PathVariable Long id) {
        try {
            // Utilisation d'EntityGraph pour charger 'abonnement', 'niveau' et 'image' en même temps
            Etudiant etudiant = etudiantRepository.findById(id)
                    .orElseThrow(() -> new EntityNotFoundException("Étudiant non trouvé avec l'ID : " + id));

            List<Abonnement> abonnements = etudiant.getAbonnements();

            List<MatiereDTO> matieresDTO = abonnements.stream()
                    .flatMap(abonnement -> abonnement.getMatieres().stream())
                    .distinct()
                    .map(MatiereDTO::new)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(matieresDTO);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of("message", e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                    "message", "Erreur lors de la récupération des matières",
                    "error", e.getMessage()
            ));
        }
    }


    @GetMapping("/{id}/matieres")
    @PreAuthorize("hasAnyRole('ADMIN', 'ETUDIANT')")
    public ResponseEntity<List<MatiereDTO>> getMatieresByEtudiant(@PathVariable Long id) {
        return etudiantRepository.findById(id)
                .map(etudiant -> {
                    List<MatiereDTO> matieres = etudiant.getAbonnements().stream()
                            .flatMap(abonnement -> abonnement.getMatieres().stream())
                            .distinct()
                            .map(MatiereDTO::new)
                            .collect(Collectors.toList());
                    return ResponseEntity.ok(matieres);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Check if a student has access to courses
     */
    @GetMapping("/{id}/access/courses")
    @PreAuthorize("hasAnyRole('ADMIN', 'ETUDIANT')")
    public ResponseEntity<Map<String, Boolean>> checkCoursesAccess(@PathVariable Long id) {
        return etudiantRepository.findById(id)
                .map(etudiant -> {
                    boolean hasAccess = etudiant.hasAccessToCourses();
                    return ResponseEntity.ok(Map.of("hasAccess", hasAccess));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Check if a student has access to recordings
     */
    @GetMapping("/{id}/access/recordings")
    @PreAuthorize("hasAnyRole('ADMIN', 'ETUDIANT')")
    public ResponseEntity<Map<String, Boolean>> checkRecordingsAccess(@PathVariable Long id) {
        return etudiantRepository.findById(id)
                .map(etudiant -> {
                    boolean hasAccess = etudiant.hasAccessToRecordings();
                    return ResponseEntity.ok(Map.of("hasAccess", hasAccess));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Check if a student has access to live sessions
     */
    @GetMapping("/{id}/access/livesessions")
    @PreAuthorize("hasAnyRole('ADMIN', 'ETUDIANT')")
    public ResponseEntity<Map<String, Boolean>> checkLiveSessionsAccess(@PathVariable Long id) {
        return etudiantRepository.findById(id)
                .map(etudiant -> {
                    boolean hasAccess = etudiant.hasAccessToLiveSessions();
                    return ResponseEntity.ok(Map.of("hasAccess", hasAccess));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Check if a student has access to a specific matiere
     */
    @GetMapping("/{id}/access/matiere/{matiereId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'ETUDIANT')")
    public ResponseEntity<Map<String, Boolean>> checkMatiereAccess(
            @PathVariable Long id,
            @PathVariable Long matiereId) {
        return etudiantRepository.findById(id)
                .map(etudiant -> {
                    boolean hasAccess = etudiant.hasAccessToMatiere(matiereId);
                    return ResponseEntity.ok(Map.of("hasAccess", hasAccess));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get all access permissions for a student in one call
     */
    @GetMapping("/{id}/access")
    @PreAuthorize("hasAnyRole('ADMIN', 'ETUDIANT')")
    public ResponseEntity<Map<String, Boolean>> getAllAccessPermissions(@PathVariable Long id) {
        return etudiantRepository.findById(id)
                .map(etudiant -> {
                    Map<String, Boolean> permissions = new HashMap<>();
                    permissions.put("courses", etudiant.hasAccessToCourses());
                    permissions.put("recordings", etudiant.hasAccessToRecordings());
                    permissions.put("liveSessions", etudiant.hasAccessToLiveSessions());
                    return ResponseEntity.ok(permissions);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get current student's access permissions
     */
    @GetMapping("/me/access")
    @PreAuthorize("hasAnyRole('ADMIN', 'ETUDIANT')")
    public ResponseEntity<?> getCurrentStudentAccessPermissions(Authentication authentication) {
        try {
            if (authentication == null || authentication.getPrincipal() == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of("message", "Non authentifié"));
            }

            // Get username from JWT token
            Jwt jwt = (Jwt) authentication.getPrincipal();
            String username = jwt.getClaimAsString("preferred_username");
            String email = jwt.getClaimAsString("email");

            // Try to find by username first
            Optional<Etudiant> etudiantOpt = etudiantRepository.findByUsername(username);

            // If not found by username, try by email
            if (!etudiantOpt.isPresent() && email != null) {
                etudiantOpt = etudiantRepository.findByEmail(email);
            }

            if (etudiantOpt.isPresent()) {
                Etudiant etudiant = etudiantOpt.get();
                Map<String, Boolean> permissions = new HashMap<>();
                permissions.put("courses", etudiant.hasAccessToCourses());
                permissions.put("recordings", etudiant.hasAccessToRecordings());
                permissions.put("liveSessions", etudiant.hasAccessToLiveSessions());
                return ResponseEntity.ok(permissions);
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("message", "Étudiant non trouvé pour l'utilisateur connecté"));
            }
        } catch (Exception e) {
            log.error("Error retrieving current student access permissions: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("message", "Erreur lors de la récupération des permissions d'accès", "error", e.getMessage()));
        }
    }
}