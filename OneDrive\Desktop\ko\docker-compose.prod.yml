version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: elearning-mysql-prod
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data_prod:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./mysql/conf.d:/etc/mysql/conf.d
    networks:
      - elearning-network-prod
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Keycloak Authentication Server
  keycloak:
    image: quay.io/keycloak/keycloak:22.0.4
    container_name: elearning-keycloak-prod
    restart: always
    environment:
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
      KC_DB: mysql
      KC_DB_URL: ***********************************
      KC_DB_USERNAME: ${KEYCLOAK_DB_USER}
      KC_DB_PASSWORD: ${KEYCLOAK_DB_PASSWORD}
      KC_HOSTNAME: ${KEYCLOAK_HOSTNAME}
      KC_HOSTNAME_PORT: ${KEYCLOAK_PORT:-8080}
      KC_HOSTNAME_STRICT: true
      KC_HOSTNAME_STRICT_HTTPS: true
      KC_HTTP_ENABLED: false
      KC_HTTPS_CERTIFICATE_FILE: /opt/keycloak/conf/server.crt.pem
      KC_HTTPS_CERTIFICATE_KEY_FILE: /opt/keycloak/conf/server.key.pem
      KC_HEALTH_ENABLED: true
      KC_METRICS_ENABLED: true
      KC_LOG_LEVEL: INFO
    volumes:
      - ./ssl:/opt/keycloak/conf
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - elearning-network-prod
    command: start
    healthcheck:
      test: ["CMD-SHELL", "curl -f https://localhost:8443/health/ready || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Backend Spring Boot Application
  backend:
    build:
      context: ./Backend_E_Learning-adminC
      dockerfile: Dockerfile
    container_name: elearning-backend-prod
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: production
      SPRING_DATASOURCE_URL: ***********************/${MYSQL_DATABASE}
      SPRING_DATASOURCE_USERNAME: ${MYSQL_USER}
      SPRING_DATASOURCE_PASSWORD: ${MYSQL_PASSWORD}
      KEYCLOAK_AUTH_SERVER_URL: https://${KEYCLOAK_HOSTNAME}:${KEYCLOAK_PORT:-8080}
      KEYCLOAK_REALM: ${KEYCLOAK_REALM}
      KEYCLOAK_RESOURCE: ${KEYCLOAK_CLIENT_ID}
      KEYCLOAK_CREDENTIALS_SECRET: ${KEYCLOAK_CLIENT_SECRET}
      KEYCLOAK_ADMIN_USERNAME: ${KEYCLOAK_ADMIN}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
      JAVA_OPTS: "-Xmx1g -Xms512m -XX:+UseG1GC"
    depends_on:
      mysql:
        condition: service_healthy
      keycloak:
        condition: service_healthy
    networks:
      - elearning-network-prod
    volumes:
      - backend_logs_prod:/app/logs
      - ./ssl:/app/ssl:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1.5G
        reservations:
          memory: 512M

  # Frontend User Application
  frontend-user:
    build:
      context: ./Frontend_User_E_Learning-adminC
      dockerfile: Dockerfile
    container_name: elearning-frontend-user-prod
    restart: always
    depends_on:
      - backend
    networks:
      - elearning-network-prod
    environment:
      REACT_APP_API_URL: https://${API_DOMAIN}
      REACT_APP_KEYCLOAK_URL: https://${KEYCLOAK_HOSTNAME}:${KEYCLOAK_PORT:-8080}
      REACT_APP_KEYCLOAK_REALM: ${KEYCLOAK_REALM}
      REACT_APP_KEYCLOAK_CLIENT_ID: ${KEYCLOAK_CLIENT_ID}
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Frontend Vitrine Application
  frontend-vitrine:
    build:
      context: ./vitrine-vitrine3
      dockerfile: Dockerfile
    container_name: elearning-frontend-vitrine-prod
    restart: always
    depends_on:
      - backend
    networks:
      - elearning-network-prod
    environment:
      REACT_APP_API_URL: https://${API_DOMAIN}
      REACT_APP_KEYCLOAK_URL: https://${KEYCLOAK_HOSTNAME}:${KEYCLOAK_PORT:-8080}
      REACT_APP_KEYCLOAK_REALM: ${KEYCLOAK_REALM}
      REACT_APP_KEYCLOAK_CLIENT_ID: ${KEYCLOAK_CLIENT_ID}
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: elearning-nginx-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend
      - frontend-user
      - frontend-vitrine
    networks:
      - elearning-network-prod
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Session Management (Optional)
  redis:
    image: redis:7-alpine
    container_name: elearning-redis-prod
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data_prod:/data
    networks:
      - elearning-network-prod
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

networks:
  elearning-network-prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  mysql_data_prod:
  backend_logs_prod:
  redis_data_prod:
