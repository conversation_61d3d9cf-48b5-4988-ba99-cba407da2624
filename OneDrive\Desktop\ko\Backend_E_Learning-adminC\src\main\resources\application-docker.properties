# Docker-specific configuration
server.port=8081

# Database Configuration for Docker
spring.datasource.url=${SPRING_DATASOURCE_URL:************************************}
spring.datasource.username=${SPRING_DATASOURCE_USERNAME:elearning_user}
spring.datasource.password=${SPRING_DATASOURCE_PASSWORD:elearning_password}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true

# Keycloak Configuration for Docker
spring.security.oauth2.resourceserver.jwt.issuer-uri=${KEYCLOAK_AUTH_SERVER_URL:http://keycloak:8080}/realms/${KEYCLOAK_REALM:e-learning-realm}
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=${K<PERSON>Y<PERSON>OAK_AUTH_SERVER_URL:http://keycloak:8080}/realms/${KEYCLOAK_REALM:e-learning-realm}/protocol/openid-connect/certs

# Keycloak Admin Client Configuration
keycloak.realm=${KEYCLOAK_REALM:e-learning-realm}
keycloak.auth-server-url=${KEYCLOAK_AUTH_SERVER_URL:http://keycloak:8080}
keycloak.resource=${KEYCLOAK_RESOURCE:e-learning}
keycloak.credentials.secret=${KEYCLOAK_CREDENTIALS_SECRET:aRjZ2PB2jnt6NVBypwy4dooa9Fx4lBku}
keycloak.bearer-only=true

# Keycloak Admin Credentials
keycloak.admin.username=${KEYCLOAK_ADMIN_USERNAME:admin}
keycloak.admin.password=${KEYCLOAK_ADMIN_PASSWORD:0000}

# Additional Keycloak Settings
keycloak.public-client=false
keycloak.ssl-required=external

# CORS Configuration for Docker
spring.web.cors.allowed-origins=http://localhost:3000,http://localhost:3001,http://frontend-user,http://frontend-vitrine
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# Actuator for health checks
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always
management.health.db.enabled=true

# Logging Configuration
logging.level.org.hibernate.SQL=INFO
logging.level.org.springframework.security=INFO
logging.level.com.abonnements_microservices=INFO
logging.level.org.keycloak=INFO
logging.file.name=/app/logs/application.log
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Twilio Configuration (same as original)
twilio.account.sid=**********************************
twilio.auth.token=b733e4cd2cdc3e02c28be4ecf9a8240d
twilio.phone.number=+***********

# Brevo (Sendinblue) Email Configuration (same as original)
brevo.api.key=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
brevo.sender.email=<EMAIL>
brevo.sender.name=E-Learning Platform

# 100ms Configuration (same as original)
hmsvideo.app.access.key=681bc3be4944f067313a9afc
hmsvideo.app.secret=B3GIwpC1IK4URwBG1ZHuyKvSe7gHdxA4U6M9y5Ttf-AV7m6LR4IpyfQIxr-RUTKigMKZDSAb3cefWXh-F5mcz3Bc26HTPCVu_XsATiS9x3GdOXj-4G68jVgTPFfI3GxtNLcIeSD76zOgYUK2MH6hDJm2wC1BxMMo6MkYJf7DnkI=
hmsvideo.management.token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************.Y05QRoBlwEXTlWvYygJxC9k6E0TxuQRj9O8Pmwqzn54
hmsvideo.token.expiration=86400

# OpenAI Configuration (same as original)
openai.api.key=********************************************************************************************************************************************************************
