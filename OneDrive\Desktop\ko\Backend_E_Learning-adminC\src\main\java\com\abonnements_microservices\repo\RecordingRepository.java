package com.abonnements_microservices.repo;

import com.abonnements_microservices.model.Recording;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RecordingRepository extends JpaRepository<Recording, Long> {
    List<Recording> findByMatiereIdMatiere(Long matiereId);
    List<Recording> findByEnseignantId(Long enseignantId);
    List<Recording> findByNiveauId(Long niveauId);
    List<Recording> findByMatiere_IdMatiereAndNiveau_Id(Long matiereId, Long niveauId);
}
