import React, { useState } from 'react';

const RegisterStudent = () => {
    const [formData, setFormData] = useState({
        username: '',
        email: '',
        password: '',
    });

    const handleChange = (e) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        const response = await fetch("http://localhost:8081/api/auth/register-student", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(formData),
        });

        if (response.ok) {
            alert("Inscription réussie !");
        } else {
            alert("Erreur d'inscription");
        }
    };

    return (
        <div>
            <h2>Inscription Étudiant</h2>
            <form onSubmit={handleSubmit}>
                <input type="text" name="username" placeholder="Nom d'utilisateur" onChange={handleChange} required />
                <input type="email" name="email" placeholder="Email" onChange={handleChange} required />
                <input type="password" name="password" placeholder="Mot de passe" onChange={handleChange} required />
                <button type="submit">S'inscrire</button>
            </form>
        </div>
    );
};

export default RegisterStudent;
