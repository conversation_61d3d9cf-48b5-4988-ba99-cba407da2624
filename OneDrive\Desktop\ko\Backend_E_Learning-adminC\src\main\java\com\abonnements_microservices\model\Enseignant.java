package com.abonnements_microservices.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.ArrayList;
import java.util.List;

@Entity
@Data
@NoArgsConstructor
@Table(name = "enseignant")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Enseignant {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false, nullable = false)
    private Long id;

    private String username;
    private String firstName;
    private String lastName;
    private String email;
    private String password;
    private String role;
    private String phoneNumber;
    
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "enseignant_niveaux",
        joinColumns = @JoinColumn(name = "enseignant_id"),
        inverseJoinColumns = @JoinColumn(name = "niveau_id")
    )
    @JsonIgnoreProperties({"enseignants", "matieres"})
    private List<Niveau> niveaux = new ArrayList<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "enseignant_matieres",
        joinColumns = @JoinColumn(name = "enseignant_id"),
        inverseJoinColumns = @JoinColumn(name = "matiere_id")
    )
    @JsonIgnoreProperties({"enseignants", "chapitres", "niveau", "abonnement"})
    private List<Matiere> matieres = new ArrayList<>();
    
    // Helper methods for managing relationships
    public void addMatiere(Matiere matiere) {
        this.matieres.add(matiere);
        matiere.getEnseignants().add(this);
    }

    public void removeMatiere(Matiere matiere) {
        this.matieres.remove(matiere);
        matiere.getEnseignants().remove(this);
    }

    public void addNiveau(Niveau niveau) {
        this.niveaux.add(niveau);
        niveau.getEnseignants().add(this);
    }

    public void removeNiveau(Niveau niveau) {
        this.niveaux.remove(niveau);
        niveau.getEnseignants().remove(this);
    }

    @PrePersist
    public void prePersist() {
        if (this.role == null) {
            this.role = "ENSEIGNANT";
        }
    }
}