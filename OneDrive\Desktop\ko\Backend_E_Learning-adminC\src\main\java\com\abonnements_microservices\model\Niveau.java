package com.abonnements_microservices.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Data
@NoArgsConstructor
@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
public class Niveau {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column(nullable = false, unique = true)
	@NotBlank(message = "Le nom du niveau est obligatoire")
	private String nom;
	@OneToMany(mappedBy = "niveau", cascade = CascadeType.ALL, orphanRemoval = true)
	@JsonIgnoreProperties({ "niveau" })
    private Set<Etudiant> etudiants = new HashSet<>();

	/*@OneToMany(mappedBy = "niveau", cascade = CascadeType.ALL, orphanRemoval = true)
	//@JsonIgnoreProperties("niveau")
	@JsonManagedReference
private List<MatiereNiveau> matiereNiveaux = new ArrayList<>();
*/
	@OneToMany(mappedBy = "niveau", cascade = CascadeType.ALL, orphanRemoval = true)
	@JsonManagedReference("niveau-matiere")
    private Set<MatiereNiveau> matiereNiveaux = new HashSet<>();

	/*@ManyToMany(mappedBy = "niveaux", fetch = FetchType.LAZY)
	@JsonIgnoreProperties({ "niveaux", "matieres", "password" })
	private List<Enseignant> enseignants = new ArrayList<>();*/
	@ManyToMany(mappedBy = "niveaux", fetch = FetchType.LAZY)
	@JsonIgnore
    private Set<Enseignant> enseignants = new HashSet<>();

	/*public void addMatiere(Matiere matiere) {
		matieres.add(matiere);
		matiere.getNiveaux().add(this); // Ajout du niveau à la liste dans Matiere
	}

	public void removeMatiere(Matiere matiere) {
		matieres.remove(matiere);
		matiere.getNiveaux().remove(this); // Suppression du niveau de la liste
	}*/

}
