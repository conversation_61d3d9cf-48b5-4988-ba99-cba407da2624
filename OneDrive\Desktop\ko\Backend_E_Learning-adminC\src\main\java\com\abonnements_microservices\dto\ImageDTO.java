package com.abonnements_microservices.dto;

import com.abonnements_microservices.model.Image;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ImageDTO {
    private Long idImage;
    private String name;
    private String type;
    private byte[] image; // ➕ ce champ manquait

    // Constructeur depuis Image
    public ImageDTO(Image image) {
        this.idImage = image.getIdImage();
        this.name = image.getName();
        this.type = image.getType();
        this.image = image.getImage(); // ➕ on copie aussi le contenu binaire
    }

    // Conversion vers entité
    public Image toEntity() {
        return Image.builder()
                .idImage(this.idImage)
                .name(this.name)
                .type(this.type)
                .image(this.image) // ✅ maintenant ça compile
                .build();
    }
}
