@echo off
REM E-Learning Platform Docker Startup Script for Windows

echo 🚀 Starting E-Learning Platform...

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker first.
    pause
    exit /b 1
)

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

REM Create necessary directories
echo 📁 Creating necessary directories...
if not exist "mysql\init" mkdir mysql\init
if not exist "Backend_E_Learning-adminC\logs" mkdir Backend_E_Learning-adminC\logs

REM Pull latest images
echo 📥 Pulling latest Docker images...
docker-compose pull

REM Build and start services
echo 🔨 Building and starting services...
docker-compose up --build -d

REM Wait for services to be ready
echo ⏳ Waiting for services to be ready...
timeout /t 30 /nobreak >nul

REM Check service status
echo 🔍 Checking service status...
docker-compose ps

echo.
echo ✅ E-Learning Platform is starting up!
echo.
echo 📋 Service URLs:
echo    🗄️  MySQL Database:     localhost:3306
echo    🔐 Keycloak:           http://localhost:8080
echo    🖥️  Backend API:        http://localhost:8081
echo    👥 User Frontend:      http://localhost:3000
echo    🌐 Vitrine Frontend:   http://localhost:3001
echo.
echo 🔑 Default Keycloak Admin Credentials:
echo    Username: admin
echo    Password: 0000
echo.
echo 📊 To view logs: docker-compose logs -f [service-name]
echo 🛑 To stop: docker-compose down
echo 🗑️  To stop and remove volumes: docker-compose down -v
echo.
pause
