package com.abonnements_microservices.repo;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.abonnements_microservices.model.Question;
import com.abonnements_microservices.model.User;

@Repository
public interface QuestionRepository extends JpaRepository<Question, Long> {
    
    Page<Question> findAllByOrderByDateCreationDesc(Pageable pageable);
    
    Page<Question> findByAuteurOrderByDateCreationDesc(User auteur, Pageable pageable);
    
    @Query("SELECT q FROM Question q WHERE q.matiere.id = :matiereId ORDER BY q.dateCreation DESC")
    Page<Question> findByMatiereIdOrderByDateCreationDesc(@Param("matiereId") Long matiereId, Pageable pageable);
    
    @Query("SELECT q FROM Question q WHERE LOWER(q.titre) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(q.contenu) LIKE LOWER(CONCAT('%', :keyword, '%')) ORDER BY q.dateCreation DESC")
    Page<Question> searchQuestions(@Param("keyword") String keyword, Pageable pageable);
    
    List<Question> findTop5ByOrderByDateCreationDesc();
    
    List<Question> findTop5ByOrderByVuesDesc();
}
