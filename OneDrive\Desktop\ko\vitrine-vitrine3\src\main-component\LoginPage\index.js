import React from 'react';
import Grid from "@mui/material/Grid";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../contexts/KeycloakProvider";
import './style.scss';

const LoginPage = () => {
  /*const navigate = useNavigate();
  const auth = useAuth();

  if (!auth) {
    return <div>Loading authentication...</div>; // Prevents crash if auth is null
  }

  const { authenticated, keycloak, initialized } = auth;

  if (authenticated) {
    navigate('/home');
    return null;
  }

  return (
    <Grid className="loginWrapper">
      <Grid className="loginForm">
        <h2>Sign In</h2>
        {!authenticated && initialized ? (
          <button onClick={() => keycloak?.login && keycloak.login()}>Login with Keycloak</button>
        ) : (
          <p>Redirecting...</p>
        )}
      </Grid>
    </Grid>
  );*/
};

export default LoginPage;
