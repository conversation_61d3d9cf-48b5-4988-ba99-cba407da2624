import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useKeycloak } from '@react-keycloak/web';
import "./login.css";
import myImage from "../../images/pattern/output-onlinepngtools.png";

const Login = () => {
  const { keycloak, initialized } = useKeycloak();
  const navigate = useNavigate();

  useEffect(() => {
    if (initialized) {
      if (keycloak.authenticated) {
        navigate('/dashboard');
      }
    }
  }, [keycloak.authenticated, initialized, navigate]);

  const handleLogin = () => {
    try {
      keycloak.login({
        redirectUri: window.location.origin + '/dashboard'
      });
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  if (!initialized) {
    return (
      <div style={{
        height: '100vh',
        width: '100vw',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f5f5f5'
      }}>
        <div>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <div className="mt-2">Preparing login...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="fix-wrapper">
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-lg-5 col-md-6">
            <div className="card mb-0 h-auto">
              <div className="card-body">
                <div className="text-center mb-2">
                  <div>
                    <svg width="250" height="56" viewBox="0 0 250 56">
                      <g transform="translate(20, 10)">
                        <text x="70" y="35" fontSize="32" fontFamily="'Inter', sans-serif" fontWeight="bold">
                          DEUTZA
                        </text>
                      </g>
                      <image className="image" href={myImage} x="0" y="-7" width="80" height="70" />
                    </svg>
                  </div>
                </div>
                <h4 className="text-center mb-4">Sign in your account</h4>
                <div className="text-center mt-4">
                  <button
                    type="button"
                    className="btn btn-primary btn-block"
                    onClick={handleLogin}
                  >
                    Sign In with Keycloak
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
