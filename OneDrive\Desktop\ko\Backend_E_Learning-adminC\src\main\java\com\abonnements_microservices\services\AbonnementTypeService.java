package com.abonnements_microservices.services;

import com.abonnements_microservices.dto.AbonnementTypeDTO;
import com.abonnements_microservices.model.AbonnementType;
import com.abonnements_microservices.repo.AbonnementTypeRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class AbonnementTypeService {

    @Autowired
    private AbonnementTypeRepository abonnementTypeRepository;

    @Transactional(readOnly = true)
    public List<AbonnementTypeDTO> getAllAbonnementTypes() {
        return abonnementTypeRepository.findAll().stream()
                .map(AbonnementTypeDTO::fromEntity)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public AbonnementTypeDTO getAbonnementTypeById(Long id) {
        return abonnementTypeRepository.findById(id)
                .map(AbonnementTypeDTO::fromEntity)
                .orElseThrow(() -> new EntityNotFoundException("AbonnementType not found with id: " + id));
    }

    @Transactional
    public AbonnementTypeDTO createAbonnementType(AbonnementTypeDTO dto) {
        AbonnementType type = dto.toEntity();
        type = abonnementTypeRepository.save(type);
        return AbonnementTypeDTO.fromEntity(type);
    }

    @Transactional
    public AbonnementTypeDTO updateAbonnementType(Long id, AbonnementTypeDTO dto) {
        AbonnementType type = abonnementTypeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("AbonnementType not found with id: " + id));
        
        type.setNom(dto.getNom());
        type.setDescription(dto.getDescription());
        type.setHasCourses(dto.isHasCourses());
        type.setHasRecordings(dto.isHasRecordings());
        type.setHasLiveSessions(dto.isHasLiveSessions());
        
        type = abonnementTypeRepository.save(type);
        return AbonnementTypeDTO.fromEntity(type);
    }

    @Transactional
    public void deleteAbonnementType(Long id) {
        if (!abonnementTypeRepository.existsById(id)) {
            throw new EntityNotFoundException("AbonnementType not found with id: " + id);
        }
        abonnementTypeRepository.deleteById(id);
    }

    @Transactional(readOnly = true)
    public Page<AbonnementTypeDTO> getAllAbonnementTypesPage(int page, int size) {
        Page<AbonnementType> typePage = abonnementTypeRepository.findAll(PageRequest.of(page, size));
        return typePage.map(AbonnementTypeDTO::fromEntity);
    }
    
    // Initialize default subscription types if none exist
    @Transactional
    public void initializeDefaultTypes() {
        if (abonnementTypeRepository.count() == 0) {
            // Type 1: Basic - Only courses
            AbonnementType type1 = new AbonnementType();
            type1.setNom("Basic");
            type1.setDescription("Accès aux cours uniquement");
            type1.setHasCourses(true);
            type1.setHasRecordings(false);
            type1.setHasLiveSessions(false);
            abonnementTypeRepository.save(type1);
            
            // Type 2: Standard - Courses and recordings
            AbonnementType type2 = new AbonnementType();
            type2.setNom("Standard");
            type2.setDescription("Accès aux cours et aux enregistrements");
            type2.setHasCourses(true);
            type2.setHasRecordings(true);
            type2.setHasLiveSessions(false);
            abonnementTypeRepository.save(type2);
            
            // Type 3: Premium - Everything
            AbonnementType type3 = new AbonnementType();
            type3.setNom("Premium");
            type3.setDescription("Accès complet: cours, enregistrements et sessions live");
            type3.setHasCourses(true);
            type3.setHasRecordings(true);
            type3.setHasLiveSessions(true);
            abonnementTypeRepository.save(type3);
        }
    }
}
