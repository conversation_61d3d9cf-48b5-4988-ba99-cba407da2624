#!/bin/bash

# E-Learning Platform Docker Startup Script

echo "🚀 Starting E-Learning Platform..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p mysql/init
mkdir -p Backend_E_Learning-adminC/logs

# Pull latest images
echo "📥 Pulling latest Docker images..."
docker-compose pull

# Build and start services
echo "🔨 Building and starting services..."
docker-compose up --build -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check service status
echo "🔍 Checking service status..."
docker-compose ps

echo ""
echo "✅ E-Learning Platform is starting up!"
echo ""
echo "📋 Service URLs:"
echo "   🗄️  MySQL Database:     localhost:3306"
echo "   🔐 Keycloak:           http://localhost:8080"
echo "   🖥️  Backend API:        http://localhost:8081"
echo "   👥 User Frontend:      http://localhost:3000"
echo "   🌐 Vitrine Frontend:   http://localhost:3001"
echo ""
echo "🔑 Default Keycloak Admin Credentials:"
echo "   Username: admin"
echo "   Password: 0000"
echo ""
echo "📊 To view logs: docker-compose logs -f [service-name]"
echo "🛑 To stop: docker-compose down"
echo "🗑️  To stop and remove volumes: docker-compose down -v"
