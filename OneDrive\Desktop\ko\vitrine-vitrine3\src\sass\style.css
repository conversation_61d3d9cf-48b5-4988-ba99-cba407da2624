/*------------------------------------------------------------------
[Master Stylesheet]
Theme Name:     Eduko - Online Course & Education HTML5 Template
Version:        1.0.0
-------------------------------------------------------------------*/
/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------

1. General
	1.1 Theme Reset Style
	1.2 Global Elements

2. header
	2.1 topbar
	2.2 navigation

3. content
	3.1 wpo-hero-slider
	3.2 wpo-about-section
	3.3 wpo-courses-section
	3.4 wpo-popular-area
	3.5 wpo-testimonial-section
	3.6 wpo-team-section
	3.7 wpo-choose-section
	3.8 wpo-blog-section
	3.9 wpo-subscribe-section

4. wpo-footer

5. Home-style-2
   5.1 wpo-features-area
   5.2 wpo-about-section-s2
   5.3 wpo-courses-section-s2
   5.4 wpo-choose-section-s2
   5.6 wpo-subscribe-section-s2

6. Home-style-3
   6.1 wpo-features-area-s2
   6.2 wpo-about-section-s3
   6.3 wpo-courses-section-s3
   6.4 wpo-popular-area-s2
   6.5 wpo-event-area

7. Home-style-4
   7.1 wpo-about-section-s4
   7.2 wpo-fun-fact-section
   7.3 wpo-courses-section-s3
   7.4 wpo-popular-area-s3
   7.5 wpo-testimonial-section-s2
   7.6 wpo-blog-section-s2

8. Home-style-5
   8.1 wpo-features-area-s3
   8.2 wpo-about-section-s5
   8.3 wpo-fun-fact-section-s2
   8.4 wpo-courses-section-s5
   8.5 wpo-popular-area-s4

9. wpo-course-page

10. course-details-page

11. wpo-lesson-section

12. teacher-page

13. wpo-team-single

14. wpo-gallery-page

15. wpo-shop-page

16. wpo-shop-single-page

17. wpo-cart-page-style

18. wpo-checkout-page-style

19. wpo-faq-page

20. wpo-blog-pg-section

21. wpo-blog-single-section

22. wpo-contact-pg-section

23. error-404-section

24. wpo-terms-section

----------------------------------------------------------------*/
/*------------------------------------------------------------------
1. General
----------------------------------------------------------------*/
/*---------------------------
	Fonts
----------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Urbanist:wght@300;400;500;600;700;800;900&display=swap");
/* 1.1 Theme Reset Style */
html {
  font-size: 15px;
}

:root {
  scroll-behavior: unset;
}

body {
  font-family: "Urbanist";
  color: #625F71;
  background-color: #fff;
  font-size: 18px;
  font-size: 1.2rem;
  overflow-x: hidden;
}

@media (max-width: 767px) {
  body {
    font-size: 14px;
  }
}

p {
  font-size: 18px;
  color: #625F71;
  line-height: 1.8em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #1A1729;
  font-family: "Urbanist";
  font-weight: 500;
}

ul {
  padding-left: 0;
  margin: 0;
}

a {
  text-decoration: none;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

a:hover {
  text-decoration: none;
}

img {
  max-width: 100%;
}

.hidden {
  display: none;
}

.sr-only {
  display: none !important;
  opacity: 0;
  visibility: hidden;
}

/* 1.2 Global Elements */
.page-wrapper {
  position: relative;
  overflow: hidden;
}

.wow {
  visibility: hidden;
}

.fi:before {
  margin: 0;
}

.section-padding {
  padding: 120px 0;
}

@media (max-width: 991px) {
  .section-padding {
    padding: 90px 0;
  }
}

@media (max-width: 767px) {
  .section-padding {
    padding: 80px 0;
  }
}

.pt-100 {
  padding-top: 100px;
}

@media (max-width: 991px) {
  .pt-100 {
    padding-top: 80px;
  }
}

@media (max-width: 767px) {
  .pt-100 {
    padding-top: 70px;
  }
}

/*** contact form error handling ***/
.contact-validation-active .error-handling-messages {
  width: 100% !important;
  margin-top: 15px !important;
}

.contact-validation-active label.error {
  color: red;
  font-size: 0.93333rem;
  font-weight: normal;
  margin: 5px 0 0 0;
  text-align: left;
  display: block;
}

.contact-validation-active #c-loader,
.contact-validation-active #loader {
  display: none;
  margin-top: 10px;
}

.contact-validation-active #c-loader i,
.contact-validation-active #loader i {
  font-size: 30px;
  font-size: 2rem;
  color: #4540E1;
  display: inline-block;
  -webkit-animation: rotating linear 2s infinite;
  animation: rotating linear 2s infinite;
}

.contact-validation-active #success,
.contact-validation-active #c-success,
.contact-validation-active #c-error,
.contact-validation-active #error {
  width: 100%;
  color: #fff;
  padding: 5px 10px;
  font-size: 16px;
  text-align: center;
  display: none;
}

@media (max-width: 767px) {
  .contact-validation-active #success,
  .contact-validation-active #c-success,
  .contact-validation-active #c-error,
  .contact-validation-active #error {
    font-size: 15px;
  }
}

.contact-validation-active #c-success,
.contact-validation-active #success {
  background-color: #009a00;
  border-left: 5px solid green;
  margin-bottom: 5px;
}

.contact-validation-active #c-error,
.contact-validation-active #error {
  background-color: #ff1a1a;
  border-left: 5px solid red;
}

@-webkit-keyframes rotating {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes rotating {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.rotating {
  -webkit-animation: rotating 5s alternate infinite;
  animation: rotating 5s alternate infinite;
}

/*** back to top **/
.back-to-top {
  background-color: rgba(69, 64, 225, 0.7);
  width: 45px;
  height: 45px;
  line-height: 45px;
  border: 2px solid #4540E1;
  border-radius: 45px;
  text-align: center;
  display: none;
  position: fixed;
  z-index: 999;
  right: 15px;
  bottom: 15px;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

@media (max-width: 991px) {
  .back-to-top {
    width: 35px;
    height: 35px;
    line-height: 35px;
  }
}

.back-to-top:hover {
  background-color: #4540E1;
}

.back-to-top i {
  font-size: 18px;
  font-size: 1.2rem;
  color: #fff;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/** for popup image ***/
.mfp-wrap {
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 99999;
}

.mfp-with-zoom .mfp-container,
.mfp-with-zoom.mfp-bg {
  opacity: 0;
  -webkit-backface-visibility: hidden;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}

.mfp-with-zoom.mfp-ready .mfp-container {
  opacity: 1;
}

.mfp-with-zoom.mfp-ready.mfp-bg {
  opacity: 0.8;
}

.mfp-with-zoom.mfp-removing .mfp-container,
.mfp-with-zoom.mfp-removing.mfp-bg {
  opacity: 0;
}

/*** for fancybox video ***/
.fancybox-overlay {
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999 !important;
}

.fancybox-wrap {
  z-index: 99999 !important;
}

.wpo-section-title,
.wpo-section-title-s2,
.wpo-section-title-s3 {
  text-align: center;
  margin: 0 auto;
  margin-bottom: 60px;
  z-index: 1;
}

.wpo-section-title small,
.wpo-section-title-s2 small,
.wpo-section-title-s3 small {
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  color: #F2BC00;
}

.wpo-section-title h2,
.wpo-section-title-s2 h2,
.wpo-section-title-s3 h2 {
  margin: 0;
  font-size: 55px;
  font-style: normal;
  font-weight: 700;
  line-height: 75px;
  margin-top: 10px;
  z-index: 1;
}

@media (max-width: 991px) {
  .wpo-section-title h2,
  .wpo-section-title-s2 h2,
  .wpo-section-title-s3 h2 {
    font-size: 45px;
    line-height: 55px;
    margin-top: 10px;
  }
}

@media (max-width: 767px) {
  .wpo-section-title h2,
  .wpo-section-title-s2 h2,
  .wpo-section-title-s3 h2 {
    font-size: 32px;
    line-height: 40px;
    margin-top: 10px;
  }
}

@media (max-width: 330px) {
  .wpo-section-title h2,
  .wpo-section-title-s2 h2,
  .wpo-section-title-s3 h2 {
    font-size: 30px;
  }
}

.wpo-section-title h2 span,
.wpo-section-title-s2 h2 span,
.wpo-section-title-s3 h2 span {
  position: relative;
  z-index: -1;
}

.wpo-section-title h2 span .shape,
.wpo-section-title-s2 h2 span .shape,
.wpo-section-title-s3 h2 span .shape {
  position: absolute;
  top: -10px;
  left: -30px;
  width: 150%;
  z-index: -1;
}

.wpo-section-title h2 span .shape svg,
.wpo-section-title-s2 h2 span .shape svg,
.wpo-section-title-s3 h2 span .shape svg {
  fill: #F2BC00;
}

.wpo-section-title p,
.wpo-section-title-s2 p,
.wpo-section-title-s3 p {
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
}

.wpo-section-title-s2 h2 span .shape {
  top: -10px;
  left: -15px;
  width: 108%;
}

@media (max-width: 767px) {
  .wpo-section-title-s2 h2 span .shape {
    top: -4px;
    left: -10px;
    width: 108%;
  }
}

.wpo-section-title-s3 small {
  color: #625F71;
}

.theme-btn, .view-cart-btn,
.theme-btn-s2 {
  color: #1A1729;
  display: inline-block;
  font-weight: 500;
  padding: 15px 35px;
  border: 0;
  text-transform: capitalize;
  -webkit-backface-visibility: hidden;
  z-index: 1;
  position: relative;
  font-size: 18px;
  background: #F2BC00;
  font-family: "Urbanist";
  border-radius: 30px;
  font-weight: 600;
}

.theme-btn:after, .view-cart-btn:after,
.theme-btn-s2:after {
  position: absolute;
  content: '';
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-transition: opacity 0.5s ease-out;
  transition: opacity 0.5s ease-out;
  background: #e8b400;
  opacity: 0;
  z-index: -1;
  border-radius: 30px;
}

.theme-btn:hover, .view-cart-btn:hover, .theme-btn:focus, .view-cart-btn:focus, .theme-btn:active, .view-cart-btn:active,
.theme-btn-s2:hover,
.theme-btn-s2:focus,
.theme-btn-s2:active {
  color: #1A1729;
}

.theme-btn:hover:after, .view-cart-btn:hover:after,
.theme-btn-s2:hover:after {
  opacity: 1;
}

@media (max-width: 991px) {
  .theme-btn, .view-cart-btn,
  .theme-btn-s2 {
    font-size: 18px;
    font-size: 1.2rem;
  }
}

@media (max-width: 767px) {
  .theme-btn, .view-cart-btn,
  .theme-btn-s2 {
    padding: 12px 18px;
    font-size: 14px;
  }
}

.theme-btn-s2 {
  padding-right: 70px;
}

@media (max-width: 767px) {
  .theme-btn-s2 {
    padding-right: 50px;
  }
}

.theme-btn-s2:before {
  position: absolute;
  right: 5px;
  top: 5px;
  width: 46px;
  height: 46px;
  line-height: 46px;
  border-radius: 30px;
  background: #fff;
  content: "\e628";
  font-family: "themify";
  text-align: center;
}

@media (max-width: 767px) {
  .theme-btn-s2:before {
    width: 35px;
    height: 35px;
    line-height: 35px;
  }
}

.theme-btn-s3,
.theme-btn-s4,
.theme-btn-s5 {
  color: #fff;
  display: inline-block;
  padding: 15px 35px;
  padding-right: 75px;
  text-transform: capitalize;
  z-index: 1;
  position: relative;
  font-size: 20px;
  font-weight: 700;
  background: #15A280;
  color: #fff;
  -webkit-transition: all .4s ease-in-out;
  transition: all .4s ease-in-out;
  border-radius: 5px;
}

@media (max-width: 991px) {
  .theme-btn-s3,
  .theme-btn-s4,
  .theme-btn-s5 {
    font-size: 14px;
    font-size: 0.93333rem;
  }
}

.theme-btn-s3:before,
.theme-btn-s4:before,
.theme-btn-s5:before {
  position: absolute;
  right: 40px;
  top: 16px;
  content: "\f123";
  font-family: "Flaticon";
  text-align: center;
}

.theme-btn-s3:hover, .theme-btn-s3:focus, .theme-btn-s3:active,
.theme-btn-s4:hover,
.theme-btn-s4:focus,
.theme-btn-s4:active,
.theme-btn-s5:hover,
.theme-btn-s5:focus,
.theme-btn-s5:active {
  color: #fff;
  background: #11876b;
}

@media (max-width: 767px) {
  .theme-btn-s3,
  .theme-btn-s4,
  .theme-btn-s5 {
    padding: 12px 18px;
    padding-right: 40px;
    font-size: 13px;
  }
  .theme-btn-s3:before,
  .theme-btn-s4:before,
  .theme-btn-s5:before {
    right: 20px;
    top: 13px;
  }
}

.theme-btn-s4 {
  background: none;
  border: 1px solid #1A1729;
  color: #1A1729;
}

.theme-btn-s4:hover {
  background: #1A1729;
}

.theme-btn-s5 {
  background: #F9406C;
}

.theme-btn-s5:hover {
  background: #f82255;
}

.view-cart-btn {
  display: block;
  margin-top: 15px;
  border-radius: 5px;
  padding: 10px 45px;
}

.view-cart-btn:after {
  display: none;
}

.view-cart-btn:hover {
  background-color: #2722d7;
}

.view-cart-btn.s1 {
  background: #ddd;
}

.view-cart-btn.s1:hover {
  background-color: #c9c9c9;
}

.form input,
.form textarea,
.form select {
  border-color: #bfbfbf;
  border-radius: 0;
  outline: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #595959;
  font-style: normal;
}

.form input:focus,
.form textarea:focus,
.form select:focus {
  border-color: #4540E1;
  -webkit-box-shadow: 0 0 5px 0 #706ce8;
  -o-box-shadow: 0 0 5px 0 #706ce8;
  -ms-box-shadow: 0 0 5px 0 #706ce8;
  box-shadow: 0 0 5px 0 #706ce8;
}

.form ::-webkit-input-placeholder {
  font-style: 14px;
  font-style: italic;
  color: #595959;
}

.form :-moz-placeholder {
  font-style: 14px;
  font-style: italic;
  color: #595959;
}

.form ::-moz-placeholder {
  font-style: 14px;
  font-style: italic;
  color: #595959;
}

.form :-ms-input-placeholder {
  font-style: 14px;
  font-style: italic;
  color: #595959;
}

.form select {
  font-style: normal;
  background: url(../../images/select-icon.png) no-repeat right center;
  display: inline-block;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  cursor: pointer;
}

.form select::-ms-expand {
  /* for IE 11 */
  display: none;
}

.form ::-webkit-input-placeholder {
  /* Edge */
  font-style: normal;
}

.form :-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-style: normal;
}

.form ::-ms-input-placeholder {
  font-style: normal;
}

.form ::placeholder {
  font-style: normal;
}

.social-links {
  overflow: hidden;
}

.social-links li {
  float: left;
  width: 35px;
  height: 35px;
  margin-right: 1px;
}

.social-links li a {
  background-color: #706ce8;
  width: 35px;
  height: 35px;
  line-height: 35px;
  display: block;
  color: #fff;
  text-align: center;
}

.social-links li a:hover {
  background-color: #4540E1;
}

.wpo-page-title {
  background: #FCF9EF;
  min-height: 350px;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  z-index: 1;
  overflow: hidden;
}

@media (max-width: 767px) {
  .wpo-page-title {
    min-height: 250px;
  }
}

.wpo-page-title .shape-1 {
  position: absolute;
  left: 15%;
  top: 20%;
  z-index: -1;
  -webkit-animation: bounce 5s linear infinite;
          animation: bounce 5s linear infinite;
}

@media (max-width: 991px) {
  .wpo-page-title .shape-1 {
    top: 8%;
  }
}

@media (max-width: 575px) {
  .wpo-page-title .shape-1 {
    display: none;
  }
}

@media (max-width: 991px) {
  .wpo-page-title .shape-1 img {
    max-width: 50px;
  }
}

.wpo-page-title .shape-2 {
  position: absolute;
  left: 50px;
  bottom: 50px;
  z-index: -1;
  -webkit-animation: zoom-in-zoom-out 5s ease infinite;
          animation: zoom-in-zoom-out 5s ease infinite;
}

@media (max-width: 575px) {
  .wpo-page-title .shape-2 {
    display: none;
  }
}

@media (max-width: 991px) {
  .wpo-page-title .shape-2 img {
    max-width: 50px;
  }
}

.wpo-page-title .shape-3 {
  position: absolute;
  right: -30px;
  top: 40px;
  z-index: -1;
  -webkit-animation: bounceX 5s linear infinite;
          animation: bounceX 5s linear infinite;
}

@media (max-width: 575px) {
  .wpo-page-title .shape-3 {
    display: none;
  }
}

@media (max-width: 991px) {
  .wpo-page-title .shape-3 img {
    max-width: 50px;
  }
}

.wpo-page-title .shape-4 {
  position: absolute;
  right: 50px;
  bottom: 15%;
  z-index: -1;
  -webkit-animation: bounceX 7s linear infinite;
          animation: bounceX 7s linear infinite;
}

@media (max-width: 575px) {
  .wpo-page-title .shape-4 {
    display: none;
  }
}

@media (max-width: 991px) {
  .wpo-page-title .shape-4 img {
    max-width: 90px;
  }
}

.wpo-page-title .wpo-breadcumb-wrap {
  text-align: center;
}

.wpo-page-title .wpo-breadcumb-wrap h2 {
  font-size: 60px;
  color: #1A1729;
  line-height: 60px;
  margin-bottom: 20px;
  font-weight: 600;
}

@media (max-width: 767px) {
  .wpo-page-title .wpo-breadcumb-wrap h2 {
    font-size: 30px;
    line-height: 35px;
    margin-bottom: 10px;
  }
}

.wpo-page-title .wpo-breadcumb-wrap ol {
  padding-left: 0;
  margin-bottom: 0;
}

.wpo-page-title .wpo-breadcumb-wrap ol li {
  display: inline-block;
  padding: 0px 5px;
  padding-right: 20px;
  color: #625F71;
  position: relative;
  font-size: 20px;
  font-family: "Urbanist";
}

.wpo-page-title .wpo-breadcumb-wrap ol li:last-child {
  padding-right: 0;
}

@media (max-width: 767px) {
  .wpo-page-title .wpo-breadcumb-wrap ol li {
    font-size: 18px;
  }
}

.wpo-page-title .wpo-breadcumb-wrap ol li:after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: #625F71;
  border-radius: 50%;
}

.wpo-page-title .wpo-breadcumb-wrap ol li:last-child span {
  color: #625F71;
}

.wpo-page-title .wpo-breadcumb-wrap ol li:last-child:after {
  display: none;
}

.wpo-page-title .wpo-breadcumb-wrap ol li a {
  color: #625F71;
  font-size: 20px;
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-page-title .wpo-breadcumb-wrap ol li a:hover {
  color: #F2BC00;
}

.preloader {
  background: #1A1729;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 1000;
  overflow: hidden;
}

.preloader .vertical-centered-box {
  position: absolute;
  width: 100%;
  height: 100%;
  text-align: center;
}

.preloader .vertical-centered-box:after {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -0.25em;
}

.preloader .vertical-centered-box .content {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: inline-block;
  vertical-align: middle;
  text-align: left;
  font-size: 0;
}

.preloader * {
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.preloader .loader-circle {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  -webkit-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
          box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  margin-left: -60px;
  margin-top: -60px;
}

.preloader .loader-line-mask {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 60px;
  height: 120px;
  margin-left: -60px;
  margin-top: -60px;
  overflow: hidden;
  -webkit-transform-origin: 60px 60px;
  transform-origin: 60px 60px;
  -webkit-mask-image: -webkit-linear-gradient(top, #000000, rgba(0, 0, 0, 0));
  -webkit-animation: rotate 1.2s infinite linear;
  animation: rotate 1.2s infinite linear;
}

.preloader .loader-line-mask .loader-line {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  -webkit-box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5);
          box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5);
}

.preloader #particles-background,
.preloader #particles-foreground {
  left: -51%;
  top: -51%;
  width: 202%;
  height: 202%;
  -webkit-transform: scale3d(0.5, 0.5, 1);
  transform: scale3d(0.5, 0.5, 1);
}

.preloader #particles-background {
  background: #2c2d44;
  background-image: linear-gradient(45deg, #3f3251 2%, #002025 100%);
}

.preloader lesshat-selector {
  -lh-property: 0;
}

@-webkit-keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.preloader [not-existing] {
  zoom: 1;
}

.preloader lesshat-selector {
  -lh-property: 0;
}

@-webkit-keyframes fade {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.25;
  }
}

@keyframes fade {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.25;
  }
}

.preloader [not-existing] {
  zoom: 1;
}

.preloader lesshat-selector {
  -lh-property: 0;
}

@-webkit-keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.preloader [not-existing] {
  zoom: 1;
}

/*------------------------------------
	blog sidebar
------------------------------------*/
.blog-sidebar {
  /*** search-widget ***/
  /*** about-widget ***/
  /*** category-widget ***/
  /*** recent-post-widget ***/
  /*** instagram-widget ***/
  /*** tag-widget ***/
  /*** wpo-contact-widget ***/
}

@media screen and (min-width: 992px) {
  .blog-sidebar {
    padding-left: 45px;
  }
}

@media (max-width: 991px) {
  .blog-sidebar {
    margin-top: 80px;
    max-width: 400px;
  }
}

@media (max-width: 767px) {
  .blog-sidebar {
    margin-top: 60px;
    max-width: 400px;
  }
}

.blog-sidebar .widget h3 {
  font-size: 23px;
  font-size: 1.53333rem;
  color: #1A1729;
  margin: 0 0 1.3em;
  position: relative;
  text-transform: capitalize;
  font-family: "Urbanist";
  padding-bottom: 20px;
  font-weight: 600;
}

.blog-sidebar .widget h3:before {
  content: "";
  background-color: #4540E1;
  width: 55px;
  height: 4px;
  position: absolute;
  left: 0;
  bottom: 0;
  border-radius: 10px;
}

.blog-sidebar .widget h3:after {
  content: "";
  background-color: #f2f2f2;
  width: 80%;
  height: 4px;
  position: absolute;
  left: 65px;
  bottom: 0;
  border-radius: 10px;
}

.blog-sidebar > .widget + .widget {
  margin-top: 65px;
}

.blog-sidebar .search-widget form div {
  position: relative;
}

.blog-sidebar .search-widget input {
  background-color: #f2f2f2;
  height: 70px;
  font-size: 16px;
  font-size: 1.06667rem;
  padding: 6px 50px 6px 20px;
  border: 0;
  border-radius: 5px;
  -webkit-box-shadow: none;
          box-shadow: none;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.blog-sidebar .search-widget input:focus {
  background-color: rgba(69, 64, 225, 0.1);
}

.blog-sidebar .search-widget form button {
  background: transparent;
  font-size: 20px;
  font-size: 1.33333rem;
  color: #fff;
  background-color: #4540E1;
  border: 0;
  outline: 0;
  position: absolute;
  right: 10px;
  top: 52%;
  height: 50px;
  line-height: 50px;
  width: 50px;
  border-radius: 6px;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.blog-sidebar .about-widget {
  text-align: center;
  background-color: #f2f2f2;
  padding: 45px;
  position: relative;
  z-index: 1;
}

@media (max-width: 1200px) {
  .blog-sidebar .about-widget {
    padding: 35px;
  }
}

.blog-sidebar .about-widget .aw-shape {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
}

.blog-sidebar .about-widget .img-holder {
  margin-bottom: 25px;
}

.blog-sidebar .about-widget .img-holder img {
  border-radius: 50%;
  width: 100%;
  max-width: 200px;
}

.blog-sidebar .about-widget ul {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.blog-sidebar .about-widget ul li {
  margin-right: 10px;
}

.blog-sidebar .about-widget ul li:last-child {
  margin-right: 0;
}

.blog-sidebar .about-widget ul li a {
  text-decoration: none;
  display: block;
  width: 40px;
  height: 40px;
  line-height: 44px;
  background-color: #fff;
  border-radius: 50%;
}

.blog-sidebar .about-widget h4 {
  font-size: 25px;
  font-weight: 600;
  margin-bottom: 15px;
}

.blog-sidebar .about-widget p {
  font-size: 15px;
  font-size: 1rem;
  margin-bottom: 1.2em;
}

.blog-sidebar .about-widget a {
  font-family: "Urbanist";
  font-size: 14px;
  font-size: 0.93333rem;
  color: #1A1729;
  text-decoration: underline;
}

.blog-sidebar .about-widget a:hover {
  color: #4540E1;
}

.blog-sidebar .category-widget ul {
  list-style: none;
}

.blog-sidebar .category-widget ul li {
  font-size: 19px;
  font-size: 1.26667rem;
  position: relative;
}

@media (max-width: 767px) {
  .blog-sidebar .category-widget ul li {
    font-size: 16px;
    font-size: 1.06667rem;
  }
}

.blog-sidebar .category-widget ul li span {
  position: absolute;
  right: 0;
}

.blog-sidebar .category-widget ul > li + li {
  margin-top: 12px;
  padding-top: 12px;
}

.blog-sidebar .category-widget ul a {
  display: block;
  color: #474f62;
  font-size: 20px;
  font-family: "Urbanist";
}

.blog-sidebar .category-widget ul a span {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  background-color: #f2f2f2;
  text-align: center;
  border-radius: 50%;
  font-size: 18px;
}

@media (max-width: 767px) {
  .blog-sidebar .category-widget ul a span {
    font-size: 14px;
    font-size: 0.93333rem;
  }
}

.blog-sidebar .category-widget ul a:hover,
.blog-sidebar .category-widget ul li:hover:before {
  color: #4540E1;
}

.blog-sidebar .recent-post-widget .post {
  overflow: hidden;
}

.blog-sidebar .recent-post-widget .posts > .post + .post {
  margin-top: 15px;
  padding-top: 15px;
}

.blog-sidebar .recent-post-widget .post .img-holder {
  width: 90px;
  float: left;
}

.blog-sidebar .recent-post-widget .post .img-holder img {
  border-radius: 6px;
}

.blog-sidebar .recent-post-widget .post .details {
  width: calc(100% - 90px);
  float: left;
  padding-left: 20px;
}

.blog-sidebar .recent-post-widget .post h4 {
  font-size: 20px;
  font-size: 1.33333rem;
  font-weight: 500;
  line-height: 1.3em;
  margin: 0 0 0.3em;
  font-family: "Urbanist";
}

@media (max-width: 1199px) {
  .blog-sidebar .recent-post-widget .post h4 {
    margin: 0;
  }
}

@media (max-width: 767px) {
  .blog-sidebar .recent-post-widget .post h4 {
    font-size: 18px;
    font-size: 1.2rem;
  }
}

.blog-sidebar .recent-post-widget .post h4 a {
  display: inline-block;
  color: #1A1729;
}

.blog-sidebar .recent-post-widget .post h4 a:hover {
  color: #4540E1;
}

.blog-sidebar .recent-post-widget .post .details .date {
  font-size: 16px;
  font-size: 1.06667rem;
  color: #625F71;
}

.blog-sidebar .recent-post-widget .post .details .date i {
  display: inline-block;
  padding-right: 7px;
}

.blog-sidebar .wpo-instagram-widget ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  list-style: none;
}

.blog-sidebar .wpo-instagram-widget ul li {
  -ms-flex: 0 0 33.33%;
  -webkit-box-flex: 0;
  flex: 0 0 33.33%;
  max-width: 33.33%;
  margin-bottom: 5px;
  padding: 0px 3px;
}

.blog-sidebar .wpo-instagram-widget ul li img {
  width: 100%;
  border-radius: 6px;
}

.blog-sidebar .tag-widget ul {
  overflow: hidden;
  list-style: none;
}

.blog-sidebar .tag-widget ul li {
  float: left;
  margin: 0 8px 8px 0;
}

.blog-sidebar .tag-widget ul li a {
  font-size: 16px;
  font-size: 1.06667rem;
  display: inline-block;
  padding: 5px 18px;
  color: #1A1729;
  background: #f2f2f2;
  border-radius: 5px;
  font-family: "Urbanist";
  text-transform: uppercase;
}

.blog-sidebar .tag-widget ul li a:hover {
  background: #4540E1;
  color: #fff;
}

.blog-sidebar .wpo-contact-widget {
  border: 0;
  background: url(../../images/bg-2.jpg) no-repeat center center;
  position: relative;
  background-size: cover;
  z-index: 1;
  padding: 30px 40px;
}

.blog-sidebar .wpo-contact-widget:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(22, 57, 103, 0.9);
  content: "";
  z-index: -1;
}

@media (max-width: 1200px) {
  .blog-sidebar .wpo-contact-widget {
    padding: 20px;
  }
}

.blog-sidebar .wpo-contact-widget h2 {
  font-size: 36px;
  font-weight: 600;
  text-align: left;
  color: #fff;
  margin-bottom: 20px;
}

.blog-sidebar .wpo-contact-widget h2::before {
  left: 28px;
  background: url(../../images/Single-title-shape2.png) no-repeat right center;
}

.blog-sidebar .wpo-contact-widget p {
  color: #fff;
  font-size: 18px;
}

.blog-sidebar .wpo-contact-widget a {
  display: inline-block;
  padding: 10px 20px;
  border: 1px solid #fff;
  font-size: 18px;
  color: #fff;
  padding-right: 90px;
  position: relative;
  margin-top: 10px;
}

.blog-sidebar .wpo-contact-widget a::before {
  font-family: "themify";
  content: "\e628";
  font-size: 18px;
  position: absolute;
  right: 15px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

@media screen and (min-width: 992px) {
  .wpo-blog-pg-section.blog-pg-left-sidebar .blog-sidebar {
    padding-left: 0px;
    padding-right: 45px;
  }
}

@media screen and (min-width: 992px) {
  .wpo-blog-single-section.wpo-blog-single-left-sidebar-section .blog-sidebar {
    padding-left: 0px;
    padding-right: 45px;
  }
}

/**** pagination ****/
.pagination-wrapper {
  text-align: center;
  margin-top: 60px;
}

@media (max-width: 991px) {
  .pagination-wrapper {
    text-align: left;
    margin-top: 40px;
  }
}

.pagination-wrapper .pg-pagination {
  display: inline-block;
  overflow: hidden;
  list-style-type: none;
  text-align: center;
}

.pagination-wrapper .pg-pagination li {
  float: left;
  margin-right: 10px;
}

@media (max-width: 767px) {
  .pagination-wrapper .pg-pagination li {
    margin-right: 5px;
  }
}

.pagination-wrapper .pg-pagination li:last-child {
  margin-right: 0;
}

.pagination-wrapper .pg-pagination li a {
  background-color: transparent;
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  font-size: 1.06667rem;
  font-weight: 600;
  color: #1A1729;
  background: #f2f2f2;
  display: block;
}

@media (max-width: 991px) {
  .pagination-wrapper .pg-pagination li a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 15px;
    font-size: 1rem;
  }
}

.pagination-wrapper .pg-pagination .active a,
.pagination-wrapper .pg-pagination li a:hover {
  background: #4540E1;
  border-color: #4540E1;
  color: #fff;
}

.pagination-wrapper .pg-pagination .fi:before {
  font-size: 15px;
  font-size: 1rem;
}

.pagination-wrapper-left {
  text-align: left;
}

.pagination-wrapper-right {
  text-align: right;
}

@media screen and (min-width: 1200px) {
  .pagination-wrapper-right {
    padding-right: 50px;
  }
}

@media (max-width: 991px) {
  .pagination-wrapper-right {
    margin-top: 45px;
    text-align: left;
  }
}

/*--------------------------------------------------------------
2. header
--------------------------------------------------------------*/
/*-- 2.1 topbar --*/
.topbar {
  background: #4540E1;
  padding: 0 60px;
}

@media (max-width: 991px) {
  .topbar {
    text-align: center;
    padding: 0;
  }
}

@media (max-width: 767px) {
  .header-style-1 {
    text-align: center;
    padding: 20px 0;
  }
}

.topbar {
  font-size: 16px;
  color: #e5e3e3;
  margin: 0;
}

.topbar ul {
  overflow: hidden;
  list-style: none;
  float: right;
  margin-bottom: 0;
}

@media (max-width: 991px) {
  .topbar ul {
    float: none;
    display: inline-block;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

.topbar ul li {
  font-size: 16px;
  float: left;
  padding: 15px 10px;
  color: #fff;
  line-height: 1em;
}

@media (max-width: 991px) {
  .topbar ul li {
    padding: 15px 10px;
  }
}

.topbar .contact-info ul li a {
  color: #fff;
}

.topbar .contact-info ul li a:hover {
  color: #4540E1;
}

.topbar ul li:first-child {
  padding-left: 0;
}

.topbar .contact-intro ul {
  float: left;
  margin-bottom: 0;
}

@media (max-width: 991px) {
  .topbar .contact-intro ul {
    float: none;
  }
}

@media (max-width: 1200px) {
  .topbar ul li {
    font-size: 12px;
    padding: 15px 10px;
  }
}

@media (max-width: 991px) {
  .topbar ul li {
    font-size: 13px;
  }
  .topbar {
    padding: 10px 0;
  }
}

@media (max-width: 767px) {
  .topbar ul li {
    float: left;
    border: none;
    padding: 8px 6px;
    font-size: 13px;
  }
}

@media (max-width: 590px) {
  .topbar ul li {
    float: none;
  }
  .topbar .contact-info ul li {
    float: left;
  }
  .topbar ul {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}

.topbar .theme-btn-s2 {
  padding: 15px 20px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: -3px;
  color: #fff !important;
}

.topbar ul .fi {
  position: relative;
  top: 2px;
  display: inline-block;
  padding-right: 6px;
}

.topbar ul .fi:before {
  font-size: 15px;
  font-size: 1rem;
  color: #fff;
}

/* 2.2 navigation */
.wpo-site-header {
  /* navigation open and close btn hide for width screen */
  /* style for navigation less than 992px */
  /*navbar collaps less then 992px*/
  /*** cart-search-contact ***/
  background-color: transparent;
}

.wpo-site-header .navigation {
  background-color: #fff;
  margin-bottom: 0;
  padding: 0;
  border: 0;
  border-radius: 0;
}

.sticky-header {
  width: 100%;
  position: fixed;
  left: 0;
  top: -400px;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 1s;
  transition: all 1s;
  -webkit-box-shadow: 0 -5px 15px #086ad84a;
  box-shadow: 0 -5px 15px #086ad84a;
}

.sticky-on {
  opacity: 1;
  top: 0;
  visibility: visible;
}

.wpo-site-header .navigation > .container {
  position: relative;
}

@media (min-width: 991px) {
  .wpo-site-header {
    padding: 0 50px;
  }
}

.wpo-site-header .row {
  width: 100%;
}

@media (max-width: 991px) {
  .wpo-site-header .navigation {
    padding: 20px 0;
  }
}

.wpo-site-header #navbar {
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.wpo-site-header #navbar > ul li a:hover,
.wpo-site-header #navbar > ul li a:focus {
  text-decoration: none;
  color: #4540E1;
}

@media screen and (min-width: 992px) {
  .wpo-site-header #navbar {
    /*** hover effect ***/
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .wpo-site-header #navbar li {
    position: relative;
  }
  .wpo-site-header #navbar > ul > li > a:before {
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 0;
    height: 4px;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
    background: #F2BC00;
    content: "";
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all .3s;
    transition: all .3s;
    border-radius: 3px;
  }
  .wpo-site-header #navbar > ul > li > a:hover:before,
  .wpo-site-header #navbar > ul > li > a.active:before {
    opacity: 1;
    visibility: visible;
    width: 100%;
  }
  .wpo-site-header #navbar > ul > li > a.active {
    color: #625F71;
    font-weight: 700;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu > .menu-item-has-children > a {
    position: relative;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu > .menu-item-has-children > a:before {
    font-family: "themify";
    content: "\e649";
    font-size: 11px;
    font-size: 0.73333rem;
    position: absolute;
    right: 15px;
    top: 58%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
  .wpo-site-header #navbar > ul > li:hover > .sub-menu {
    top: 100%;
    visibility: visible;
    opacity: 1;
  }
  .wpo-site-header #navbar .sub-menu > li:hover > .sub-menu {
    left: 100%;
    visibility: visible;
    opacity: 1;
  }
}

.wpo-site-header .container-fluid {
  padding: 0;
  -ms-flex-pack: distribute;
      justify-content: space-around;
}

@media (max-width: 991px) {
  .wpo-site-header {
    /* class for show hide navigation */
  }
  .wpo-site-header .container {
    width: 100%;
    max-width: 100%;
  }
  .wpo-site-header .header-right {
    right: -10px;
  }
  .wpo-site-header .navbar-header button span {
    background-color: #fff;
    width: 20px;
    display: block;
    height: 2px;
    margin-bottom: 5px;
  }
  .wpo-site-header .navbar-header button span:last-child {
    margin: 0;
  }
}

.wpo-site-header #navbar ul {
  list-style: none;
}

@media (max-width: 991px) and (max-width: 991px) {
  .wpo-site-header #navbar {
    top: 0;
    visibility: visible;
    opacity: 1;
  }
}

@media (max-width: 991px) {
  .wpo-site-header #navbar > ul {
    z-index: 101;
  }
  .wpo-site-header #navbar ul a {
    color: #000;
  }
  .wpo-site-header #navbar ul a:hover,
  .wpo-site-header #navbar ul li.current a {
    color: #F2BC00;
  }
  .wpo-site-header #navbar .close-navbar .ti-close {
    position: relative;
    top: 1px;
  }
  .wpo-site-header .navbar-toggler .first-angle,
  .wpo-site-header .navbar-toggler .last-angle {
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
  .wpo-site-header .x-close .middle-angle {
    opacity: 0;
  }
  .wpo-site-header .x-close .first-angle {
    position: absolute;
    -webkit-transform: rotate(-44deg);
    top: 16px;
    left: 10px;
  }
  .wpo-site-header .x-close .last-angle {
    -webkit-transform: rotate(44deg);
    position: absolute;
    top: 16px;
    right: 10px;
  }
}

@media (max-width: 767px) {
  .wpo-site-header .navbar-header .navbar-brand {
    font-size: 24px;
  }
  .wpo-site-header #navbar .navbar-nav {
    margin: 0;
  }
}

@media (max-width: 991px) {
  .wpo-site-header .navbar-collapse.collapse {
    display: none;
  }
  .wpo-site-header .navbar-collapse.collapse.in {
    display: block;
  }
  .wpo-site-header .navbar-header .collapse,
  .wpo-site-header .navbar-toggle {
    display: block;
  }
  .wpo-site-header .navbar-header {
    float: none;
    position: relative;
    z-index: 99;
    text-align: center;
  }
  .wpo-site-header .navbar-right {
    float: none;
  }
  .wpo-site-header .navbar-nav {
    float: none;
  }
  .wpo-site-header .navbar-nav > li {
    float: none;
  }
}

.wpo-site-header .header-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  z-index: 991;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-site-header .header-right button {
  padding: 0;
  border: 0;
  outline: 0;
}

.wpo-site-header .header-right > div {
  float: left;
  position: relative;
}

.wpo-site-header .header-right .close-form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (max-width: 991px) {
  .wpo-site-header .header-right .close-form {
    display: none;
  }
}

.wpo-site-header .header-right .close-form .login {
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  color: #1A1729;
  margin-right: 20px;
}

@media (max-width: 1399px) {
  .wpo-site-header .header-right .close-form .login {
    font-size: 15px;
  }
}

.wpo-site-header .header-right .close-form .login:hover {
  color: #F2BC00;
}

@media (max-width: 1400px) {
  .wpo-site-header .header-right .close-form .theme-btn, .wpo-site-header .header-right .close-form .view-cart-btn {
    padding: 15px 15px;
    font-size: 15px;
  }
}

@media (max-width: 1110px) {
  .wpo-site-header .header-right .close-form .theme-btn, .wpo-site-header .header-right .close-form .view-cart-btn {
    font-size: 13px;
    padding: 6px 10px;
  }
}

@media (max-width: 991px) {
  .wpo-site-header .header-right .close-form .theme-btn, .wpo-site-header .header-right .close-form .view-cart-btn {
    padding: 0;
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 991px) {
  .wpo-site-header .header-right .close-form .theme-btn .text, .wpo-site-header .header-right .close-form .view-cart-btn .text {
    display: none;
  }
}

@media (min-width: 991px) {
  .wpo-site-header .header-right .close-form .theme-btn .mobile, .wpo-site-header .header-right .close-form .view-cart-btn .mobile {
    display: none;
  }
}

.wpo-site-header .header-right .close-form .theme-btn .mobile .fi, .wpo-site-header .header-right .close-form .view-cart-btn .mobile .fi {
  font-size: 20px;
}

@media (max-width: 450px) {
  .wpo-site-header .header-right > div {
    margin-right: 10px;
  }
  .header-right {
    right: -10px;
  }
}

.wpo-site-header .header-right > div:last-child {
  margin: 0 0 0 10px;
  position: relative;
}

@media (max-width: 1199px) {
  .wpo-site-header .header-right > div:last-child {
    margin: 0;
  }
}

.wpo-site-header .header-right .header-search-form-wrapper {
  position: relative;
}

.wpo-site-header .header-right .header-search-form-wrapper .fi:before {
  font-size: 18px;
}

@media (max-width: 450px) {
  .wpo-site-header .header-right .cart-toggle-btn {
    display: none;
  }
}

@media (max-width: 450px) {
  .wpo-site-header .header-right .search-toggle-btn {
    width: 18px;
    height: 30px;
    text-align: center;
  }
}

.wpo-site-header .header-right .search-toggle-btn .fi {
  color: #625F71;
  margin-right: 20px;
}

@media (max-width: 450px) {
  .wpo-site-header .header-right .search-toggle-btn .fi {
    margin-right: 0;
    text-align: center;
  }
}

@media (max-width: 991px) {
  .wpo-site-header .header-right .search-toggle-btn .fi:before {
    font-size: 16px;
  }
}

.wpo-site-header .header-right .search-toggle-btn .ti-close {
  display: block;
  line-height: 20px;
  position: relative;
  top: 0;
}

/*** cart-search-contact ***/
.cart-search-contact button {
  background: transparent;
  padding: 0;
  border: 0;
  outline: 0;
}

.cart-search-contact .fi {
  font-size: 30px;
  font-size: 2rem;
  color: #1A1729;
}

.cart-search-contact > div {
  float: left;
  position: relative;
}

.cart-search-contact > div + div {
  margin-left: 12px;
}

.cart-search-contact .header-search-form {
  position: absolute;
  width: 350px;
  right: 0;
  top: 75px;
  -webkit-transform: scale(0);
          transform: scale(0);
  opacity: 0;
  visibility: hidden;
  background: #fff;
  padding: 30px;
  border-radius: 6px;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  -webkit-box-shadow: -2px 18px 40px -9px #999999;
          box-shadow: -2px 18px 40px -9px #999999;
}

@media (max-width: 991px) {
  .cart-search-contact .header-search-form {
    top: 65px;
  }
}

@media (max-width: 767px) {
  .cart-search-contact .header-search-form {
    right: 15px;
  }
}

@media (max-width: 575px) {
  .cart-search-contact .header-search-form {
    width: 260px;
    padding: 15px;
  }
}

.cart-search-contact .header-search-form-wrapper .fi:before {
  font-size: 18px;
  font-size: 1.2rem;
}

.cart-search-contact form div {
  position: relative;
}

.cart-search-contact form div button {
  position: absolute;
  right: 15px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.cart-search-contact form div button .fi:before {
  font-size: 18px !important;
}

.cart-search-contact input {
  width: 100%;
  height: 50px;
  padding: 6px 20px;
  border: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 1px solid #D9D9D9;
}

.cart-search-contact input:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #4540E1;
}

.wpo-site-header .header-right .header-search-content-toggle {
  opacity: 1;
  visibility: visible;
  right: 0;
  -webkit-transform: scale(1);
          transform: scale(1);
}

.mini-cart .cart-count {
  background: #4540E1;
  width: 19px;
  height: 19px;
  font-size: 10px;
  font-size: 0.53333rem;
  line-height: 17px;
  color: white;
  position: absolute;
  top: -2px;
  right: -5px;
  border-radius: 50%;
  font-weight: 600;
}

.mini-cart-content {
  background: #fff;
  width: 320px;
  height: 100%;
  z-index: 100;
  position: fixed;
  right: -320px;
  top: 0;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  -webkit-box-shadow: -2px 18px 40px -9px #999999;
          box-shadow: -2px 18px 40px -9px #999999;
}

@media (max-width: 575px) {
  .mini-cart-content {
    width: 290px;
  }
}

.mini-cart-content .mini-cart-close {
  display: block;
  width: 40px;
  height: 40px;
  background-color: #333333;
  color: white;
  font-size: 0px;
  text-align: center;
  overflow: hidden;
  position: relative;
  float: right;
  line-height: 54px;
  border-radius: 3px;
  right: 10px;
  top: 20px;
}

.mini-cart-content .mini-cart-close i {
  font-size: 15px;
}

.mini-cart-content p {
  font-size: 15px;
  font-size: 1rem;
  font-weight: 500;
  color: #1A1729;
  margin: 0;
  text-transform: uppercase;
}

.mini-cart-content .mini-cart-items {
  padding: 100px 20px 25px;
}

.mini-cart-content .mini-cart-item {
  padding-top: 15px;
  margin-top: 15px;
  border-top: 1px solid #efefef;
}

.mini-cart-content .mini-cart-item:first-child {
  padding-top: 0;
  margin-top: 0;
  border-top: 0;
}

.mini-cart-content .mini-cart-item-image {
  width: 50px;
  height: 50px;
  border: 2px solid #f3f3f3;
  float: left;
  margin-right: 15px;
}

.mini-cart-content .mini-cart-item-image a,
.mini-cart-content .mini-cart-item-image img {
  display: block;
  width: 46px;
  height: 46px;
}

.mini-cart-content .mini-cart-item-des {
  position: relative;
  overflow: hidden;
}

.mini-cart-content .mini-cart-item-des a {
  font-size: 14px;
  font-size: 0.93333rem;
  font-weight: 600;
  text-align: left;
  color: #1A1729;
}

.mini-cart-content .mini-cart-item-des a:hover {
  color: #4540E1;
}

.mini-cart-content .mini-cart-item-des .mini-cart-item-price {
  font-size: 13px;
  font-size: 0.86667rem;
  color: #888;
  display: block;
  margin-top: 3px;
}

.mini-cart-content .mini-cart-item-des .mini-cart-item-quantity {
  font-size: 12px;
  font-size: 0.8rem;
  color: #444;
  display: block;
  position: absolute;
  right: 0;
  top: 6px;
}

.mini-cart-content .visible-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;
  opacity: .02;
  z-index: -1;
}

.mini-cart-content .mini-cart-action {
  padding: 20px 0 30px;
  text-align: center;
  position: absolute;
  left: 50%;
  bottom: 0;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}

.mini-cart-content .mini-cart-action .theme-btn-s2 {
  float: right;
}

.mini-cart-content .mini-cart-action .mini-btn {
  width: 240px;
}

.mini-cart-content .mini-cart-action .mini-checkout-price {
  font-size: 16px;
  font-size: 1.06667rem;
  font-weight: 600;
  color: #1A1729;
  display: block;
  margin-bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.mini-cart-content .mini-cart-action .mini-checkout-price span {
  color: #c79652;
  font-size: 20px;
}

.mini-cart-content-toggle,
.mini-wislist-content-toggle {
  opacity: 1;
  visibility: visible;
  right: 0;
}

@media (max-width: 991px) {
  .page-wrapper {
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
  .body-overlay:before {
    content: "";
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
}

.header-search-area {
  background: #fff;
  width: 100%;
  padding: 100px 0;
  text-align: center;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
}

.header-search-area h3 {
  font-size: 45px;
  margin: 0 0 1em;
}

.header-search-area form {
  position: relative;
}

.header-search-area input {
  height: 50px;
  font-size: 18px;
  font-size: 1.2rem;
  border-radius: 0;
  border: 0;
  border-bottom: 1px solid #d0d0d0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.header-search-area input:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
  border-color: #4540E1;
}

.header-search-area form button {
  background: transparent;
  border: 0;
  font-size: 20px;
  font-size: 1.33333rem;
  position: absolute;
  right: 0;
  bottom: 10px;
}

.header-search-area form button:focus {
  border: none;
  outline: none;
}

.header-search-area .close-form {
  position: absolute;
  right: 35px;
  top: 35px;
  bottom: auto;
  padding-bottom: 5px;
  border-bottom: 1px solid;
}

.header-search-area .close-form button {
  background: transparent;
  border: 0;
  outline: 0;
  font-size: 13px;
  font-size: 0.86667rem;
  text-transform: uppercase;
  color: #000;
  letter-spacing: 2px;
  z-index: 99;
}

.header-search-area .close-form button:focus {
  border: 0;
  outline: 0;
}

.wpo-site-header {
  /* navigation open and close btn hide for width screen */
  /* style for navigation less than 992px */
  /*navbar collaps less then 992px*/
}

.wpo-site-header .navigation {
  background-color: transparent;
  margin-bottom: 0;
  padding: 0;
  border: 0;
  border-radius: 0;
}

.wpo-site-header .navigation > .container {
  position: relative;
}

@media (max-width: 991px) {
  .wpo-site-header .navigation {
    padding: 20px 0;
  }
}

.wpo-site-header #navbar {
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}

.wpo-site-header #navbar > ul li a:hover,
.wpo-site-header #navbar > ul li a:focus {
  text-decoration: none;
  color: #F2BC00;
}

@media screen and (min-width: 992px) {
  .wpo-site-header #navbar {
    /*** hover effect ***/
  }
  .wpo-site-header #navbar li {
    position: relative;
  }
  .wpo-site-header #navbar > ul > li > a {
    font-size: 18px;
    padding: 35px 20px;
    display: block;
    color: #625F71;
    font-weight: 400;
    position: relative;
    font-family: "Urbanist";
    text-transform: uppercase;
  }
}

@media screen and (min-width: 992px) and (max-width: 1870px) {
  .wpo-site-header #navbar > ul > li > a {
    padding: 35px 18px;
  }
}

@media screen and (min-width: 992px) {
  .wpo-site-header #navbar > ul .sub-menu {
    background-color: #fff;
    -webkit-box-shadow: 0px 2px 20px 0px rgba(62, 65, 159, 0.09);
            box-shadow: 0px 2px 20px 0px rgba(62, 65, 159, 0.09);
    width: 235px;
    position: absolute;
    padding: 20px 0;
    left: 0;
    top: 110%;
    z-index: 10;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
  .wpo-site-header #navbar > ul > li .sub-menu a {
    font-size: 17px;
    display: block;
    padding: 10px 15px;
    color: #1A1729;
    font-family: "Urbanist";
    position: relative;
    overflow: hidden;
    font-weight: 500;
    text-transform: capitalize;
  }
  .wpo-site-header #navbar > ul > li .sub-menu a:after {
    position: absolute;
    left: 15px;
    bottom: 4px;
    width: 0px;
    height: 2px;
    content: "";
    background: #F2BC00;
    -webkit-transition: all .3s;
    transition: all .3s;
    opacity: 0;
    visibility: hidden;
  }
  .wpo-site-header #navbar > ul > li .sub-menu a:hover:after, .wpo-site-header #navbar > ul > li .sub-menu a.active:after {
    width: 40px;
    opacity: 1;
    visibility: visible;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu .sub-menu {
    left: 110%;
    top: 0;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu > .menu-item-has-children > a {
    position: relative;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu > .menu-item-has-children > a:before {
    font-family: "themify";
    content: "\e649";
    font-size: 11px;
    font-size: 0.73333rem;
    position: absolute;
    right: 15px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
  .wpo-site-header #navbar > ul > li:hover > .sub-menu {
    top: 100%;
    visibility: visible;
    opacity: 1;
  }
  .wpo-site-header #navbar .sub-menu > li:hover > .sub-menu {
    left: 100%;
    visibility: visible;
    opacity: 1;
  }
}

@media (max-width: 991px) {
  .wpo-site-header #navbar > ul > li a {
    display: block;
    font-size: 15px;
    font-size: 1rem;
    color: #fff;
    font-weight: 600;
  }
  .wpo-site-header #navbar > ul > li a:hover, .wpo-site-header #navbar > ul > li a.active {
    color: #fff;
  }
  .wpo-site-header #navbar > ul .sub-menu > li:last-child {
    border-bottom: 0;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu a {
    padding: 8px 15px 8px 45px;
  }
  .wpo-site-header #navbar > ul > li > .sub-menu .sub-menu a {
    padding: 8px 15px 8px 65px;
  }
  .wpo-site-header #navbar > ul .menu-item-has-children > a {
    position: relative;
    color: #ddd;
  }
  .wpo-site-header #navbar > ul .menu-item-has-children > a:hover, .wpo-site-header #navbar > ul .menu-item-has-children > a.active {
    color: #fff;
  }
  .wpo-site-header #navbar > ul .menu-item-has-children > a:before {
    font-family: "themify";
    content: "\e649";
    font-size: 11px;
    font-size: 0.73333rem;
    position: absolute;
    right: 30px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
  .wpo-site-header #navbar > ul .menu-item-has-children > a.rotate:before {
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
    top: 29%;
  }
  .wpo-site-header #navbar > ul .menu-item-has-children > a.rotate {
    color: #fff;
  }
}

@media screen and (min-width: 992px) {
  .wpo-site-header .navbar-header .open-btn {
    display: none;
  }
  .wpo-site-header #navbar .close-navbar {
    display: none;
  }
}

.wpo-site-header .menu-close {
  display: none;
}

@media (max-width: 991px) {
  .wpo-site-header {
    /* class for show hide navigation */
  }
  .wpo-site-header .container {
    width: 100%;
  }
  .wpo-site-header .mobail-menu button {
    background-color: #F2BC00;
    width: 40px;
    height: 35px;
    border: 0;
    padding: 5px 10px;
    outline: 0;
    position: relative;
    z-index: 20;
  }
  .wpo-site-header .mobail-menu button:focus {
    outline: none;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
  .wpo-site-header .mobail-menu button span {
    background-color: #fff;
    width: 20px;
    display: block;
    height: 2px;
    margin-bottom: 5px;
  }
  .wpo-site-header .mobail-menu button span:last-child {
    margin: 0;
  }
  .wpo-site-header #navbar {
    background: #1A1729;
    display: block !important;
    width: 300px;
    height: 100%;
    margin: 0;
    padding: 0;
    border: 0;
    position: fixed;
    left: -320px;
    top: 0px;
    z-index: 999999;
    opacity: 0;
    visibility: hidden;
  }
  .wpo-site-header #navbar .navbar-nav {
    display: block;
  }
  .wpo-site-header #navbar > ul {
    position: relative;
    z-index: 101;
  }
  .wpo-site-header #navbar ul a {
    color: #000;
  }
  .wpo-site-header #navbar ul a:hover,
  .wpo-site-header #navbar ul li.current a {
    color: #4540E1;
  }
  .wpo-site-header #navbar .navbar-nav {
    height: 100%;
    overflow: auto;
    padding-bottom: 60px;
  }
  .wpo-site-header #navbar .close-navbar {
    background-color: #fff;
    width: 40px;
    height: 40px;
    color: #000;
    border: 0;
    outline: none;
    position: absolute;
    left: -41px;
    top: 90px;
    z-index: 20;
  }
  .wpo-site-header #navbar .close-navbar .ti-close {
    position: relative;
    top: 1px;
  }
  .wpo-site-header .menu-close {
    display: block;
    z-index: 99;
    background: none;
    text-align: center;
    color: #fff;
    border: 0;
    text-align: center;
    margin-left: auto;
    margin-top: 30px;
    margin-right: 30px;
    margin-bottom: 30px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border: 1px solid #fff;
  }
  .wpo-site-header .slideInn {
    left: 0 !important;
    opacity: 1 !important;
    visibility: visible !important;
  }
  .wpo-site-header .navbar-toggler .first-angle,
  .wpo-site-header .navbar-toggler .last-angle {
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
  }
  .wpo-site-header .x-close .middle-angle {
    opacity: 0;
  }
  .wpo-site-header .x-close .first-angle {
    position: absolute;
    -webkit-transform: rotate(-44deg);
    top: 16px;
    left: 10px;
  }
  .wpo-site-header .x-close .last-angle {
    -webkit-transform: rotate(44deg);
    position: absolute;
    top: 16px;
    right: 10px;
  }
}

@media (max-width: 767px) {
  .wpo-site-header .navbar-header .navbar-brand {
    font-size: 24px;
  }
  .wpo-site-header #navbar .navbar-nav {
    margin: 0;
  }
}

.wpo-site-header .navbar-header .navbar-brand img {
  max-width: 170px;
}

@media (max-width: 991px) {
  .wpo-site-header .navbar-collapse.collapse {
    display: none;
  }
  .wpo-site-header .navbar-collapse.collapse.in {
    display: block;
  }
  .wpo-site-header .navbar-header .collapse,
  .wpo-site-header .navbar-toggle {
    display: block;
  }
  .wpo-site-header .navbar-header {
    float: none;
  }
  .wpo-site-header .navbar-right {
    float: none;
  }
  .wpo-site-header .navbar-nav {
    float: none;
  }
  .wpo-site-header .navbar-nav > li {
    float: none;
  }
}

@media (max-width: 440px) {
  .wpo-site-header .navbar-header .navbar-brand img {
    max-width: 170px;
  }
}

@media (max-width: 370px) {
  .wpo-site-header .navbar-header .navbar-brand img {
    max-width: 140px;
  }
}

@media (max-width: 1700px) {
  .wpo-site-header {
    padding: 0 30px;
  }
}

@media (max-width: 1500px) {
  .wpo-site-header #navbar > ul > li > a {
    padding: 30px 15px;
  }
  .wpo-site-header {
    padding: 0 10px;
  }
}

@media (max-width: 1400px) {
  .wpo-site-header #navbar > ul > li > a {
    padding: 30px 10px;
    font-size: 18px;
  }
}

@media (max-width: 1200px) {
  .wpo-site-header #navbar > ul > li > a {
    padding: 30px 6px;
    font-size: 15px;
  }
}

@media (max-width: 991px) {
  .wpo-site-header #navbar > ul > li > a {
    font-size: 15px;
  }
}

@media (max-width: 767px) {
  .header-right .theme-btn, .header-right .view-cart-btn {
    padding: 20px 12px;
  }
}

@media (max-width: 991px) {
  .wpo-site-header #navbar > ul > li > a {
    padding: 15px 30px;
  }
}

@media (max-width: 991px) {
  .wpo-site-header,
  .wpo-header-style-2 {
    z-index: 99999;
  }
}

.wpo-site-header .navigation.sticky-header {
  padding: 0 130px;
  background: #fff;
}

@media (max-width: 1700px) {
  .wpo-site-header .navigation.sticky-header {
    padding: 0 50px;
  }
}

@media (max-width: 1400px) {
  .wpo-site-header .navigation.sticky-header {
    padding: 0 20px;
  }
}

@media (max-width: 991px) {
  .wpo-site-header .navigation.sticky-header {
    padding: 20px;
  }
}

@media (max-width: 575px) {
  .wpo-site-header .navigation.sticky-header {
    padding: 20px 10px;
  }
}

.wpo-header-style-2,
.wpo-header-style-3 {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 99;
  background: transparent;
}

.wpo-header-style-2 .navigation,
.wpo-header-style-3 .navigation {
  background: transparent;
  border-radius: 5px;
}

.wpo-header-style-2 .navigation.sticky-on,
.wpo-header-style-3 .navigation.sticky-on {
  background: #fff;
}

.wpo-header-style-2 .navigation .navbar-header .navbar-brand,
.wpo-header-style-3 .navigation .navbar-header .navbar-brand {
  margin-left: 5px;
}

@media (max-width: 991px) {
  .wpo-header-style-2 .navigation .navbar-header .navbar-brand,
  .wpo-header-style-3 .navigation .navbar-header .navbar-brand {
    margin-left: 0;
  }
}

@media screen and (min-width: 992px) {
  .wpo-header-style-2 .wpo-site-header #navbar > ul .sub-menu,
  .wpo-header-style-3 .wpo-site-header #navbar > ul .sub-menu {
    background: #fff;
  }
  .wpo-header-style-2 .wpo-site-header #navbar > ul .sub-menu a,
  .wpo-header-style-3 .wpo-site-header #navbar > ul .sub-menu a {
    color: #1A1729;
  }
  .wpo-header-style-2 #navbar > ul > li > a,
  .wpo-header-style-3 #navbar > ul > li > a {
    color: #1A1729;
  }
}

.wpo-header-style-2 .header-right .cart-toggle-btn,
.wpo-header-style-2 .header-right .search-toggle-btn,
.wpo-header-style-3 .header-right .cart-toggle-btn,
.wpo-header-style-3 .header-right .search-toggle-btn {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.wpo-header-style-2 .header-right .cart-toggle-btn .fi,
.wpo-header-style-2 .header-right .search-toggle-btn .fi,
.wpo-header-style-3 .header-right .cart-toggle-btn .fi,
.wpo-header-style-3 .header-right .search-toggle-btn .fi {
  color: #1A1729;
}

.wpo-header-style-3 .theme-btn, .wpo-header-style-3 .view-cart-btn {
  background: none;
  border: 1px solid #1A1729;
}

.wpo-header-style-3 .theme-btn:hover, .wpo-header-style-3 .view-cart-btn:hover {
  background: #1A1729;
  color: #fff;
}

.wpo-header-style-3 .theme-btn:after, .wpo-header-style-3 .view-cart-btn:after {
  display: none;
}

@media (min-width: 992px) {
  .wpo-header-style-3 #navbar > ul > li > a:before {
    display: none;
  }
}

.wpo-header-style-3 #navbar > ul > li > a:hover {
  color: #1A1729;
}

.wpo-header-style-3 #navbar > ul > li .sub-menu a:after {
  background-color: #1A1729;
}

/*--------------------------------------------------------------
3. content
--------------------------------------------------------------*/
/*3.1 wpo-hero-slider*/
.wpo-hero-slider,
.static-hero,
.static-hero-s2,
.static-hero-s3,
.static-hero-s4,
.static-hero-s5 {
  height: 920px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  z-index: 0;
}

@media (max-width: 991px) {
  .wpo-hero-slider,
  .static-hero,
  .static-hero-s2,
  .static-hero-s3,
  .static-hero-s4,
  .static-hero-s5 {
    height: 600px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider,
  .static-hero,
  .static-hero-s2,
  .static-hero-s3,
  .static-hero-s4,
  .static-hero-s5 {
    height: 500px;
  }
}

.wpo-hero-slider .swiper-slide,
.static-hero .swiper-slide,
.static-hero-s2 .swiper-slide,
.static-hero-s3 .swiper-slide,
.static-hero-s4 .swiper-slide,
.static-hero-s5 .swiper-slide {
  overflow: hidden;
}

.wpo-hero-slider .swiper-container,
.wpo-hero-slider .hero-container,
.static-hero .swiper-container,
.static-hero .hero-container,
.static-hero-s2 .swiper-container,
.static-hero-s2 .hero-container,
.static-hero-s3 .swiper-container,
.static-hero-s3 .hero-container,
.static-hero-s4 .swiper-container,
.static-hero-s4 .hero-container,
.static-hero-s5 .swiper-container,
.static-hero-s5 .hero-container {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.wpo-hero-slider .slide-inner,
.wpo-hero-slider .hero-inner,
.static-hero .slide-inner,
.static-hero .hero-inner,
.static-hero-s2 .slide-inner,
.static-hero-s2 .hero-inner,
.static-hero-s3 .slide-inner,
.static-hero-s3 .hero-inner,
.static-hero-s4 .slide-inner,
.static-hero-s4 .hero-inner,
.static-hero-s5 .slide-inner,
.static-hero-s5 .hero-inner {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  background-size: cover;
  background-position: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  text-align: left;
}

.wpo-hero-slider .slide-inner:before,
.wpo-hero-slider .hero-inner:before,
.static-hero .slide-inner:before,
.static-hero .hero-inner:before,
.static-hero-s2 .slide-inner:before,
.static-hero-s2 .hero-inner:before,
.static-hero-s3 .slide-inner:before,
.static-hero-s3 .hero-inner:before,
.static-hero-s4 .slide-inner:before,
.static-hero-s4 .hero-inner:before,
.static-hero-s5 .slide-inner:before,
.static-hero-s5 .hero-inner:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(288.29deg, #080A14 38.57%, #10121E 61.69%, #282A34 73.68%, #404354 89.54%, #343643 98.19%);
  opacity: 0.5;
  content: "";
}

.wpo-hero-slider .slide-inner .slide-content,
.wpo-hero-slider .hero-inner .slide-content,
.static-hero .slide-inner .slide-content,
.static-hero .hero-inner .slide-content,
.static-hero-s2 .slide-inner .slide-content,
.static-hero-s2 .hero-inner .slide-content,
.static-hero-s3 .slide-inner .slide-content,
.static-hero-s3 .hero-inner .slide-content,
.static-hero-s4 .slide-inner .slide-content,
.static-hero-s4 .hero-inner .slide-content,
.static-hero-s5 .slide-inner .slide-content,
.static-hero-s5 .hero-inner .slide-content {
  text-align: center;
  padding-bottom: 50px;
}

@media (max-width: 1399px) {
  .wpo-hero-slider .slide-inner .slide-content,
  .wpo-hero-slider .hero-inner .slide-content,
  .static-hero .slide-inner .slide-content,
  .static-hero .hero-inner .slide-content,
  .static-hero-s2 .slide-inner .slide-content,
  .static-hero-s2 .hero-inner .slide-content,
  .static-hero-s3 .slide-inner .slide-content,
  .static-hero-s3 .hero-inner .slide-content,
  .static-hero-s4 .slide-inner .slide-content,
  .static-hero-s4 .hero-inner .slide-content,
  .static-hero-s5 .slide-inner .slide-content,
  .static-hero-s5 .hero-inner .slide-content {
    padding-bottom: 100px;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider .slide-inner .slide-content,
  .wpo-hero-slider .hero-inner .slide-content,
  .static-hero .slide-inner .slide-content,
  .static-hero .hero-inner .slide-content,
  .static-hero-s2 .slide-inner .slide-content,
  .static-hero-s2 .hero-inner .slide-content,
  .static-hero-s3 .slide-inner .slide-content,
  .static-hero-s3 .hero-inner .slide-content,
  .static-hero-s4 .slide-inner .slide-content,
  .static-hero-s4 .hero-inner .slide-content,
  .static-hero-s5 .slide-inner .slide-content,
  .static-hero-s5 .hero-inner .slide-content {
    padding-bottom: 0px;
  }
}

.wpo-hero-slider .swiper-button-prev,
.wpo-hero-slider .swiper-button-next,
.static-hero .swiper-button-prev,
.static-hero .swiper-button-next,
.static-hero-s2 .swiper-button-prev,
.static-hero-s2 .swiper-button-next,
.static-hero-s3 .swiper-button-prev,
.static-hero-s3 .swiper-button-next,
.static-hero-s4 .swiper-button-prev,
.static-hero-s4 .swiper-button-next,
.static-hero-s5 .swiper-button-prev,
.static-hero-s5 .swiper-button-next {
  background: rgba(255, 255, 255, 0.2);
  width: 60px;
  height: 60px;
  line-height: 60px;
  border: 2px solid #fff;
  border-radius: 50%;
  opacity: 0.7;
  text-align: center;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  display: none;
}

.wpo-hero-slider .swiper-button-prev:hover,
.wpo-hero-slider .swiper-button-next:hover,
.static-hero .swiper-button-prev:hover,
.static-hero .swiper-button-next:hover,
.static-hero-s2 .swiper-button-prev:hover,
.static-hero-s2 .swiper-button-next:hover,
.static-hero-s3 .swiper-button-prev:hover,
.static-hero-s3 .swiper-button-next:hover,
.static-hero-s4 .swiper-button-prev:hover,
.static-hero-s4 .swiper-button-next:hover,
.static-hero-s5 .swiper-button-prev:hover,
.static-hero-s5 .swiper-button-next:hover {
  opacity: 0.9;
}

@media (max-width: 767px) {
  .wpo-hero-slider .swiper-button-prev,
  .wpo-hero-slider .swiper-button-next,
  .static-hero .swiper-button-prev,
  .static-hero .swiper-button-next,
  .static-hero-s2 .swiper-button-prev,
  .static-hero-s2 .swiper-button-next,
  .static-hero-s3 .swiper-button-prev,
  .static-hero-s3 .swiper-button-next,
  .static-hero-s4 .swiper-button-prev,
  .static-hero-s4 .swiper-button-next,
  .static-hero-s5 .swiper-button-prev,
  .static-hero-s5 .swiper-button-next {
    display: none;
  }
}

.wpo-hero-slider .swiper-button-prev,
.static-hero .swiper-button-prev,
.static-hero-s2 .swiper-button-prev,
.static-hero-s3 .swiper-button-prev,
.static-hero-s4 .swiper-button-prev,
.static-hero-s5 .swiper-button-prev {
  left: 25px;
}

.wpo-hero-slider .swiper-button-prev:before,
.static-hero .swiper-button-prev:before,
.static-hero-s2 .swiper-button-prev:before,
.static-hero-s3 .swiper-button-prev:before,
.static-hero-s4 .swiper-button-prev:before,
.static-hero-s5 .swiper-button-prev:before {
  font-family: "themify";
  content: "\e629";
  font-size: 20px;
  color: #fff;
}

.wpo-hero-slider .swiper-button-next,
.static-hero .swiper-button-next,
.static-hero-s2 .swiper-button-next,
.static-hero-s3 .swiper-button-next,
.static-hero-s4 .swiper-button-next,
.static-hero-s5 .swiper-button-next {
  right: 25px;
}

.wpo-hero-slider .swiper-button-next:before,
.static-hero .swiper-button-next:before,
.static-hero-s2 .swiper-button-next:before,
.static-hero-s3 .swiper-button-next:before,
.static-hero-s4 .swiper-button-next:before,
.static-hero-s5 .swiper-button-next:before {
  font-family: "themify";
  content: "\e628";
  font-size: 20px;
  color: #fff;
}

@media (max-width: 991px) {
  .wpo-hero-slider .swiper-button-prev,
  .wpo-hero-slider .swiper-button-next,
  .static-hero .swiper-button-prev,
  .static-hero .swiper-button-next,
  .static-hero-s2 .swiper-button-prev,
  .static-hero-s2 .swiper-button-next,
  .static-hero-s3 .swiper-button-prev,
  .static-hero-s3 .swiper-button-next,
  .static-hero-s4 .swiper-button-prev,
  .static-hero-s4 .swiper-button-next,
  .static-hero-s5 .swiper-button-prev,
  .static-hero-s5 .swiper-button-next {
    display: none;
  }
}

.wpo-hero-slider .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.static-hero .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.static-hero-s2 .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.static-hero-s3 .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.static-hero-s4 .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.static-hero-s5 .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 20px 0 !important;
}

.wpo-hero-slider .swiper-pagination,
.static-hero .swiper-pagination,
.static-hero-s2 .swiper-pagination,
.static-hero-s3 .swiper-pagination,
.static-hero-s4 .swiper-pagination,
.static-hero-s5 .swiper-pagination {
  right: 50px;
  top: 52%;
  left: auto;
  width: unset;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

@media (max-width: 991px) {
  .wpo-hero-slider .swiper-pagination,
  .static-hero .swiper-pagination,
  .static-hero-s2 .swiper-pagination,
  .static-hero-s3 .swiper-pagination,
  .static-hero-s4 .swiper-pagination,
  .static-hero-s5 .swiper-pagination {
    right: 20px;
  }
}

@media (max-width: 575px) {
  .wpo-hero-slider .swiper-pagination,
  .static-hero .swiper-pagination,
  .static-hero-s2 .swiper-pagination,
  .static-hero-s3 .swiper-pagination,
  .static-hero-s4 .swiper-pagination,
  .static-hero-s5 .swiper-pagination {
    right: 5px;
  }
}

.wpo-hero-slider .swiper-pagination .swiper-pagination-bullet,
.static-hero .swiper-pagination .swiper-pagination-bullet,
.static-hero-s2 .swiper-pagination .swiper-pagination-bullet,
.static-hero-s3 .swiper-pagination .swiper-pagination-bullet,
.static-hero-s4 .swiper-pagination .swiper-pagination-bullet,
.static-hero-s5 .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  text-align: center;
  line-height: 12px;
  font-size: 18px;
  color: #fff;
  opacity: 1;
  display: block;
  background: none;
}

.wpo-hero-slider .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active,
.static-hero .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active,
.static-hero-s2 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active,
.static-hero-s3 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active,
.static-hero-s4 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active,
.static-hero-s5 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  color: #fff;
  position: relative;
}

.wpo-hero-slider .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active:before,
.static-hero .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active:before,
.static-hero-s2 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active:before,
.static-hero-s3 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active:before,
.static-hero-s4 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active:before,
.static-hero-s5 .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active:before {
  position: absolute;
  left: -55px;
  top: 50%;
  width: 50px;
  height: 1px;
  content: "";
  background: #fff;
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-hero-slider .swiper-container-horizontal > .swiper-pagination-bullets,
.wpo-hero-slider .swiper-pagination-custom,
.wpo-hero-slider .swiper-pagination-fraction,
.static-hero .swiper-container-horizontal > .swiper-pagination-bullets,
.static-hero .swiper-pagination-custom,
.static-hero .swiper-pagination-fraction,
.static-hero-s2 .swiper-container-horizontal > .swiper-pagination-bullets,
.static-hero-s2 .swiper-pagination-custom,
.static-hero-s2 .swiper-pagination-fraction,
.static-hero-s3 .swiper-container-horizontal > .swiper-pagination-bullets,
.static-hero-s3 .swiper-pagination-custom,
.static-hero-s3 .swiper-pagination-fraction,
.static-hero-s4 .swiper-container-horizontal > .swiper-pagination-bullets,
.static-hero-s4 .swiper-pagination-custom,
.static-hero-s4 .swiper-pagination-fraction,
.static-hero-s5 .swiper-container-horizontal > .swiper-pagination-bullets,
.static-hero-s5 .swiper-pagination-custom,
.static-hero-s5 .swiper-pagination-fraction {
  bottom: 30px;
}

.wpo-hero-slider .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.static-hero .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.static-hero-s2 .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.static-hero-s3 .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.static-hero-s4 .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.static-hero-s5 .swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 8px;
}

.wpo-hero-slider .hero-shape,
.static-hero .hero-shape,
.static-hero-s2 .hero-shape,
.static-hero-s3 .hero-shape,
.static-hero-s4 .hero-shape,
.static-hero-s5 .hero-shape {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  z-index: 11;
}

.wpo-hero-slider .hero-shape svg,
.static-hero .hero-shape svg,
.static-hero-s2 .hero-shape svg,
.static-hero-s3 .hero-shape svg,
.static-hero-s4 .hero-shape svg,
.static-hero-s5 .hero-shape svg {
  width: 100%;
}

@media (max-width: 1199px) {
  .wpo-hero-slider,
  .static-hero,
  .static-hero-s2,
  .static-hero-s3,
  .static-hero-s4,
  .static-hero-s5 {
    height: 680px;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider,
  .static-hero,
  .static-hero-s2,
  .static-hero-s3,
  .static-hero-s4,
  .static-hero-s5 {
    height: 600px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider,
  .static-hero,
  .static-hero-s2,
  .static-hero-s3,
  .static-hero-s4,
  .static-hero-s5 {
    height: 450px;
  }
}

.wpo-hero-slider .gradient-overlay,
.static-hero .gradient-overlay,
.static-hero-s2 .gradient-overlay,
.static-hero-s3 .gradient-overlay,
.static-hero-s4 .gradient-overlay,
.static-hero-s5 .gradient-overlay {
  background: -webkit-gradient(linear, left top, right top, color-stop(45%, #1A1729), color-stop(65%, transparent));
  background: linear-gradient(left, #1A1729 45%, transparent 65%);
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 10;
  opacity: .6;
}

.wpo-hero-slider .gradient-overlay + .container,
.static-hero .gradient-overlay + .container,
.static-hero-s2 .gradient-overlay + .container,
.static-hero-s3 .gradient-overlay + .container,
.static-hero-s4 .gradient-overlay + .container,
.static-hero-s5 .gradient-overlay + .container {
  position: relative;
  z-index: 11;
}

.wpo-hero-slider .swiper-slide,
.static-hero .swiper-slide,
.static-hero-s2 .swiper-slide,
.static-hero-s3 .swiper-slide,
.static-hero-s4 .swiper-slide,
.static-hero-s5 .swiper-slide {
  position: relative;
  z-index: 11;
}

.wpo-hero-slider .wpo-hero-title-top span,
.static-hero .wpo-hero-title-top span,
.static-hero-s2 .wpo-hero-title-top span,
.static-hero-s3 .wpo-hero-title-top span,
.static-hero-s4 .wpo-hero-title-top span,
.static-hero-s5 .wpo-hero-title-top span {
  font-size: 22px;
  color: #e4e4e4;
  font-family: "Urbanist";
}

@media (max-width: 767px) {
  .wpo-hero-slider .wpo-hero-title-top span,
  .static-hero .wpo-hero-title-top span,
  .static-hero-s2 .wpo-hero-title-top span,
  .static-hero-s3 .wpo-hero-title-top span,
  .static-hero-s4 .wpo-hero-title-top span,
  .static-hero-s5 .wpo-hero-title-top span {
    font-size: 15px;
  }
}

.wpo-hero-slider .slide-title-sub h5,
.static-hero .slide-title-sub h5,
.static-hero-s2 .slide-title-sub h5,
.static-hero-s3 .slide-title-sub h5,
.static-hero-s4 .slide-title-sub h5,
.static-hero-s5 .slide-title-sub h5 {
  font-size: 25px;
  font-weight: 500;
  line-height: 30px;
  text-transform: capitalize;
  color: #F2BC00;
}

@media (max-width: 767px) {
  .wpo-hero-slider .slide-title-sub h5,
  .static-hero .slide-title-sub h5,
  .static-hero-s2 .slide-title-sub h5,
  .static-hero-s3 .slide-title-sub h5,
  .static-hero-s4 .slide-title-sub h5,
  .static-hero-s5 .slide-title-sub h5 {
    font-size: 20px;
    line-height: 25px;
  }
}

.wpo-hero-slider .slide-title,
.static-hero .slide-title,
.static-hero-s2 .slide-title,
.static-hero-s3 .slide-title,
.static-hero-s4 .slide-title,
.static-hero-s5 .slide-title {
  max-width: 1000px;
  margin: 0 auto;
}

@media (max-width: 1199px) {
  .wpo-hero-slider .slide-title,
  .static-hero .slide-title,
  .static-hero-s2 .slide-title,
  .static-hero-s3 .slide-title,
  .static-hero-s4 .slide-title,
  .static-hero-s5 .slide-title {
    max-width: 750px;
  }
}

.wpo-hero-slider .slide-title h2,
.static-hero .slide-title h2,
.static-hero-s2 .slide-title h2,
.static-hero-s3 .slide-title h2,
.static-hero-s4 .slide-title h2,
.static-hero-s5 .slide-title h2 {
  font-size: 95px;
  font-weight: 500;
  line-height: 95px;
  margin: 10px 0 15px;
  color: #fff;
  text-transform: capitalize;
}

@media (max-width: 1199px) {
  .wpo-hero-slider .slide-title h2,
  .static-hero .slide-title h2,
  .static-hero-s2 .slide-title h2,
  .static-hero-s3 .slide-title h2,
  .static-hero-s4 .slide-title h2,
  .static-hero-s5 .slide-title h2 {
    font-size: 70px;
    line-height: 75px;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider .slide-title h2,
  .static-hero .slide-title h2,
  .static-hero-s2 .slide-title h2,
  .static-hero-s3 .slide-title h2,
  .static-hero-s4 .slide-title h2,
  .static-hero-s5 .slide-title h2 {
    font-size: 40px;
    line-height: 55px;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider .slide-title h2,
  .static-hero .slide-title h2,
  .static-hero-s2 .slide-title h2,
  .static-hero-s3 .slide-title h2,
  .static-hero-s4 .slide-title h2,
  .static-hero-s5 .slide-title h2 {
    font-size: 30px;
    line-height: 36px;
  }
}

.wpo-hero-slider .slide-text,
.static-hero .slide-text,
.static-hero-s2 .slide-text,
.static-hero-s3 .slide-text,
.static-hero-s4 .slide-text,
.static-hero-s5 .slide-text {
  max-width: 680px;
  margin: 0 auto;
}

@media (max-width: 767px) {
  .wpo-hero-slider .slide-text,
  .static-hero .slide-text,
  .static-hero-s2 .slide-text,
  .static-hero-s3 .slide-text,
  .static-hero-s4 .slide-text,
  .static-hero-s5 .slide-text {
    max-width: 500px;
  }
}

.wpo-hero-slider .slide-text p,
.static-hero .slide-text p,
.static-hero-s2 .slide-text p,
.static-hero-s3 .slide-text p,
.static-hero-s4 .slide-text p,
.static-hero-s5 .slide-text p {
  font-size: 22px;
  color: #fff;
  line-height: 35px;
  max-width: 680px;
  margin-bottom: 40px;
  color: #e2e2e2;
}

@media (max-width: 991px) {
  .wpo-hero-slider .slide-text p,
  .static-hero .slide-text p,
  .static-hero-s2 .slide-text p,
  .static-hero-s3 .slide-text p,
  .static-hero-s4 .slide-text p,
  .static-hero-s5 .slide-text p {
    font-size: 18px;
    font-size: 1.2rem;
  }
}

@media (max-width: 767px) {
  .wpo-hero-slider .slide-text p,
  .static-hero .slide-text p,
  .static-hero-s2 .slide-text p,
  .static-hero-s3 .slide-text p,
  .static-hero-s4 .slide-text p,
  .static-hero-s5 .slide-text p {
    font-size: 16px;
    font-size: 1.06667rem;
    line-height: 22px;
    margin-bottom: 30px;
  }
}

@media (max-width: 991px) {
  .wpo-hero-slider .slide-btns,
  .static-hero .slide-btns,
  .static-hero-s2 .slide-btns,
  .static-hero-s3 .slide-btns,
  .static-hero-s4 .slide-btns,
  .static-hero-s5 .slide-btns {
    max-width: 400px;
    margin: 0 auto;
  }
}

.wpo-hero-slider .slide-btns .hero-video-btn,
.static-hero .slide-btns .hero-video-btn,
.static-hero-s2 .slide-btns .hero-video-btn,
.static-hero-s3 .slide-btns .hero-video-btn,
.static-hero-s4 .slide-btns .hero-video-btn,
.static-hero-s5 .slide-btns .hero-video-btn {
  margin-left: 40px;
}

@media (max-width: 767px) {
  .wpo-hero-slider .slide-btns .hero-video-btn,
  .static-hero .slide-btns .hero-video-btn,
  .static-hero-s2 .slide-btns .hero-video-btn,
  .static-hero-s3 .slide-btns .hero-video-btn,
  .static-hero-s4 .slide-btns .hero-video-btn,
  .static-hero-s5 .slide-btns .hero-video-btn {
    margin-left: 20px;
  }
}

/*-------------------------------------------
	hero style 1
--------------------------------------------*/
.static-hero,
.static-hero-s2,
.static-hero-s3,
.static-hero-s4,
.static-hero-s5 {
  background: #EEF9F6;
  height: 900px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  z-index: 0;
  overflow: hidden;
  margin: 0 50px;
  border-radius: 20px;
}

@media (max-width: 1500px) {
  .static-hero,
  .static-hero-s2,
  .static-hero-s3,
  .static-hero-s4,
  .static-hero-s5 {
    margin: 0 15px;
  }
}

@media (max-width: 1399px) {
  .static-hero,
  .static-hero-s2,
  .static-hero-s3,
  .static-hero-s4,
  .static-hero-s5 {
    height: 850px;
  }
}

@media (max-width: 1199px) {
  .static-hero,
  .static-hero-s2,
  .static-hero-s3,
  .static-hero-s4,
  .static-hero-s5 {
    height: 720px;
  }
}

@media (max-width: 991px) {
  .static-hero,
  .static-hero-s2,
  .static-hero-s3,
  .static-hero-s4,
  .static-hero-s5 {
    height: 100%;
  }
  .static-hero .hero-container,
  .static-hero-s2 .hero-container,
  .static-hero-s3 .hero-container,
  .static-hero-s4 .hero-container,
  .static-hero-s5 .hero-container {
    position: relative;
    padding: 100px 0 0;
  }
  .static-hero .hero-container .hero-inner,
  .static-hero-s2 .hero-container .hero-inner,
  .static-hero-s3 .hero-container .hero-inner,
  .static-hero-s4 .hero-container .hero-inner,
  .static-hero-s5 .hero-container .hero-inner {
    position: relative;
  }
}

@media (max-width: 575px) {
  .static-hero .hero-container,
  .static-hero-s2 .hero-container,
  .static-hero-s3 .hero-container,
  .static-hero-s4 .hero-container,
  .static-hero-s5 .hero-container {
    padding: 70px 0 0;
  }
}

.static-hero .slide-title,
.static-hero-s2 .slide-title,
.static-hero-s3 .slide-title,
.static-hero-s4 .slide-title,
.static-hero-s5 .slide-title {
  padding-top: 10px;
}

@media (max-width: 991px) {
  .static-hero .slide-title,
  .static-hero-s2 .slide-title,
  .static-hero-s3 .slide-title,
  .static-hero-s4 .slide-title,
  .static-hero-s5 .slide-title {
    margin: auto;
  }
}

@media (max-width: 575px) {
  .static-hero .slide-title,
  .static-hero-s2 .slide-title,
  .static-hero-s3 .slide-title,
  .static-hero-s4 .slide-title,
  .static-hero-s5 .slide-title {
    padding-top: 0;
  }
}

.static-hero .slide-title h2,
.static-hero-s2 .slide-title h2,
.static-hero-s3 .slide-title h2,
.static-hero-s4 .slide-title h2,
.static-hero-s5 .slide-title h2 {
  font-size: 70px;
  font-style: normal;
  font-weight: 700;
  color: #1A1729;
  z-index: 1;
}

@media (max-width: 1399px) {
  .static-hero .slide-title h2,
  .static-hero-s2 .slide-title h2,
  .static-hero-s3 .slide-title h2,
  .static-hero-s4 .slide-title h2,
  .static-hero-s5 .slide-title h2 {
    font-size: 55px;
    line-height: 65px;
  }
}

@media (max-width: 1199px) {
  .static-hero .slide-title h2,
  .static-hero-s2 .slide-title h2,
  .static-hero-s3 .slide-title h2,
  .static-hero-s4 .slide-title h2,
  .static-hero-s5 .slide-title h2 {
    font-size: 50px;
    line-height: 65px;
  }
}

@media (max-width: 1070px) {
  .static-hero .slide-title h2,
  .static-hero-s2 .slide-title h2,
  .static-hero-s3 .slide-title h2,
  .static-hero-s4 .slide-title h2,
  .static-hero-s5 .slide-title h2 {
    font-size: 40px;
    line-height: 55px;
  }
}

@media (max-width: 575px) {
  .static-hero .slide-title h2,
  .static-hero-s2 .slide-title h2,
  .static-hero-s3 .slide-title h2,
  .static-hero-s4 .slide-title h2,
  .static-hero-s5 .slide-title h2 {
    font-size: 30px;
    line-height: 47px;
  }
}

.static-hero .slide-title h2 span,
.static-hero-s2 .slide-title h2 span,
.static-hero-s3 .slide-title h2 span,
.static-hero-s4 .slide-title h2 span,
.static-hero-s5 .slide-title h2 span {
  position: relative;
  z-index: -1;
}

.static-hero .slide-title h2 span .shape,
.static-hero-s2 .slide-title h2 span .shape,
.static-hero-s3 .slide-title h2 span .shape,
.static-hero-s4 .slide-title h2 span .shape,
.static-hero-s5 .slide-title h2 span .shape {
  position: absolute;
  top: -10px;
  left: -20px;
  width: 115%;
  z-index: -1;
}

.static-hero .slide-title h2 span .shape svg,
.static-hero-s2 .slide-title h2 span .shape svg,
.static-hero-s3 .slide-title h2 span .shape svg,
.static-hero-s4 .slide-title h2 span .shape svg,
.static-hero-s5 .slide-title h2 span .shape svg {
  fill: #F2BC00;
}

@media (max-width: 991px) {
  .static-hero .slide-title-sub,
  .static-hero-s2 .slide-title-sub,
  .static-hero-s3 .slide-title-sub,
  .static-hero-s4 .slide-title-sub,
  .static-hero-s5 .slide-title-sub {
    margin: auto;
  }
}

.static-hero .slide-title-sub span,
.static-hero-s2 .slide-title-sub span,
.static-hero-s3 .slide-title-sub span,
.static-hero-s4 .slide-title-sub span,
.static-hero-s5 .slide-title-sub span {
  font-size: 18px;
  line-height: 23px;
  font-weight: 700;
  color: #15A280;
}

@media (max-width: 1199px) {
  .static-hero .slide-title-sub span,
  .static-hero-s2 .slide-title-sub span,
  .static-hero-s3 .slide-title-sub span,
  .static-hero-s4 .slide-title-sub span,
  .static-hero-s5 .slide-title-sub span {
    font-size: 16px;
  }
}

@media (max-width: 991px) {
  .static-hero .slide-text,
  .static-hero-s2 .slide-text,
  .static-hero-s3 .slide-text,
  .static-hero-s4 .slide-text,
  .static-hero-s5 .slide-text {
    margin: auto;
  }
}

.static-hero .slide-text p,
.static-hero-s2 .slide-text p,
.static-hero-s3 .slide-text p,
.static-hero-s4 .slide-text p,
.static-hero-s5 .slide-text p {
  color: #625F71;
}

@media (max-width: 1199px) {
  .static-hero .slide-text p,
  .static-hero-s2 .slide-text p,
  .static-hero-s3 .slide-text p,
  .static-hero-s4 .slide-text p,
  .static-hero-s5 .slide-text p {
    font-size: 16px;
  }
}

@media (max-width: 991px) {
  .static-hero .slide-text p,
  .static-hero-s2 .slide-text p,
  .static-hero-s3 .slide-text p,
  .static-hero-s4 .slide-text p,
  .static-hero-s5 .slide-text p {
    margin: auto;
  }
}

.static-hero .hero-inner .container,
.static-hero-s2 .hero-inner .container,
.static-hero-s3 .hero-inner .container,
.static-hero-s4 .hero-inner .container,
.static-hero-s5 .hero-inner .container {
  position: relative;
}

.static-hero .hero-content,
.static-hero-s2 .hero-content,
.static-hero-s3 .hero-content,
.static-hero-s4 .hero-content,
.static-hero-s5 .hero-content {
  padding-left: 95px;
  max-width: 740px;
}

@media (max-width: 1600px) {
  .static-hero .hero-content,
  .static-hero-s2 .hero-content,
  .static-hero-s3 .hero-content,
  .static-hero-s4 .hero-content,
  .static-hero-s5 .hero-content {
    padding-left: 85px;
    max-width: 650px;
  }
}

@media (max-width: 1499px) {
  .static-hero .hero-content,
  .static-hero-s2 .hero-content,
  .static-hero-s3 .hero-content,
  .static-hero-s4 .hero-content,
  .static-hero-s5 .hero-content {
    padding-left: 30px;
    max-width: 575px;
  }
}

@media (max-width: 1399px) {
  .static-hero .hero-content,
  .static-hero-s2 .hero-content,
  .static-hero-s3 .hero-content,
  .static-hero-s4 .hero-content,
  .static-hero-s5 .hero-content {
    max-width: 450px;
  }
}

@media (max-width: 1199px) {
  .static-hero .hero-content,
  .static-hero-s2 .hero-content,
  .static-hero-s3 .hero-content,
  .static-hero-s4 .hero-content,
  .static-hero-s5 .hero-content {
    padding-left: 30px;
    max-width: 470px;
  }
}

@media (max-width: 1070px) {
  .static-hero .hero-content,
  .static-hero-s2 .hero-content,
  .static-hero-s3 .hero-content,
  .static-hero-s4 .hero-content,
  .static-hero-s5 .hero-content {
    max-width: 350px;
  }
}

@media (max-width: 991px) {
  .static-hero .hero-content,
  .static-hero-s2 .hero-content,
  .static-hero-s3 .hero-content,
  .static-hero-s4 .hero-content,
  .static-hero-s5 .hero-content {
    max-width: 100%;
    text-align: center;
    padding-left: 0;
  }
}

.static-hero .hero-inner::before,
.static-hero-s2 .hero-inner::before,
.static-hero-s3 .hero-inner::before,
.static-hero-s4 .hero-inner::before,
.static-hero-s5 .hero-inner::before {
  display: none;
}

@media (max-width: 1400px) {
  .static-hero .slide-text p,
  .static-hero-s2 .slide-text p,
  .static-hero-s3 .slide-text p,
  .static-hero-s4 .slide-text p,
  .static-hero-s5 .slide-text p {
    max-width: 600px;
  }
}

.static-hero .slide-btns,
.static-hero-s2 .slide-btns,
.static-hero-s3 .slide-btns,
.static-hero-s4 .slide-btns,
.static-hero-s5 .slide-btns {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 40px;
}

.static-hero .slide-btns ul,
.static-hero-s2 .slide-btns ul,
.static-hero-s3 .slide-btns ul,
.static-hero-s4 .slide-btns ul,
.static-hero-s5 .slide-btns ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  list-style: none;
  padding-left: 20px;
}

.static-hero .slide-btns ul li .video-btn,
.static-hero-s2 .slide-btns ul li .video-btn,
.static-hero-s3 .slide-btns ul li .video-btn,
.static-hero-s4 .slide-btns ul li .video-btn,
.static-hero-s5 .slide-btns ul li .video-btn {
  display: block;
  width: 55px;
  height: 55px;
  border: 1px solid #15A280;
  text-align: center;
  line-height: 55px;
  border-radius: 50%;
  color: #15A280;
  margin-right: 10px;
}

@media (max-width: 1070px) {
  .static-hero .slide-btns ul li.video-text,
  .static-hero-s2 .slide-btns ul li.video-text,
  .static-hero-s3 .slide-btns ul li.video-text,
  .static-hero-s4 .slide-btns ul li.video-text,
  .static-hero-s5 .slide-btns ul li.video-text {
    display: none;
  }
}

.static-hero .slide-btns ul li.video-text a,
.static-hero-s2 .slide-btns ul li.video-text a,
.static-hero-s3 .slide-btns ul li.video-text a,
.static-hero-s4 .slide-btns ul li.video-text a,
.static-hero-s5 .slide-btns ul li.video-text a {
  color: #15A280;
  font-weight: 600;
  font-size: 20px;
  width: 100%;
  height: 100%;
  -webkit-box-shadow: none;
          box-shadow: none;
  border: none;
}

@media (max-width: 991px) {
  .static-hero .slide-btns,
  .static-hero-s2 .slide-btns,
  .static-hero-s3 .slide-btns,
  .static-hero-s4 .slide-btns,
  .static-hero-s5 .slide-btns {
    margin: auto;
    margin-top: 20px;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.static-hero .student-pic,
.static-hero-s2 .student-pic,
.static-hero-s3 .student-pic,
.static-hero-s4 .student-pic,
.static-hero-s5 .student-pic {
  position: absolute;
  right: 160px;
  top: -30px;
  z-index: 1;
}

@media (max-width: 1750px) {
  .static-hero .student-pic,
  .static-hero-s2 .student-pic,
  .static-hero-s3 .student-pic,
  .static-hero-s4 .student-pic,
  .static-hero-s5 .student-pic {
    right: 90px;
    max-width: 550px;
    top: 45%;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
  }
}

@media (max-width: 1399px) {
  .static-hero .student-pic,
  .static-hero-s2 .student-pic,
  .static-hero-s3 .student-pic,
  .static-hero-s4 .student-pic,
  .static-hero-s5 .student-pic {
    max-width: 500px;
  }
}

@media (max-width: 1300px) {
  .static-hero .student-pic,
  .static-hero-s2 .student-pic,
  .static-hero-s3 .student-pic,
  .static-hero-s4 .student-pic,
  .static-hero-s5 .student-pic {
    max-width: 450px;
    right: 60px;
  }
}

@media (max-width: 991px) {
  .static-hero .student-pic,
  .static-hero-s2 .student-pic,
  .static-hero-s3 .student-pic,
  .static-hero-s4 .student-pic,
  .static-hero-s5 .student-pic {
    position: relative;
    bottom: 0;
    margin: auto;
    padding-top: 50px;
    right: -30px;
    -webkit-transform: unset;
            transform: unset;
  }
}

@media (max-width: 767px) {
  .static-hero .student-pic,
  .static-hero-s2 .student-pic,
  .static-hero-s3 .student-pic,
  .static-hero-s4 .student-pic,
  .static-hero-s5 .student-pic {
    margin-top: 30px;
    right: 0;
  }
}

@media (max-width: 767px) {
  .static-hero .student-pic .student-shape,
  .static-hero-s2 .student-pic .student-shape,
  .static-hero-s3 .student-pic .student-shape,
  .static-hero-s4 .student-pic .student-shape,
  .static-hero-s5 .student-pic .student-shape {
    position: absolute;
    left: 40px;
    top: 0;
    width: 100%;
  }
}

@media (max-width: 575px) {
  .static-hero .student-pic .student-shape,
  .static-hero-s2 .student-pic .student-shape,
  .static-hero-s3 .student-pic .student-shape,
  .static-hero-s4 .student-pic .student-shape,
  .static-hero-s5 .student-pic .student-shape {
    display: none;
  }
}

.static-hero .student-pic .student-shape .shape-1,
.static-hero .student-pic .student-shape .shape-2,
.static-hero .student-pic .student-shape .shape-3,
.static-hero-s2 .student-pic .student-shape .shape-1,
.static-hero-s2 .student-pic .student-shape .shape-2,
.static-hero-s2 .student-pic .student-shape .shape-3,
.static-hero-s3 .student-pic .student-shape .shape-1,
.static-hero-s3 .student-pic .student-shape .shape-2,
.static-hero-s3 .student-pic .student-shape .shape-3,
.static-hero-s4 .student-pic .student-shape .shape-1,
.static-hero-s4 .student-pic .student-shape .shape-2,
.static-hero-s4 .student-pic .student-shape .shape-3,
.static-hero-s5 .student-pic .student-shape .shape-1,
.static-hero-s5 .student-pic .student-shape .shape-2,
.static-hero-s5 .student-pic .student-shape .shape-3 {
  position: absolute;
  border: 7px solid #F2BC00;
  background: #fff;
  left: 6px;
  top: 170px;
  width: 600px;
  height: 600px;
  border-radius: 50%;
  z-index: -1;
}

@media (max-width: 1750px) {
  .static-hero .student-pic .student-shape .shape-1,
  .static-hero .student-pic .student-shape .shape-2,
  .static-hero .student-pic .student-shape .shape-3,
  .static-hero-s2 .student-pic .student-shape .shape-1,
  .static-hero-s2 .student-pic .student-shape .shape-2,
  .static-hero-s2 .student-pic .student-shape .shape-3,
  .static-hero-s3 .student-pic .student-shape .shape-1,
  .static-hero-s3 .student-pic .student-shape .shape-2,
  .static-hero-s3 .student-pic .student-shape .shape-3,
  .static-hero-s4 .student-pic .student-shape .shape-1,
  .static-hero-s4 .student-pic .student-shape .shape-2,
  .static-hero-s4 .student-pic .student-shape .shape-3,
  .static-hero-s5 .student-pic .student-shape .shape-1,
  .static-hero-s5 .student-pic .student-shape .shape-2,
  .static-hero-s5 .student-pic .student-shape .shape-3 {
    width: 500px;
    height: 500px;
  }
}

@media (max-width: 1399px) {
  .static-hero .student-pic .student-shape .shape-1,
  .static-hero .student-pic .student-shape .shape-2,
  .static-hero .student-pic .student-shape .shape-3,
  .static-hero-s2 .student-pic .student-shape .shape-1,
  .static-hero-s2 .student-pic .student-shape .shape-2,
  .static-hero-s2 .student-pic .student-shape .shape-3,
  .static-hero-s3 .student-pic .student-shape .shape-1,
  .static-hero-s3 .student-pic .student-shape .shape-2,
  .static-hero-s3 .student-pic .student-shape .shape-3,
  .static-hero-s4 .student-pic .student-shape .shape-1,
  .static-hero-s4 .student-pic .student-shape .shape-2,
  .static-hero-s4 .student-pic .student-shape .shape-3,
  .static-hero-s5 .student-pic .student-shape .shape-1,
  .static-hero-s5 .student-pic .student-shape .shape-2,
  .static-hero-s5 .student-pic .student-shape .shape-3 {
    width: 400px;
    height: 400px;
  }
}

@media (max-width: 1300px) {
  .static-hero .student-pic .student-shape .shape-1,
  .static-hero .student-pic .student-shape .shape-2,
  .static-hero .student-pic .student-shape .shape-3,
  .static-hero-s2 .student-pic .student-shape .shape-1,
  .static-hero-s2 .student-pic .student-shape .shape-2,
  .static-hero-s2 .student-pic .student-shape .shape-3,
  .static-hero-s3 .student-pic .student-shape .shape-1,
  .static-hero-s3 .student-pic .student-shape .shape-2,
  .static-hero-s3 .student-pic .student-shape .shape-3,
  .static-hero-s4 .student-pic .student-shape .shape-1,
  .static-hero-s4 .student-pic .student-shape .shape-2,
  .static-hero-s4 .student-pic .student-shape .shape-3,
  .static-hero-s5 .student-pic .student-shape .shape-1,
  .static-hero-s5 .student-pic .student-shape .shape-2,
  .static-hero-s5 .student-pic .student-shape .shape-3 {
    width: 300px;
    height: 300px;
  }
}

.static-hero .student-pic .student-shape .shape-2,
.static-hero-s2 .student-pic .student-shape .shape-2,
.static-hero-s3 .student-pic .student-shape .shape-2,
.static-hero-s4 .student-pic .student-shape .shape-2,
.static-hero-s5 .student-pic .student-shape .shape-2 {
  background: none;
  left: -45px;
  top: 120px;
  width: 700px;
  height: 700px;
}

@media (max-width: 1750px) {
  .static-hero .student-pic .student-shape .shape-2,
  .static-hero-s2 .student-pic .student-shape .shape-2,
  .static-hero-s3 .student-pic .student-shape .shape-2,
  .static-hero-s4 .student-pic .student-shape .shape-2,
  .static-hero-s5 .student-pic .student-shape .shape-2 {
    width: 600px;
    height: 600px;
  }
}

@media (max-width: 1399px) {
  .static-hero .student-pic .student-shape .shape-2,
  .static-hero-s2 .student-pic .student-shape .shape-2,
  .static-hero-s3 .student-pic .student-shape .shape-2,
  .static-hero-s4 .student-pic .student-shape .shape-2,
  .static-hero-s5 .student-pic .student-shape .shape-2 {
    width: 500px;
    height: 500px;
  }
}

@media (max-width: 1300px) {
  .static-hero .student-pic .student-shape .shape-2,
  .static-hero-s2 .student-pic .student-shape .shape-2,
  .static-hero-s3 .student-pic .student-shape .shape-2,
  .static-hero-s4 .student-pic .student-shape .shape-2,
  .static-hero-s5 .student-pic .student-shape .shape-2 {
    width: 400px;
    height: 400px;
  }
}

.static-hero .student-pic .student-shape .shape-3,
.static-hero-s2 .student-pic .student-shape .shape-3,
.static-hero-s3 .student-pic .student-shape .shape-3,
.static-hero-s4 .student-pic .student-shape .shape-3,
.static-hero-s5 .student-pic .student-shape .shape-3 {
  background: #fff;
  left: -90px;
  top: 75px;
  width: 786px;
  height: 786px;
  z-index: -3;
}

@media (max-width: 1750px) {
  .static-hero .student-pic .student-shape .shape-3,
  .static-hero-s2 .student-pic .student-shape .shape-3,
  .static-hero-s3 .student-pic .student-shape .shape-3,
  .static-hero-s4 .student-pic .student-shape .shape-3,
  .static-hero-s5 .student-pic .student-shape .shape-3 {
    width: 700px;
    height: 700px;
  }
}

@media (max-width: 1399px) {
  .static-hero .student-pic .student-shape .shape-3,
  .static-hero-s2 .student-pic .student-shape .shape-3,
  .static-hero-s3 .student-pic .student-shape .shape-3,
  .static-hero-s4 .student-pic .student-shape .shape-3,
  .static-hero-s5 .student-pic .student-shape .shape-3 {
    width: 600px;
    height: 600px;
  }
}

@media (max-width: 1300px) {
  .static-hero .student-pic .student-shape .shape-3,
  .static-hero-s2 .student-pic .student-shape .shape-3,
  .static-hero-s3 .student-pic .student-shape .shape-3,
  .static-hero-s4 .student-pic .student-shape .shape-3,
  .static-hero-s5 .student-pic .student-shape .shape-3 {
    width: 500px;
    height: 500px;
  }
}

@media (max-width: 575px) {
  .static-hero .student-pic .student-shape .shape-3,
  .static-hero-s2 .student-pic .student-shape .shape-3,
  .static-hero-s3 .student-pic .student-shape .shape-3,
  .static-hero-s4 .student-pic .student-shape .shape-3,
  .static-hero-s5 .student-pic .student-shape .shape-3 {
    display: none;
  }
}

.static-hero .student-pic .student,
.static-hero-s2 .student-pic .student,
.static-hero-s3 .student-pic .student,
.static-hero-s4 .student-pic .student,
.static-hero-s5 .student-pic .student {
  position: absolute;
  right: -11%;
  top: 12%;
  max-width: 312px;
  background: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 30px 20px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  text-align: left;
  border-radius: 10px;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
}

@media (max-width: 767px) {
  .static-hero .student-pic .student,
  .static-hero-s2 .student-pic .student,
  .static-hero-s3 .student-pic .student,
  .static-hero-s4 .student-pic .student,
  .static-hero-s5 .student-pic .student {
    left: 50%;
    max-width: 290px;
    width: 260px;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
    top: 0;
    padding: 20px 20px;
  }
}

.static-hero .student-pic .student h3,
.static-hero-s2 .student-pic .student h3,
.static-hero-s3 .student-pic .student h3,
.static-hero-s4 .student-pic .student h3,
.static-hero-s5 .student-pic .student h3 {
  margin-bottom: 0;
  font-weight: 700;
  font-size: 26px;
  line-height: 33px;
}

@media (max-width: 767px) {
  .static-hero .student-pic .student h3,
  .static-hero-s2 .student-pic .student h3,
  .static-hero-s3 .student-pic .student h3,
  .static-hero-s4 .student-pic .student h3,
  .static-hero-s5 .student-pic .student h3 {
    font-size: 20px;
    line-height: 28px;
  }
}

.static-hero .student-pic .student p,
.static-hero-s2 .student-pic .student p,
.static-hero-s3 .student-pic .student p,
.static-hero-s4 .student-pic .student p,
.static-hero-s5 .student-pic .student p {
  margin-bottom: 0;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
}

@media (max-width: 767px) {
  .static-hero .student-pic .student p,
  .static-hero-s2 .student-pic .student p,
  .static-hero-s3 .student-pic .student p,
  .static-hero-s4 .student-pic .student p,
  .static-hero-s5 .student-pic .student p {
    font-size: 15px;
  }
}

.static-hero .student-pic .student .icon i,
.static-hero-s2 .student-pic .student .icon i,
.static-hero-s3 .student-pic .student .icon i,
.static-hero-s4 .student-pic .student .icon i,
.static-hero-s5 .student-pic .student .icon i {
  width: 60px;
  height: 60px;
  line-height: 60px;
  background: #15A280;
  text-align: center;
  border-radius: 50%;
  margin-right: 10px;
  display: block;
  color: #fff;
  font-size: 30px;
}

.static-hero .student-pic .wpo-supporter,
.static-hero-s2 .student-pic .wpo-supporter,
.static-hero-s3 .student-pic .wpo-supporter,
.static-hero-s4 .student-pic .wpo-supporter,
.static-hero-s5 .student-pic .wpo-supporter {
  position: absolute;
  left: -22%;
  bottom: 3%;
  padding: 30px;
  background: #fff;
  border-radius: 10px;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
  z-index: 99;
  text-align: center;
}

@media (max-width: 1199px) {
  .static-hero .student-pic .wpo-supporter,
  .static-hero-s2 .student-pic .wpo-supporter,
  .static-hero-s3 .student-pic .wpo-supporter,
  .static-hero-s4 .student-pic .wpo-supporter,
  .static-hero-s5 .student-pic .wpo-supporter {
    padding: 15px;
    top: auto;
  }
}

@media (max-width: 767px) {
  .static-hero .student-pic .wpo-supporter,
  .static-hero-s2 .student-pic .wpo-supporter,
  .static-hero-s3 .student-pic .wpo-supporter,
  .static-hero-s4 .student-pic .wpo-supporter,
  .static-hero-s5 .student-pic .wpo-supporter {
    padding: 15px;
    top: auto;
    right: auto;
    width: 100%;
    left: 0;
  }
}

.static-hero .student-pic .wpo-supporter .wpo-supporter-text,
.static-hero-s2 .student-pic .wpo-supporter .wpo-supporter-text,
.static-hero-s3 .student-pic .wpo-supporter .wpo-supporter-text,
.static-hero-s4 .student-pic .wpo-supporter .wpo-supporter-text,
.static-hero-s5 .student-pic .wpo-supporter .wpo-supporter-text {
  text-align: center;
}

.static-hero .student-pic .wpo-supporter .wpo-supporter-text h3,
.static-hero-s2 .student-pic .wpo-supporter .wpo-supporter-text h3,
.static-hero-s3 .student-pic .wpo-supporter .wpo-supporter-text h3,
.static-hero-s4 .student-pic .wpo-supporter .wpo-supporter-text h3,
.static-hero-s5 .student-pic .wpo-supporter .wpo-supporter-text h3 {
  font-size: 40px;
  font-style: normal;
  font-weight: 700;
  margin-bottom: 0px;
}

.static-hero .student-pic .wpo-supporter .wpo-supporter-text p,
.static-hero-s2 .student-pic .wpo-supporter .wpo-supporter-text p,
.static-hero-s3 .student-pic .wpo-supporter .wpo-supporter-text p,
.static-hero-s4 .student-pic .wpo-supporter .wpo-supporter-text p,
.static-hero-s5 .student-pic .wpo-supporter .wpo-supporter-text p {
  margin-bottom: 0;
  font-style: normal;
  font-weight: 400;
  font-size: 20px;
  line-height: 27px;
}

.static-hero .student-pic .wpo-supporter .wpo-supporter-slide,
.static-hero-s2 .student-pic .wpo-supporter .wpo-supporter-slide,
.static-hero-s3 .student-pic .wpo-supporter .wpo-supporter-slide,
.static-hero-s4 .student-pic .wpo-supporter .wpo-supporter-slide,
.static-hero-s5 .student-pic .wpo-supporter .wpo-supporter-slide {
  max-width: 216px;
  list-style: none;
  margin: 0 auto;
  margin-top: 20px;
}

.static-hero .student-pic .wpo-supporter .wpo-supporter-slide .owl-nav,
.static-hero-s2 .student-pic .wpo-supporter .wpo-supporter-slide .owl-nav,
.static-hero-s3 .student-pic .wpo-supporter .wpo-supporter-slide .owl-nav,
.static-hero-s4 .student-pic .wpo-supporter .wpo-supporter-slide .owl-nav,
.static-hero-s5 .student-pic .wpo-supporter .wpo-supporter-slide .owl-nav {
  display: none;
}

.static-hero .student-pic .wpo-supporter .wpo-supporter-slide li,
.static-hero-s2 .student-pic .wpo-supporter .wpo-supporter-slide li,
.static-hero-s3 .student-pic .wpo-supporter .wpo-supporter-slide li,
.static-hero-s4 .student-pic .wpo-supporter .wpo-supporter-slide li,
.static-hero-s5 .student-pic .wpo-supporter .wpo-supporter-slide li {
  width: 55px;
  height: 55px;
}

.static-hero .student-pic .wpo-supporter .wpo-supporter-slide li img,
.static-hero-s2 .student-pic .wpo-supporter .wpo-supporter-slide li img,
.static-hero-s3 .student-pic .wpo-supporter .wpo-supporter-slide li img,
.static-hero-s4 .student-pic .wpo-supporter .wpo-supporter-slide li img,
.static-hero-s5 .student-pic .wpo-supporter .wpo-supporter-slide li img {
  width: 55px;
  height: 55px;
  padding: 3px;
  border: 1px solid #D9D9D9;
  border-radius: 50%;
  border-radius: 50%;
}

.static-hero .student-pic .wp-shape-1,
.static-hero-s2 .student-pic .wp-shape-1,
.static-hero-s3 .student-pic .wp-shape-1,
.static-hero-s4 .student-pic .wp-shape-1,
.static-hero-s5 .student-pic .wp-shape-1 {
  position: absolute;
  left: 0;
  top: 90px;
  -webkit-animation: bounce 3s linear infinite;
          animation: bounce 3s linear infinite;
}

@media (max-width: 1300px) {
  .static-hero .student-pic .wp-shape-1,
  .static-hero-s2 .student-pic .wp-shape-1,
  .static-hero-s3 .student-pic .wp-shape-1,
  .static-hero-s4 .student-pic .wp-shape-1,
  .static-hero-s5 .student-pic .wp-shape-1 {
    top: 50px;
    left: -20px;
  }
}

@media (max-width: 575px) {
  .static-hero .student-pic .wp-shape-1,
  .static-hero-s2 .student-pic .wp-shape-1,
  .static-hero-s3 .student-pic .wp-shape-1,
  .static-hero-s4 .student-pic .wp-shape-1,
  .static-hero-s5 .student-pic .wp-shape-1 {
    top: 120px;
  }
}

.static-hero .student-pic .wp-shape-2,
.static-hero-s2 .student-pic .wp-shape-2,
.static-hero-s3 .student-pic .wp-shape-2,
.static-hero-s4 .student-pic .wp-shape-2,
.static-hero-s5 .student-pic .wp-shape-2 {
  position: absolute;
  left: -25%;
  top: 50%;
  -webkit-animation: zoom-in-zoom-out 2s ease infinite;
          animation: zoom-in-zoom-out 2s ease infinite;
}

@media (max-width: 1300px) {
  .static-hero .student-pic .wp-shape-2,
  .static-hero-s2 .student-pic .wp-shape-2,
  .static-hero-s3 .student-pic .wp-shape-2,
  .static-hero-s4 .student-pic .wp-shape-2,
  .static-hero-s5 .student-pic .wp-shape-2 {
    left: -40%;
  }
}

@media (max-width: 767px) {
  .static-hero .student-pic .wp-shape-2,
  .static-hero-s2 .student-pic .wp-shape-2,
  .static-hero-s3 .student-pic .wp-shape-2,
  .static-hero-s4 .student-pic .wp-shape-2,
  .static-hero-s5 .student-pic .wp-shape-2 {
    left: -25%;
  }
}

@media (max-width: 575px) {
  .static-hero .student-pic .wp-shape-2,
  .static-hero-s2 .student-pic .wp-shape-2,
  .static-hero-s3 .student-pic .wp-shape-2,
  .static-hero-s4 .student-pic .wp-shape-2,
  .static-hero-s5 .student-pic .wp-shape-2 {
    left: -5%;
  }
}

.static-hero .student-pic .wp-shape-3,
.static-hero-s2 .student-pic .wp-shape-3,
.static-hero-s3 .student-pic .wp-shape-3,
.static-hero-s4 .student-pic .wp-shape-3,
.static-hero-s5 .student-pic .wp-shape-3 {
  position: absolute;
  right: -15%;
  top: 35%;
  -webkit-animation: bounce 3s linear infinite;
          animation: bounce 3s linear infinite;
}

@media (max-width: 1300px) {
  .static-hero .student-pic .wp-shape-3,
  .static-hero-s2 .student-pic .wp-shape-3,
  .static-hero-s3 .student-pic .wp-shape-3,
  .static-hero-s4 .student-pic .wp-shape-3,
  .static-hero-s5 .student-pic .wp-shape-3 {
    right: -5%;
  }
}

@media (max-width: 767px) {
  .static-hero .student-pic .wp-shape-3,
  .static-hero-s2 .student-pic .wp-shape-3,
  .static-hero-s3 .student-pic .wp-shape-3,
  .static-hero-s4 .student-pic .wp-shape-3,
  .static-hero-s5 .student-pic .wp-shape-3 {
    right: -15%;
  }
}

@media (max-width: 575px) {
  .static-hero .student-pic .wp-shape-3,
  .static-hero-s2 .student-pic .wp-shape-3,
  .static-hero-s3 .student-pic .wp-shape-3,
  .static-hero-s4 .student-pic .wp-shape-3,
  .static-hero-s5 .student-pic .wp-shape-3 {
    right: 0;
  }
}

.static-hero .student-pic .wp-shape-4,
.static-hero-s2 .student-pic .wp-shape-4,
.static-hero-s3 .student-pic .wp-shape-4,
.static-hero-s4 .student-pic .wp-shape-4,
.static-hero-s5 .student-pic .wp-shape-4 {
  position: absolute;
  right: -10%;
  bottom: 15%;
  -webkit-animation: bounceX 3s linear infinite;
          animation: bounceX 3s linear infinite;
}

/*-------------------------------------------
 static-hero-s2
--------------------------------------------*/
.static-hero-s2,
.static-hero-s3,
.static-hero-s4,
.static-hero-s5 {
  background: #FAFAFF;
  height: 1080px;
  margin: 0;
}

@media (max-width: 1199px) {
  .static-hero-s2,
  .static-hero-s3,
  .static-hero-s4,
  .static-hero-s5 {
    height: 800px;
  }
}

@media (max-width: 991px) {
  .static-hero-s2,
  .static-hero-s3,
  .static-hero-s4,
  .static-hero-s5 {
    height: 100%;
  }
}

.static-hero-s2:before,
.static-hero-s3:before,
.static-hero-s4:before,
.static-hero-s5:before {
  display: none;
}

.static-hero-s2 .student-pic,
.static-hero-s3 .student-pic,
.static-hero-s4 .student-pic,
.static-hero-s5 .student-pic {
  z-index: 1;
  right: 100px;
  top: 45%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

@media (max-width: 1830px) {
  .static-hero-s2 .student-pic,
  .static-hero-s3 .student-pic,
  .static-hero-s4 .student-pic,
  .static-hero-s5 .student-pic {
    right: 100px;
    max-width: 900px;
  }
}

@media (max-width: 1500px) {
  .static-hero-s2 .student-pic,
  .static-hero-s3 .student-pic,
  .static-hero-s4 .student-pic,
  .static-hero-s5 .student-pic {
    right: 100px;
    max-width: 800px;
  }
}

@media (max-width: 1399px) {
  .static-hero-s2 .student-pic,
  .static-hero-s3 .student-pic,
  .static-hero-s4 .student-pic,
  .static-hero-s5 .student-pic {
    right: 70px;
    max-width: 700px;
  }
}

@media (max-width: 1199px) {
  .static-hero-s2 .student-pic,
  .static-hero-s3 .student-pic,
  .static-hero-s4 .student-pic,
  .static-hero-s5 .student-pic {
    max-width: 450px;
    right: 50px;
  }
}

@media (max-width: 991px) {
  .static-hero-s2 .student-pic,
  .static-hero-s3 .student-pic,
  .static-hero-s4 .student-pic,
  .static-hero-s5 .student-pic {
    right: 0;
    bottom: 50px;
    padding-top: 0;
    top: auto;
    -webkit-transform: unset;
            transform: unset;
    max-width: 100%;
  }
}

.static-hero-s2 .student-pic .inner,
.static-hero-s3 .student-pic .inner,
.static-hero-s4 .student-pic .inner,
.static-hero-s5 .student-pic .inner {
  overflow: hidden;
  height: 860px;
  border-radius: 45%;
  bottom: -14px;
  position: relative;
  border-bottom-left-radius: 44%;
  border-bottom-right-radius: 40%;
}

@media (max-width: 1830px) {
  .static-hero-s2 .student-pic .inner,
  .static-hero-s3 .student-pic .inner,
  .static-hero-s4 .student-pic .inner,
  .static-hero-s5 .student-pic .inner {
    bottom: -75px;
  }
}

@media (max-width: 1199px) {
  .static-hero-s2 .student-pic .inner,
  .static-hero-s3 .student-pic .inner,
  .static-hero-s4 .student-pic .inner,
  .static-hero-s5 .student-pic .inner {
    height: 665px;
    bottom: -52px;
  }
}

.static-hero-s2 .student-pic .inner img,
.static-hero-s3 .student-pic .inner img,
.static-hero-s4 .student-pic .inner img,
.static-hero-s5 .student-pic .inner img {
  margin-bottom: -49px;
  position: relative;
  bottom: -25px;
}

.static-hero-s2 .student-pic .back-shape,
.static-hero-s3 .student-pic .back-shape,
.static-hero-s4 .student-pic .back-shape,
.static-hero-s5 .student-pic .back-shape {
  position: absolute;
  left: -50px;
  bottom: -30px;
  width: 691px;
  height: 691px;
  background: #fff;
  background-size: cover;
  border-radius: 50%;
  z-index: -1;
}

@media (max-width: 1830px) {
  .static-hero-s2 .student-pic .back-shape,
  .static-hero-s3 .student-pic .back-shape,
  .static-hero-s4 .student-pic .back-shape,
  .static-hero-s5 .student-pic .back-shape {
    left: -50px;
    bottom: 72px;
    width: 560px;
    height: 560px;
  }
}

@media (max-width: 1199px) {
  .static-hero-s2 .student-pic .back-shape,
  .static-hero-s3 .student-pic .back-shape,
  .static-hero-s4 .student-pic .back-shape,
  .static-hero-s5 .student-pic .back-shape {
    left: -15px;
    bottom: 72px;
    width: 380px;
    height: 380px;
  }
}

@media (max-width: 380px) {
  .static-hero-s2 .student-pic .back-shape,
  .static-hero-s3 .student-pic .back-shape,
  .static-hero-s4 .student-pic .back-shape,
  .static-hero-s5 .student-pic .back-shape {
    width: 310px;
    height: 310px;
    left: 50%;
    top: 28%;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
  }
}

.static-hero-s2 .student-pic .back-shape::before,
.static-hero-s3 .student-pic .back-shape::before,
.static-hero-s4 .student-pic .back-shape::before,
.static-hero-s5 .student-pic .back-shape::before {
  position: absolute;
  left: 15px;
  top: 15px;
  background: url(../../images/slider/back-shape.png) no-repeat center center;
  content: "";
  z-index: -11;
  border-radius: 50%;
  width: 661px;
  height: 661px;
}

@media (max-width: 1830px) {
  .static-hero-s2 .student-pic .back-shape::before,
  .static-hero-s3 .student-pic .back-shape::before,
  .static-hero-s4 .student-pic .back-shape::before,
  .static-hero-s5 .student-pic .back-shape::before {
    width: 530px;
    height: 530px;
  }
}

@media (max-width: 1199px) {
  .static-hero-s2 .student-pic .back-shape::before,
  .static-hero-s3 .student-pic .back-shape::before,
  .static-hero-s4 .student-pic .back-shape::before,
  .static-hero-s5 .student-pic .back-shape::before {
    width: 351px;
    height: 351px;
  }
}

@media (max-width: 380px) {
  .static-hero-s2 .student-pic .back-shape::before,
  .static-hero-s3 .student-pic .back-shape::before,
  .static-hero-s4 .student-pic .back-shape::before,
  .static-hero-s5 .student-pic .back-shape::before {
    width: 280px;
    height: 280px;
  }
}

.static-hero-s2 .student-pic .wp-shape-1,
.static-hero-s3 .student-pic .wp-shape-1,
.static-hero-s4 .student-pic .wp-shape-1,
.static-hero-s5 .student-pic .wp-shape-1 {
  top: auto;
  bottom: 10%;
  left: -5%;
}

.static-hero-s2 .student-pic .wp-shape-2,
.static-hero-s3 .student-pic .wp-shape-2,
.static-hero-s4 .student-pic .wp-shape-2,
.static-hero-s5 .student-pic .wp-shape-2 {
  left: -5%;
}

.static-hero-s2 .student-pic .wp-shape-3,
.static-hero-s3 .student-pic .wp-shape-3,
.static-hero-s4 .student-pic .wp-shape-3,
.static-hero-s5 .student-pic .wp-shape-3 {
  right: -8%;
}

.static-hero-s2 .hero-content,
.static-hero-s3 .hero-content,
.static-hero-s4 .hero-content,
.static-hero-s5 .hero-content {
  max-width: 730px;
  padding-bottom: 100px;
}

@media (max-width: 1670px) {
  .static-hero-s2 .hero-content,
  .static-hero-s3 .hero-content,
  .static-hero-s4 .hero-content,
  .static-hero-s5 .hero-content {
    max-width: 694px;
  }
}

@media (max-width: 1399px) {
  .static-hero-s2 .hero-content,
  .static-hero-s3 .hero-content,
  .static-hero-s4 .hero-content,
  .static-hero-s5 .hero-content {
    padding-left: 40px;
    max-width: 500px;
  }
}

@media (max-width: 1050px) {
  .static-hero-s2 .hero-content,
  .static-hero-s3 .hero-content,
  .static-hero-s4 .hero-content,
  .static-hero-s5 .hero-content {
    max-width: 360px;
    padding-left: 10px;
  }
}

@media (max-width: 991px) {
  .static-hero-s2 .hero-content,
  .static-hero-s3 .hero-content,
  .static-hero-s4 .hero-content,
  .static-hero-s5 .hero-content {
    margin: 0 auto;
    max-width: 695px;
    padding: 0;
    padding-top: 50px;
  }
}

@media (max-width: 767px) {
  .static-hero-s2 .hero-content,
  .static-hero-s3 .hero-content,
  .static-hero-s4 .hero-content,
  .static-hero-s5 .hero-content {
    padding-top: 20px;
  }
}

.static-hero-s2 .hero-content .slide-title h2 span,
.static-hero-s3 .hero-content .slide-title h2 span,
.static-hero-s4 .hero-content .slide-title h2 span,
.static-hero-s5 .hero-content .slide-title h2 span {
  background: #4540E1;
  color: #fff;
  display: inline-block;
  padding: 0 10px;
  border-radius: 5px;
  position: relative;
  line-height: 80px;
}

@media (max-width: 1199px) {
  .static-hero-s2 .hero-content .slide-title h2 span,
  .static-hero-s3 .hero-content .slide-title h2 span,
  .static-hero-s4 .hero-content .slide-title h2 span,
  .static-hero-s5 .hero-content .slide-title h2 span {
    line-height: 50px;
  }
}

@media (max-width: 1399px) {
  .static-hero-s2 .hero-content .slide-title h2,
  .static-hero-s3 .hero-content .slide-title h2,
  .static-hero-s4 .hero-content .slide-title h2,
  .static-hero-s5 .hero-content .slide-title h2 {
    font-size: 50px;
    line-height: 65px;
  }
}

@media (max-width: 1199px) {
  .static-hero-s2 .hero-content .slide-title h2,
  .static-hero-s3 .hero-content .slide-title h2,
  .static-hero-s4 .hero-content .slide-title h2,
  .static-hero-s5 .hero-content .slide-title h2 {
    font-size: 38px;
    line-height: 55px;
  }
}

@media (max-width: 767px) {
  .static-hero-s2 .hero-content .slide-title h2,
  .static-hero-s3 .hero-content .slide-title h2,
  .static-hero-s4 .hero-content .slide-title h2,
  .static-hero-s5 .hero-content .slide-title h2 {
    font-size: 30px;
    line-height: 45px;
  }
}

.static-hero-s2 .hero-content .slide-text,
.static-hero-s3 .hero-content .slide-text,
.static-hero-s4 .hero-content .slide-text,
.static-hero-s5 .hero-content .slide-text {
  margin: 0;
  margin-top: 20px;
}

@media (max-width: 767px) {
  .static-hero-s2 .hero-content .slide-text,
  .static-hero-s3 .hero-content .slide-text,
  .static-hero-s4 .hero-content .slide-text,
  .static-hero-s5 .hero-content .slide-text {
    margin: 0 auto;
  }
}

.static-hero-s2 .hero-content .slide-text p,
.static-hero-s3 .hero-content .slide-text p,
.static-hero-s4 .hero-content .slide-text p,
.static-hero-s5 .hero-content .slide-text p {
  color: #625F71;
  max-width: 580px;
  font-size: 20px;
  padding-left: 15px;
  border-left: 3px solid #15A280;
}

@media (max-width: 1199px) {
  .static-hero-s2 .hero-content .slide-text p,
  .static-hero-s3 .hero-content .slide-text p,
  .static-hero-s4 .hero-content .slide-text p,
  .static-hero-s5 .hero-content .slide-text p {
    font-size: 16px;
  }
}

@media (max-width: 991px) {
  .static-hero-s2 .hero-content .slide-text p,
  .static-hero-s3 .hero-content .slide-text p,
  .static-hero-s4 .hero-content .slide-text p,
  .static-hero-s5 .hero-content .slide-text p {
    padding-left: 0;
    border: 0;
  }
}

@media (max-width: 767px) {
  .static-hero-s2 .hero-content .slide-text p,
  .static-hero-s3 .hero-content .slide-text p,
  .static-hero-s4 .hero-content .slide-text p,
  .static-hero-s5 .hero-content .slide-text p {
    margin: 0 auto;
  }
}

.static-hero-s2 .bottom-shape,
.static-hero-s3 .bottom-shape,
.static-hero-s4 .bottom-shape,
.static-hero-s5 .bottom-shape {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
}

.static-hero-s2 .bottom-shape svg,
.static-hero-s3 .bottom-shape svg,
.static-hero-s4 .bottom-shape svg,
.static-hero-s5 .bottom-shape svg {
  width: 100%;
}

.static-hero-s3 {
  height: 1000px;
  background: url(../../images/slider/bg.png) no-repeat center top/cover;
}

@media (max-width: 1399px) {
  .static-hero-s3 {
    height: 800px;
  }
}

@media (max-width: 991px) {
  .static-hero-s3 {
    height: 100%;
    padding: 40px 0;
  }
}

@media (max-width: 991px) {
  .static-hero-s3 .hero-container {
    padding-top: 30px;
  }
}

.static-hero-s3 .student-pic {
  max-width: 945px;
  top: 52%;
}

@media (max-width: 1750px) {
  .static-hero-s3 .student-pic {
    max-width: 845px;
  }
}

@media (max-width: 1600px) {
  .static-hero-s3 .student-pic {
    right: 60px;
    max-width: 750px;
  }
}

@media (max-width: 1399px) {
  .static-hero-s3 .student-pic {
    right: 30px;
    max-width: 650px;
  }
}

@media (max-width: 1199px) {
  .static-hero-s3 .student-pic {
    max-width: 550px;
  }
}

@media (max-width: 991px) {
  .static-hero-s3 .student-pic {
    right: auto;
    bottom: 0;
  }
}

@media (max-width: 1399px) {
  .static-hero-s3 .hero-content {
    padding-bottom: 0;
  }
}

.static-hero-s3 .hero-content .slide-title h2 span {
  background: transparent;
  color: #4540E1;
  border: 1px solid #4540E1;
  border-radius: 0;
}

.static-hero-s3 .hero-content .slide-title h2 span .s1,
.static-hero-s3 .hero-content .slide-title h2 span .s2,
.static-hero-s3 .hero-content .slide-title h2 span .s3,
.static-hero-s3 .hero-content .slide-title h2 span .s4 {
  position: absolute;
  width: 10px;
  height: 10px;
  border: 1px solid #4540E1;
  background: #EEF9F6;
}

.static-hero-s3 .hero-content .slide-title h2 span .s1 {
  left: -5px;
  top: -5px;
}

.static-hero-s3 .hero-content .slide-title h2 span .s2 {
  left: -5px;
  bottom: -5px;
}

.static-hero-s3 .hero-content .slide-title h2 span .s3 {
  right: -5px;
  top: -5px;
}

.static-hero-s3 .hero-content .slide-title h2 span .s4 {
  right: -5px;
  bottom: -5px;
}

.static-hero-s4 {
  background: #F2F2F2;
  height: 1000px;
  border-radius: 0;
}

@media (max-width: 1399px) {
  .static-hero-s4 {
    height: 800px;
  }
}

@media (max-width: 991px) {
  .static-hero-s4 {
    height: 100%;
    padding: 40px 0;
  }
}

.static-hero-s4 .hero-content {
  padding-bottom: 0;
}

.static-hero-s4 .hero-content .slide-text p {
  padding-left: 0;
  border: 0;
}

.static-hero-s4 .student-pic {
  top: 52%;
}

@media (max-width: 1750px) {
  .static-hero-s4 .student-pic {
    max-width: 900px;
  }
}

@media (max-width: 1600px) {
  .static-hero-s4 .student-pic {
    right: 60px;
    max-width: 850px;
  }
}

@media (max-width: 1399px) {
  .static-hero-s4 .student-pic {
    right: 0px;
    max-width: 850px;
  }
}

@media (max-width: 1199px) {
  .static-hero-s4 .student-pic {
    max-width: 750px;
    right: 0;
  }
}

@media (max-width: 991px) {
  .static-hero-s4 .student-pic {
    right: auto;
    bottom: 0;
  }
}

.static-hero-s5 {
  background: #EAF8FA;
  height: 1055px;
  border-radius: 0;
}

@media (max-width: 1399px) {
  .static-hero-s5 {
    height: 800px;
  }
}

@media (max-width: 991px) {
  .static-hero-s5 {
    height: 100%;
    padding: 40px 0;
  }
}

@media (max-width: 991px) {
  .static-hero-s5 .hero-container {
    padding: 80px 0;
  }
}

.static-hero-s5 .slide-title {
  padding-top: 0;
}

.static-hero-s5 .slide-title h2 {
  margin-top: 0;
}

.static-hero-s5 .hero-content {
  padding-left: 230px;
}

@media (max-width: 1750px) {
  .static-hero-s5 .hero-content {
    padding-left: 130px;
  }
}

@media (max-width: 1650px) {
  .static-hero-s5 .hero-content {
    padding-left: 30px;
  }
}

@media (max-width: 991px) {
  .static-hero-s5 .hero-content {
    padding: 0;
  }
}

.static-hero-s5 .hero-content .slide-text p {
  padding-left: 0;
  border: 0;
  max-width: 484px;
  font-size: 25px;
  font-style: normal;
  font-weight: 500;
  line-height: 40px;
}

@media (max-width: 1399px) {
  .static-hero-s5 .hero-content .slide-text p {
    max-width: 380px;
    font-size: 20px;
  }
}

.static-hero-s5 .hero-content .slide-title h2 span {
  background: transparent;
  color: #F2BC00;
}

.static-hero-s5 .student-pic {
  z-index: 1;
}

@media (max-width: 1199px) {
  .static-hero-s5 .student-pic {
    max-width: 550px;
  }
}

@media (max-width: 991px) {
  .static-hero-s5 .student-pic {
    bottom: 0;
    margin-top: 70px;
  }
}

.static-hero-s5 .student-pic .vector-1 {
  position: absolute;
  top: 0;
  left: 30%;
  z-index: -1;
  -webkit-animation: bounce 3s linear infinite;
          animation: bounce 3s linear infinite;
}

@media (max-width: 1199px) {
  .static-hero-s5 .student-pic .vector-1 {
    top: -8%;
  }
}

@media (max-width: 575px) {
  .static-hero-s5 .student-pic .vector-1 {
    max-width: 10%;
  }
}

.static-hero-s5 .student-pic .vector-2 {
  position: absolute;
  top: 22%;
  left: 6%;
  z-index: -1;
  -webkit-animation: bounce 5s linear infinite;
          animation: bounce 5s linear infinite;
}

@media (max-width: 1199px) {
  .static-hero-s5 .student-pic .vector-2 {
    left: 2%;
    top: 15%;
  }
}

@media (max-width: 575px) {
  .static-hero-s5 .student-pic .vector-2 {
    max-width: 10%;
  }
}

.static-hero-s5 .student-pic .vector-3 {
  position: absolute;
  bottom: 22%;
  left: -7%;
  z-index: -1;
  -webkit-animation: circle 20s linear infinite;
          animation: circle 20s linear infinite;
}

@media (max-width: 575px) {
  .static-hero-s5 .student-pic .vector-3 {
    max-width: 10%;
  }
}

.static-hero-s5 .student-pic .vector-4 {
  position: absolute;
  bottom: -5%;
  left: 2%;
  z-index: -1;
  -webkit-animation: bounceX 5s linear infinite;
          animation: bounceX 5s linear infinite;
}

@media (max-width: 575px) {
  .static-hero-s5 .student-pic .vector-4 {
    max-width: 10%;
  }
}

.static-hero-s5 .student-pic .vector-5 {
  position: absolute;
  bottom: -12%;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: -1;
  -webkit-animation: bounce 5s linear infinite;
          animation: bounce 5s linear infinite;
}

@media (max-width: 575px) {
  .static-hero-s5 .student-pic .vector-5 {
    max-width: 10%;
  }
}

.static-hero-s5 .student-pic .vector-6 {
  position: absolute;
  bottom: -10%;
  right: 5%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: -1;
  -webkit-animation: bounceX 3s linear infinite;
          animation: bounceX 3s linear infinite;
}

@media (max-width: 575px) {
  .static-hero-s5 .student-pic .vector-6 {
    max-width: 10%;
  }
}

.static-hero-s5 .student-pic .vector-7 {
  position: absolute;
  bottom: 30%;
  right: -13%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: -1;
  -webkit-animation: bounceX 5s linear infinite;
          animation: bounceX 5s linear infinite;
}

@media (max-width: 575px) {
  .static-hero-s5 .student-pic .vector-7 {
    max-width: 10%;
  }
}

.static-hero-s5 .student-pic .vector-8 {
  position: absolute;
  top: 28%;
  right: 12%;
  z-index: -1;
  -webkit-animation: circle 10s linear infinite;
          animation: circle 10s linear infinite;
}

@media (max-width: 575px) {
  .static-hero-s5 .student-pic .vector-8 {
    max-width: 10%;
  }
}

.static-hero-s5 .student-pic .vector-9 {
  position: absolute;
  top: 0%;
  right: 12%;
  z-index: -1;
  -webkit-animation: bounceX 5s linear infinite;
          animation: bounceX 5s linear infinite;
}

@media (max-width: 575px) {
  .static-hero-s5 .student-pic .vector-9 {
    max-width: 10%;
  }
}

/*--------------------------------------------------------------
4. wpo-footer
--------------------------------------------------------------*/
.wpo-site-footer {
  background: #1A1729;
  background-size: cover;
  position: relative;
  font-size: 16px;
  overflow: hidden;
  z-index: 1;
  /*** contact-widget ***/
}

.wpo-site-footer:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: url(../../images/noise.png);
}

.wpo-site-footer ul {
  list-style: none;
}

.wpo-site-footer p {
  color: #fff;
}

.wpo-site-footer li {
  color: #fff;
}

.wpo-site-footer .container {
  position: relative;
}

.wpo-site-footer .wpo-upper-footer {
  padding: 100px 0;
  position: relative;
  overflow: hidden;
}

.wpo-site-footer .wpo-upper-footer .navbar-brand {
  padding-top: 0;
  color: #fff;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  position: relative;
  top: -10px;
}

@media (max-width: 991px) {
  .wpo-site-footer .wpo-upper-footer {
    padding: 80px 0 0;
  }
}

@media (max-width: 767px) {
  .wpo-site-footer .wpo-upper-footer {
    padding: 60px 0 0;
  }
}

@media (max-width: 991px) {
  .wpo-site-footer .wpo-upper-footer .col {
    min-height: 235px;
    margin-bottom: 70px;
  }
}

@media (max-width: 767px) {
  .wpo-site-footer .wpo-upper-footer .col {
    min-height: auto;
    margin-bottom: 60px;
  }
}

.wpo-site-footer .widget-title {
  margin-bottom: 20px;
}

.wpo-site-footer .widget-title h3 {
  font-size: 30px;
  font-style: normal;
  font-weight: 700;
  color: #fff;
  margin: 0;
  position: relative;
  font-family: "Urbanist";
}

.wpo-site-footer .about-widget {
  max-width: 245px;
}

.wpo-site-footer .about-widget p {
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;
  margin-bottom: 25px;
}

.wpo-site-footer .about-widget .social ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 20px;
}

.wpo-site-footer .about-widget .social ul li + li {
  margin-left: 10px;
}

.wpo-site-footer .about-widget .social ul li a {
  color: #fff;
  width: 40px;
  height: 40px;
  line-height: 40px;
  background: rgba(255, 255, 255, 0.15);
  display: block;
  text-align: center;
  font-size: 16px;
  border-radius: 50%;
}

.wpo-site-footer .about-widget .social ul li a:hover {
  background: #F2BC00;
}

.wpo-site-footer .about-widget .social ul li a .fi:before {
  color: #fff;
  font-size: 14px;
}

.wpo-site-footer .wpo-service-link-widget {
  padding-left: 70px;
}

.wpo-site-footer .link-widget {
  overflow: hidden;
  padding-left: 15px;
}

@media (max-width: 1200px) {
  .wpo-site-footer .link-widget {
    padding: 0;
  }
}

.wpo-site-footer .link-widget.s2 {
  padding-left: 15px;
}

@media (max-width: 1200px) {
  .wpo-site-footer .link-widget.s2 {
    padding: 0;
  }
}

.wpo-site-footer .link-widget ul li {
  position: relative;
  z-index: 1;
  padding: 8px 0;
  padding-left: 20px;
}

.wpo-site-footer .link-widget ul li:before {
  position: absolute;
  left: 0px;
  top: 16px;
  width: 6px;
  height: 6px;
  z-index: -1;
  background: #D9D9D9;
  content: "";
  border-radius: 50%;
}

.wpo-site-footer .link-widget ul li a {
  color: #fff;
  font-size: 16px;
}

.wpo-site-footer .link-widget ul li a:hover {
  color: #F2BC00;
}

.wpo-site-footer .contact-ft ul li {
  padding-bottom: 15px;
  position: relative;
  padding-left: 35px;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  text-transform: capitalize;
  color: #fff;
}

.wpo-site-footer .contact-ft ul li i {
  position: absolute;
  left: 0;
  top: 0;
  color: #fff;
}

.wpo-site-footer .contact-ft ul li .fi:before {
  font-size: 20px;
  margin-right: 15px;
}

.wpo-site-footer .wpo-lower-footer {
  text-align: left;
  padding: 25px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}

@media (max-width: 991px) {
  .wpo-site-footer .wpo-lower-footer {
    text-align: center;
  }
}

.wpo-site-footer .wpo-lower-footer ul li {
  color: rgba(188, 192, 197, 0.97);
}

.wpo-site-footer .wpo-lower-footer ul li a {
  color: #fff;
}

.wpo-site-footer .wpo-lower-footer .link ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

@media (max-width: 991px) {
  .wpo-site-footer .wpo-lower-footer .link ul {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    margin-top: 10px;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}

.wpo-site-footer .wpo-lower-footer .link ul li a {
  color: rgba(188, 192, 197, 0.97);
}

.wpo-site-footer .wpo-lower-footer .link ul li a:hover {
  color: #F2BC00;
}

.wpo-site-footer .wpo-lower-footer .link ul li + li {
  padding-left: 20px;
  position: relative;
}

@media (max-width: 375px) {
  .wpo-site-footer .wpo-lower-footer .link ul li + li {
    padding-left: 5px;
    margin-left: 5px;
  }
}

.sticky-header {
  width: 100%;
  position: fixed;
  left: 0;
  top: -200px;
  z-index: 9999;
  opacity: 0;
  -webkit-transition: all 0.7s;
  transition: all 0.7s;
}

.sticky-on {
  opacity: 1;
  top: 0;
}

/* 3.2 wpo-about-section */
.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: -200px;
  margin-right: 20px;
}

@media (max-width: 991px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-img-wrap,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap {
    margin-left: 0px;
    margin-right: 0;
  }
}

@media (max-width: 575px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-img-wrap,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img {
  margin-right: 30px;
  position: relative;
  z-index: 1;
  border-radius: 300px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img img,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img img,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img img,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img img,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img img {
  width: 100%;
  border-radius: 300px;
}

@media (max-width: 575px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img {
    margin-right: 0;
    margin-bottom: 20px;
  }
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img:before,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img:before,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img:before,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img:before,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img:before {
  position: absolute;
  left: 3%;
  top: 2%;
  width: 94%;
  height: 96%;
  content: "";
  border: 2px solid #F2BC00;
  border-radius: 300px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner {
  border-radius: 300px;
  position: relative;
  margin-bottom: 20px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner:before,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner:before,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner:before,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner:before,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner:before {
  position: absolute;
  left: 3%;
  top: 2%;
  width: 94%;
  height: 96%;
  content: "";
  border: 2px solid #fff;
  border-radius: 300px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner img,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner img,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner img,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner img,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .wpo-about-img-inner img {
  width: 100%;
  border-radius: 300px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience-wrap,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience-wrap,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience-wrap,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience-wrap,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience-wrap {
  padding: 10px;
  border: 1px dashed #15A280;
  border-radius: 10px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience {
  max-width: 320px;
  background: #15A280;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 30px 20px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  text-align: left;
  border-radius: 10px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience h3,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience h3,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience h3,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience h3,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience h3 {
  margin-bottom: 0;
  font-weight: 700;
  font-size: 26px;
  line-height: 33px;
  color: #fff;
}

@media (max-width: 767px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience h3,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience h3,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience h3,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience h3,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience h3 {
    font-size: 20px;
    line-height: 28px;
  }
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience p,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience p,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience p,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience p,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience p {
  margin-bottom: 0;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  color: #fff;
}

@media (max-width: 767px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience p,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience p,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience p,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience p,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience p {
    font-size: 15px;
  }
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon {
  margin-right: 10px;
  margin-left: 10px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon i,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon i,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon i,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon i,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon i {
  width: 60px;
  height: 60px;
  line-height: 60px;
  background: #fff;
  text-align: center;
  border-radius: 50%;
  margin-right: 10px;
  display: block;
  color: #15A280;
  font-size: 30px;
  z-index: 1;
  position: relative;
}

.wpo-about-section .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon i::after,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon i::after,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon i::after,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon i::after,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap .wpo-about-img-right .exprience .icon i::after {
  position: absolute;
  left: -8px;
  top: -8px;
  width: 76px;
  height: 76px;
  content: "";
  border: 1px solid #fff;
  z-index: -1;
  border-radius: 50%;
}

@media (max-width: 991px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-text,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-text,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-text,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-text,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-text {
    margin-top: 30px;
  }
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-section-title,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-section-title,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-section-title,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-section-title,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-section-title {
  text-align: left;
  margin: 0;
  margin-bottom: 30px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap {
  padding: 30px 0 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  border-top: 1px solid #D9D9D9;
  border-bottom: 1px solid #D9D9D9;
  margin: 30px 0;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  max-width: 566px;
}

@media (max-width: 991px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap {
    max-width: 100%;
  }
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-preferred-size: 50%;
      flex-basis: 50%;
  margin-bottom: 30px;
  padding-right: 20px;
}

@media (max-width: 1199px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item {
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item {
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
  }
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-icon .icon,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-icon .icon,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-icon .icon,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-icon .icon,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-icon .icon {
  width: 70px;
  height: 70px;
  line-height: 70px;
  background: #FAF5E6;
  text-align: center;
  border-radius: 50%;
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-icon .icon .fi,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-icon .icon .fi,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-icon .icon .fi,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-icon .icon .fi,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-icon .icon .fi {
  color: #D0A202;
  font-size: 35px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(2) .wpo-about-features-icon .icon,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(2) .wpo-about-features-icon .icon,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(2) .wpo-about-features-icon .icon,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(2) .wpo-about-features-icon .icon,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(2) .wpo-about-features-icon .icon {
  background: #DEF2ED;
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(2) .wpo-about-features-icon .icon .fi,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(2) .wpo-about-features-icon .icon .fi,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(2) .wpo-about-features-icon .icon .fi,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(2) .wpo-about-features-icon .icon .fi,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(2) .wpo-about-features-icon .icon .fi {
  color: #15A280;
  font-size: 35px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(3) .wpo-about-features-icon .icon,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(3) .wpo-about-features-icon .icon,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(3) .wpo-about-features-icon .icon,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(3) .wpo-about-features-icon .icon,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(3) .wpo-about-features-icon .icon {
  background: #EEEDFC;
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(3) .wpo-about-features-icon .icon .fi,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(3) .wpo-about-features-icon .icon .fi,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(3) .wpo-about-features-icon .icon .fi,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(3) .wpo-about-features-icon .icon .fi,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(3) .wpo-about-features-icon .icon .fi {
  color: #4540E1;
  font-size: 35px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(4) .wpo-about-features-icon .icon,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(4) .wpo-about-features-icon .icon,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(4) .wpo-about-features-icon .icon,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(4) .wpo-about-features-icon .icon,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(4) .wpo-about-features-icon .icon {
  background: #FFEDF1;
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(4) .wpo-about-features-icon .icon .fi,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(4) .wpo-about-features-icon .icon .fi,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(4) .wpo-about-features-icon .icon .fi,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(4) .wpo-about-features-icon .icon .fi,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item:nth-child(4) .wpo-about-features-icon .icon .fi {
  color: #F9406C;
  font-size: 35px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text {
  margin-left: 20px;
}

.wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text h5,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text h5,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text h5,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text h5,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text h5 {
  font-size: 22px;
  font-style: normal;
  font-weight: 500;
  color: #625F71;
}

@media (max-width: 1199px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text h5,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text h5,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text h5,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text h5,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-about-features-wrap .wpo-about-features-item .wpo-about-features-text h5 {
    font-size: 18px;
  }
}

.wpo-about-section .wpo-about-wrap .wpo-about-text h2,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text h2,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text h2,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text h2,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text h2 {
  text-align: left;
}

@media (max-width: 1199px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-text h2,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-text h2,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-text h2,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-text h2,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-text h2 {
    font-size: 35px;
    line-height: 45px;
  }
}

@media (max-width: 575px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-text h2,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-text h2,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-text h2,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-text h2,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-text h2 {
    font-size: 32px;
  }
}

.wpo-about-section .wpo-about-wrap .wpo-about-text p,
.wpo-about-section-s2 .wpo-about-wrap .wpo-about-text p,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-text p,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text p,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text p {
  overflow: hidden;
  font-weight: 400;
  font-size: 18px;
  line-height: 160%;
  max-width: 566px;
}

@media (max-width: 991px) {
  .wpo-about-section .wpo-about-wrap .wpo-about-text p,
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-text p,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-text p,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-text p,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-text p {
    max-width: 100%;
  }
}

/* 3.3 wpo-courses-section*/
.wpo-courses-section,
.wpo-courses-section-s2,
.wpo-courses-section-s4,
.wpo-courses-section-s5 {
  background: #FCF9EF;
  z-index: 1;
  position: relative;
}

.wpo-courses-section .wpo-courses-wrap .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .wpo-courses-item {
  padding: 40px 30px;
  background: #fff;
  border-radius: 10px;
  position: relative;
  z-index: 1;
  -webkit-transition: all .3s;
  transition: all .3s;
}

@media (max-width: 575px) {
  .wpo-courses-section .wpo-courses-wrap .wpo-courses-item,
  .wpo-courses-section-s2 .wpo-courses-wrap .wpo-courses-item,
  .wpo-courses-section-s4 .wpo-courses-wrap .wpo-courses-item,
  .wpo-courses-section-s5 .wpo-courses-wrap .wpo-courses-item {
    padding: 30px 25px;
  }
}

.wpo-courses-section .wpo-courses-wrap .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text .courses-icon {
  margin-bottom: 15px;
  width: 80px;
  height: 80px;
  line-height: 80px;
  background: #F2BC00;
  text-align: center;
  border-radius: 50%;
  color: #fff;
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-courses-section .wpo-courses-wrap .wpo-courses-item .wpo-courses-text .courses-icon .fi:before,
.wpo-courses-section-s2 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text .courses-icon .fi:before,
.wpo-courses-section-s4 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text .courses-icon .fi:before,
.wpo-courses-section-s5 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text .courses-icon .fi:before {
  font-size: 40px;
}

.wpo-courses-section .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2,
.wpo-courses-section-s2 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2,
.wpo-courses-section-s4 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2,
.wpo-courses-section-s5 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 {
  color: #1A1729;
  font-size: 25px;
  font-style: normal;
  font-weight: 600;
  margin-bottom: 15px;
  -webkit-transition: all .3s;
  transition: all .3s;
}

@media (max-width: 1399px) {
  .wpo-courses-section .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2,
  .wpo-courses-section-s2 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2,
  .wpo-courses-section-s4 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2,
  .wpo-courses-section-s5 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 {
    font-size: 25px;
  }
}

@media (max-width: 575px) {
  .wpo-courses-section .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2,
  .wpo-courses-section-s2 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2,
  .wpo-courses-section-s4 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2,
  .wpo-courses-section-s5 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 {
    font-size: 25px;
  }
}

.wpo-courses-section .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 a,
.wpo-courses-section-s2 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 a,
.wpo-courses-section-s4 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 a,
.wpo-courses-section-s5 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 a {
  color: #1A1729;
}

.wpo-courses-section .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 a:hover,
.wpo-courses-section-s2 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 a:hover,
.wpo-courses-section-s4 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 a:hover,
.wpo-courses-section-s5 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 a:hover {
  color: #4540E1;
}

.wpo-courses-section .wpo-courses-wrap .wpo-courses-item .wpo-courses-text p,
.wpo-courses-section-s2 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text p,
.wpo-courses-section-s4 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text p,
.wpo-courses-section-s5 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text p {
  color: #625F71;
  margin-bottom: 0;
  font-weight: 400;
  font-size: 18px;
  line-height: 27px;
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-courses-section .wpo-courses-wrap .grid.s1 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.s1 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.s1 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.s1 .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #F2BC00;
}

.wpo-courses-section .wpo-courses-wrap .grid.s2 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.s2 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.s2 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.s2 .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #15A280;
}

.wpo-courses-section .wpo-courses-wrap .grid.s3 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.s3 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.s3 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.s3 .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #4540E1;
}

.wpo-courses-section .wpo-courses-wrap .grid.s4 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.s4 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.s4 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.s4 .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #F9406C;
}

.wpo-courses-section .wpo-courses-wrap .grid.s5 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.s5 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.s5 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.s5 .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #0467FD;
}

.wpo-courses-section .wpo-courses-wrap .grid.s6 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.s6 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.s6 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.s6 .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #FF4C27;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover .wpo-courses-item, .wpo-courses-section .wpo-courses-wrap .grid.active .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active .wpo-courses-item {
  background: #FF4C27;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text h2, .wpo-courses-section .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text h2,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text h2,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text h2,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text h2,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text h2,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text h2,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text h2 {
  color: #fff;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text h2 a, .wpo-courses-section .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text h2 a,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text h2 a,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text h2 a,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text h2 a,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text h2 a,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text h2 a,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text h2 a {
  color: #fff;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text h2 a:hover, .wpo-courses-section .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text h2 a:hover,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text h2 a:hover,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text h2 a:hover,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text h2 a:hover,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text h2 a:hover,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text h2 a:hover,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text h2 a:hover {
  color: #fff;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text p, .wpo-courses-section .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text p,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text p,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text p,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text p,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text p,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text p,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text p {
  color: #fff;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text .courses-icon, .wpo-courses-section .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #fff;
  color: #FF4C27;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover.s1 .wpo-courses-item, .wpo-courses-section .wpo-courses-wrap .grid.active.s1 .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover.s1 .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active.s1 .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover.s1 .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active.s1 .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover.s1 .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active.s1 .wpo-courses-item {
  background: #F2BC00;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover.s1 .wpo-courses-item .wpo-courses-text .courses-icon, .wpo-courses-section .wpo-courses-wrap .grid.active.s1 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover.s1 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active.s1 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover.s1 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active.s1 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover.s1 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active.s1 .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #fff;
  color: #F2BC00;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover.s2 .wpo-courses-item, .wpo-courses-section .wpo-courses-wrap .grid.active.s2 .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover.s2 .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active.s2 .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover.s2 .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active.s2 .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover.s2 .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active.s2 .wpo-courses-item {
  background: #15A280;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover.s2 .wpo-courses-item .wpo-courses-text .courses-icon, .wpo-courses-section .wpo-courses-wrap .grid.active.s2 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover.s2 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active.s2 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover.s2 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active.s2 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover.s2 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active.s2 .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #fff;
  color: #15A280;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover.s3 .wpo-courses-item, .wpo-courses-section .wpo-courses-wrap .grid.active.s3 .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover.s3 .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active.s3 .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover.s3 .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active.s3 .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover.s3 .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active.s3 .wpo-courses-item {
  background: #4540E1;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover.s3 .wpo-courses-item .wpo-courses-text .courses-icon, .wpo-courses-section .wpo-courses-wrap .grid.active.s3 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover.s3 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active.s3 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover.s3 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active.s3 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover.s3 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active.s3 .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #fff;
  color: #4540E1;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover.s4 .wpo-courses-item, .wpo-courses-section .wpo-courses-wrap .grid.active.s4 .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover.s4 .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active.s4 .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover.s4 .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active.s4 .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover.s4 .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active.s4 .wpo-courses-item {
  background: #F9406C;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover.s4 .wpo-courses-item .wpo-courses-text .courses-icon, .wpo-courses-section .wpo-courses-wrap .grid.active.s4 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover.s4 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active.s4 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover.s4 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active.s4 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover.s4 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active.s4 .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #fff;
  color: #F9406C;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover.s5 .wpo-courses-item, .wpo-courses-section .wpo-courses-wrap .grid.active.s5 .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover.s5 .wpo-courses-item,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active.s5 .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover.s5 .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active.s5 .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover.s5 .wpo-courses-item,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active.s5 .wpo-courses-item {
  background: #0467FD;
}

.wpo-courses-section .wpo-courses-wrap .grid:hover.s5 .wpo-courses-item .wpo-courses-text .courses-icon, .wpo-courses-section .wpo-courses-wrap .grid.active.s5 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid:hover.s5 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s2 .wpo-courses-wrap .grid.active.s5 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid:hover.s5 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .grid.active.s5 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid:hover.s5 .wpo-courses-item .wpo-courses-text .courses-icon,
.wpo-courses-section-s5 .wpo-courses-wrap .grid.active.s5 .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #fff;
  color: #0467FD;
}

.wpo-courses-section .wpo-courses-wrap .owl-stage-outer,
.wpo-courses-section-s2 .wpo-courses-wrap .owl-stage-outer,
.wpo-courses-section-s4 .wpo-courses-wrap .owl-stage-outer,
.wpo-courses-section-s5 .wpo-courses-wrap .owl-stage-outer {
  margin: -14px;
}

.wpo-courses-section .wpo-courses-wrap .owl-stage,
.wpo-courses-section-s2 .wpo-courses-wrap .owl-stage,
.wpo-courses-section-s4 .wpo-courses-wrap .owl-stage,
.wpo-courses-section-s5 .wpo-courses-wrap .owl-stage {
  margin: 14px;
}

.wpo-courses-section .wpo-courses-wrap .owl-controls,
.wpo-courses-section-s2 .wpo-courses-wrap .owl-controls,
.wpo-courses-section-s4 .wpo-courses-wrap .owl-controls,
.wpo-courses-section-s5 .wpo-courses-wrap .owl-controls {
  width: 100%;
  margin: 0;
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.wpo-courses-section .wpo-courses-wrap .owl-nav [class*=owl-],
.wpo-courses-section-s2 .wpo-courses-wrap .owl-nav [class*=owl-],
.wpo-courses-section-s4 .wpo-courses-wrap .owl-nav [class*=owl-],
.wpo-courses-section-s5 .wpo-courses-wrap .owl-nav [class*=owl-] {
  padding: 0;
  margin: 0;
  color: #4540E1;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border: 0;
  background: none;
}

.wpo-courses-section .wpo-courses-wrap .owl-nav .owl-prev,
.wpo-courses-section .wpo-courses-wrap .owl-nav .owl-next,
.wpo-courses-section-s2 .wpo-courses-wrap .owl-nav .owl-prev,
.wpo-courses-section-s2 .wpo-courses-wrap .owl-nav .owl-next,
.wpo-courses-section-s4 .wpo-courses-wrap .owl-nav .owl-prev,
.wpo-courses-section-s4 .wpo-courses-wrap .owl-nav .owl-next,
.wpo-courses-section-s5 .wpo-courses-wrap .owl-nav .owl-prev,
.wpo-courses-section-s5 .wpo-courses-wrap .owl-nav .owl-next {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background: #fff;
  width: 50px;
  height: 50px;
  line-height: 48px;
  -webkit-transition: all .3s;
  transition: all .3s;
  border: 1px solid #DDDDDD;
  border-radius: 50%;
  font-size: 20px;
}

.wpo-courses-section .wpo-courses-wrap .owl-nav .owl-prev:hover,
.wpo-courses-section .wpo-courses-wrap .owl-nav .owl-next:hover,
.wpo-courses-section-s2 .wpo-courses-wrap .owl-nav .owl-prev:hover,
.wpo-courses-section-s2 .wpo-courses-wrap .owl-nav .owl-next:hover,
.wpo-courses-section-s4 .wpo-courses-wrap .owl-nav .owl-prev:hover,
.wpo-courses-section-s4 .wpo-courses-wrap .owl-nav .owl-next:hover,
.wpo-courses-section-s5 .wpo-courses-wrap .owl-nav .owl-prev:hover,
.wpo-courses-section-s5 .wpo-courses-wrap .owl-nav .owl-next:hover {
  background: #4540E1;
  border-color: #4540E1;
  color: #fff;
}

@media (max-width: 767px) {
  .wpo-courses-section .wpo-courses-wrap .owl-nav .owl-prev,
  .wpo-courses-section .wpo-courses-wrap .owl-nav .owl-next,
  .wpo-courses-section-s2 .wpo-courses-wrap .owl-nav .owl-prev,
  .wpo-courses-section-s2 .wpo-courses-wrap .owl-nav .owl-next,
  .wpo-courses-section-s4 .wpo-courses-wrap .owl-nav .owl-prev,
  .wpo-courses-section-s4 .wpo-courses-wrap .owl-nav .owl-next,
  .wpo-courses-section-s5 .wpo-courses-wrap .owl-nav .owl-prev,
  .wpo-courses-section-s5 .wpo-courses-wrap .owl-nav .owl-next {
    display: none;
  }
}

.wpo-courses-section .wpo-courses-wrap .owl-nav .owl-prev,
.wpo-courses-section-s2 .wpo-courses-wrap .owl-nav .owl-prev,
.wpo-courses-section-s4 .wpo-courses-wrap .owl-nav .owl-prev,
.wpo-courses-section-s5 .wpo-courses-wrap .owl-nav .owl-prev {
  left: -70px;
}

@media (max-width: 1440px) {
  .wpo-courses-section .wpo-courses-wrap .owl-nav .owl-prev,
  .wpo-courses-section-s2 .wpo-courses-wrap .owl-nav .owl-prev,
  .wpo-courses-section-s4 .wpo-courses-wrap .owl-nav .owl-prev,
  .wpo-courses-section-s5 .wpo-courses-wrap .owl-nav .owl-prev {
    left: 20px;
    opacity: 0;
    visibility: hidden;
  }
}

.wpo-courses-section .wpo-courses-wrap .owl-nav .owl-next,
.wpo-courses-section-s2 .wpo-courses-wrap .owl-nav .owl-next,
.wpo-courses-section-s4 .wpo-courses-wrap .owl-nav .owl-next,
.wpo-courses-section-s5 .wpo-courses-wrap .owl-nav .owl-next {
  right: -70px;
}

@media (max-width: 1440px) {
  .wpo-courses-section .wpo-courses-wrap .owl-nav .owl-next,
  .wpo-courses-section-s2 .wpo-courses-wrap .owl-nav .owl-next,
  .wpo-courses-section-s4 .wpo-courses-wrap .owl-nav .owl-next,
  .wpo-courses-section-s5 .wpo-courses-wrap .owl-nav .owl-next {
    right: 20px;
    opacity: 0;
    visibility: hidden;
  }
}

.wpo-courses-section .wpo-courses-wrap .owl-dots,
.wpo-courses-section-s2 .wpo-courses-wrap .owl-dots,
.wpo-courses-section-s4 .wpo-courses-wrap .owl-dots,
.wpo-courses-section-s5 .wpo-courses-wrap .owl-dots {
  text-align: center;
  bottom: -20px;
  position: relative;
}

.wpo-courses-section .wpo-courses-wrap .owl-dots button,
.wpo-courses-section-s2 .wpo-courses-wrap .owl-dots button,
.wpo-courses-section-s4 .wpo-courses-wrap .owl-dots button,
.wpo-courses-section-s5 .wpo-courses-wrap .owl-dots button {
  width: 10px;
  height: 12px;
  border: 0;
  background: #d4e6f5;
  margin-right: 10px;
  border-radius: 50%;
}

.wpo-courses-section .wpo-courses-wrap .owl-dots .owl-dot.active,
.wpo-courses-section-s2 .wpo-courses-wrap .owl-dots .owl-dot.active,
.wpo-courses-section-s4 .wpo-courses-wrap .owl-dots .owl-dot.active,
.wpo-courses-section-s5 .wpo-courses-wrap .owl-dots .owl-dot.active {
  background-color: #4540E1;
}

.wpo-courses-section .wpo-courses-wrap:hover .owl-nav .owl-prev,
.wpo-courses-section .wpo-courses-wrap:hover .owl-nav .owl-next,
.wpo-courses-section-s2 .wpo-courses-wrap:hover .owl-nav .owl-prev,
.wpo-courses-section-s2 .wpo-courses-wrap:hover .owl-nav .owl-next,
.wpo-courses-section-s4 .wpo-courses-wrap:hover .owl-nav .owl-prev,
.wpo-courses-section-s4 .wpo-courses-wrap:hover .owl-nav .owl-next,
.wpo-courses-section-s5 .wpo-courses-wrap:hover .owl-nav .owl-prev,
.wpo-courses-section-s5 .wpo-courses-wrap:hover .owl-nav .owl-next {
  opacity: 1;
  visibility: visible;
}

.wpo-courses-section .shape-1,
.wpo-courses-section-s2 .shape-1,
.wpo-courses-section-s4 .shape-1,
.wpo-courses-section-s5 .shape-1 {
  position: absolute;
  left: 15%;
  top: 20%;
  z-index: -1;
  -webkit-animation: bounce 3s linear infinite;
          animation: bounce 3s linear infinite;
}

@media (max-width: 991px) {
  .wpo-courses-section .shape-1,
  .wpo-courses-section-s2 .shape-1,
  .wpo-courses-section-s4 .shape-1,
  .wpo-courses-section-s5 .shape-1 {
    top: 8%;
  }
}

.wpo-courses-section .shape-2,
.wpo-courses-section-s2 .shape-2,
.wpo-courses-section-s4 .shape-2,
.wpo-courses-section-s5 .shape-2 {
  position: absolute;
  left: 50px;
  bottom: 50px;
  z-index: -1;
  -webkit-animation: zoom-in-zoom-out 2s ease infinite;
          animation: zoom-in-zoom-out 2s ease infinite;
}

.wpo-courses-section .shape-3,
.wpo-courses-section-s2 .shape-3,
.wpo-courses-section-s4 .shape-3,
.wpo-courses-section-s5 .shape-3 {
  position: absolute;
  right: -30px;
  top: 40px;
  z-index: -1;
  -webkit-animation: bounceX 3s linear infinite;
          animation: bounceX 3s linear infinite;
}

.wpo-courses-section .shape-4,
.wpo-courses-section-s2 .shape-4,
.wpo-courses-section-s4 .shape-4,
.wpo-courses-section-s5 .shape-4 {
  position: absolute;
  right: 50px;
  bottom: 40px;
  z-index: -1;
  -webkit-animation: circle 20s linear infinite;
          animation: circle 20s linear infinite;
}

/* 3.4 wpo-popular-area */
.wpo-popular-area,
.wpo-popular-area-s2,
.wpo-popular-area-s3,
.wpo-popular-area-s4 {
  position: relative;
  z-index: 1;
}

.wpo-popular-area .wpo-popular-item,
.wpo-popular-area-s2 .wpo-popular-item,
.wpo-popular-area-s3 .wpo-popular-item,
.wpo-popular-area-s4 .wpo-popular-item {
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
  border-radius: 10px;
  background: #fff;
  margin-bottom: 30px;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-img,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-img,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-img,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-img {
  position: relative;
  padding: 20px 20px 0px;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-img img,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-img img,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-img img,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-img img {
  border-radius: 10px;
  width: 100%;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-img .thumb,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-img .thumb,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-img .thumb,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-img .thumb {
  width: 80px;
  height: 80px;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  right: 30px;
  bottom: -40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-img .thumb span,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-img .thumb span,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-img .thumb span,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-img .thumb span {
  display: block;
  width: 66px;
  height: 66px;
  margin: 0 auto;
  background: #4540E1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  text-align: center;
  border-radius: 50%;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  color: #fff;
}

@media (max-width: 1199px) {
  .wpo-popular-area .wpo-popular-item .wpo-popular-content,
  .wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content,
  .wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content,
  .wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content {
    padding-top: 20px;
  }
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content h2,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content h2,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content h2,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content h2 {
  font-size: 26px;
  font-weight: 700;
  line-height: 40px;
  margin-top: 0;
  margin-bottom: 25px;
  padding: 0 20px;
}

@media (max-width: 1199px) {
  .wpo-popular-area .wpo-popular-item .wpo-popular-content h2,
  .wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content h2,
  .wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content h2,
  .wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content h2 {
    font-size: 22px;
    line-height: 35px;
  }
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content h2 a,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content h2 a,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content h2 a,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content h2 a {
  color: #1A1729;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content h2 a:hover,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content h2 a:hover,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content h2 a:hover,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content h2 a:hover {
  color: #4540E1;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-top,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top {
  padding-top: 22px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 20px;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-top p,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top p,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top p,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top p {
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 30px;
  margin-bottom: 5px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (max-width: 1399px) {
  .wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-top p,
  .wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top p,
  .wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top p,
  .wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top p {
    font-size: 16px;
  }
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-top p i,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top p i,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top p i,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top p i {
  margin-right: 10px;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul + ul,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul + ul,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul + ul,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul + ul {
  padding-left: 30px;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li {
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 30px;
  color: #1A1729;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (max-width: 1199px) {
  .wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li,
  .wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li,
  .wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li,
  .wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li {
    font-size: 16px;
  }
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li img,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li img,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li img,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li img {
  border-radius: 50%;
  max-width: 40px;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li + li,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li + li,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li + li,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li + li {
  margin-left: 10px;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li a,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li a,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li a,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li a {
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  color: #1A1729;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li i,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li i,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li i,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-top ul li i {
  color: #F2BC00;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom {
  padding: 15px 20px;
  border-top: 1px solid #D9D9D9;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom ul,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom ul,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom ul,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom ul {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom ul li,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom ul li,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom ul li,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom ul li {
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-popular-area .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom ul li i,
.wpo-popular-area-s2 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom ul li i,
.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom ul li i,
.wpo-popular-area-s4 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom ul li i {
  margin-right: 10px;
}

.wpo-popular-area .col:nth-child(2) .wpo-popular-item .wpo-popular-img .thumb span,
.wpo-popular-area-s2 .col:nth-child(2) .wpo-popular-item .wpo-popular-img .thumb span,
.wpo-popular-area-s3 .col:nth-child(2) .wpo-popular-item .wpo-popular-img .thumb span,
.wpo-popular-area-s4 .col:nth-child(2) .wpo-popular-item .wpo-popular-img .thumb span {
  background-color: #15A280;
}

.wpo-popular-area .col:nth-child(3) .wpo-popular-item .wpo-popular-img .thumb span,
.wpo-popular-area-s2 .col:nth-child(3) .wpo-popular-item .wpo-popular-img .thumb span,
.wpo-popular-area-s3 .col:nth-child(3) .wpo-popular-item .wpo-popular-img .thumb span,
.wpo-popular-area-s4 .col:nth-child(3) .wpo-popular-item .wpo-popular-img .thumb span {
  background-color: #F2BC00;
}

@media (max-width: 1200px) {
  .wpo-popular-area .wpo-popular-area,
  .wpo-popular-area-s2 .wpo-popular-area,
  .wpo-popular-area-s3 .wpo-popular-area,
  .wpo-popular-area-s4 .wpo-popular-area {
    padding-bottom: 90px;
  }
  .wpo-popular-area .wpo-popular-content h2,
  .wpo-popular-area-s2 .wpo-popular-content h2,
  .wpo-popular-area-s3 .wpo-popular-content h2,
  .wpo-popular-area-s4 .wpo-popular-content h2 {
    font-size: 20px;
  }
}

@media (max-width: 767px) {
  .wpo-popular-area .wpo-popular-content h2,
  .wpo-popular-area-s2 .wpo-popular-content h2,
  .wpo-popular-area-s3 .wpo-popular-content h2,
  .wpo-popular-area-s4 .wpo-popular-content h2 {
    font-size: 20px;
  }
}

/* 3.5 wpo-testimonial-section */
.wpo-testimonial-section,
.wpo-testimonial-section-s2 {
  position: relative;
  background: #FCF9EF;
  z-index: 1;
}

.wpo-testimonial-section .wpo-testimonial-wrap,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap {
  position: relative;
  z-index: 91;
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item {
  border-radius: 10px;
  background: #fff;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
  padding: 40px 30px;
}

@media (max-width: 991px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item {
    padding: 20px 15px;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text ul,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text ul {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 20px;
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text ul li,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text ul li {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  color: #625F71;
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text ul li + li,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text ul li + li {
  margin-left: 5px;
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text ul li:last-child,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text ul li:last-child {
  margin-left: 20px;
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text ul li i,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text ul li i {
  color: #F2BC00;
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text ul li i.off,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text ul li i.off {
  color: #C9C9C9;
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .fi.quote,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .fi.quote {
  font-size: 50px;
  color: #F2BC00;
  opacity: .2;
  position: absolute;
  right: 20px;
  top: 10px;
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text p,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text p {
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 35px;
  color: #1A1729;
  margin-bottom: 0;
}

@media (max-width: 1399px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text p,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text p {
    font-size: 18px;
  }
}

@media (max-width: 991px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text p,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text p {
    font-size: 17px;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-bottom: 30px;
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm .wpo-testimonial-text-btm-img,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm .wpo-testimonial-text-btm-img {
  padding: 6px;
  background: #fff;
  border-radius: 50%;
  -webkit-box-shadow: 0px 2px 10px rgba(0, 18, 52, 0.15);
          box-shadow: 0px 2px 10px rgba(0, 18, 52, 0.15);
  margin-right: 18px;
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm .wpo-testimonial-text-btm-img img,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm .wpo-testimonial-text-btm-img img {
  border-radius: 50%;
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm h3,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm h3 {
  font-style: normal;
  font-weight: 450;
  font-size: 25px;
  line-height: 32px;
}

@media (max-width: 991px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm h3,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm h3 {
    font-size: 20px;
  }
}

@media (max-width: 767px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm h3,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm h3 {
    font-size: 18px;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm h3 span,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .wpo-testimonial-text-btm h3 span {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #FFEBEB;
  display: block;
  margin-left: 10px;
  position: relative;
  top: 3px;
}

.wpo-testimonial-section .wpo-testimonial-wrap .slick-dots,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .slick-dots {
  text-align: left;
  bottom: -60px;
}

@media (max-width: 991px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .slick-dots,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .slick-dots {
    text-align: center;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .slick-dots li,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .slick-dots li {
  width: unset;
  height: unset;
}

.wpo-testimonial-section .wpo-testimonial-wrap .slick-dots li button,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .slick-dots li button {
  background: #D9D9D9;
  margin-right: 5px;
  width: 10px;
  height: 10px;
  padding: 0;
  border-radius: 50%;
}

.wpo-testimonial-section .wpo-testimonial-wrap .slick-dots li button::before,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .slick-dots li button::before {
  display: none;
}

.wpo-testimonial-section .wpo-testimonial-wrap .slick-dots li.slick-active button,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .slick-dots li.slick-active button {
  background: #4540E1;
}

.wpo-testimonial-section .wpo-testimonial-wrap .slick-dots li.slick-active button::after,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .slick-dots li.slick-active button::after {
  position: absolute;
  left: -3px;
  top: -3px;
  width: 16px;
  height: 16px;
  content: "";
  border: 1px solid #4540E1;
  border-radius: 50%;
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-stage-outer,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-stage-outer {
  margin: -14px;
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-stage,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-stage {
  margin: 14px;
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-controls,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-controls {
  width: 100%;
  margin: 0;
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-nav [class*=owl-],
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-] {
  padding: 0;
  margin: 0;
  color: #4540E1;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border: 0;
  background: none;
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-nav .owl-prev,
.wpo-testimonial-section .wpo-testimonial-wrap .owl-nav .owl-next,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav .owl-prev,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav .owl-next {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background: #fff;
  width: 50px;
  height: 50px;
  line-height: 48px;
  -webkit-transition: all .3s;
  transition: all .3s;
  border: 1px solid #DDDDDD;
  border-radius: 50%;
  font-size: 20px;
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-nav .owl-prev:hover,
.wpo-testimonial-section .wpo-testimonial-wrap .owl-nav .owl-next:hover,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav .owl-prev:hover,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav .owl-next:hover {
  background: #4540E1;
  border-color: #4540E1;
  color: #fff;
}

@media (max-width: 767px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .owl-nav .owl-prev,
  .wpo-testimonial-section .wpo-testimonial-wrap .owl-nav .owl-next,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav .owl-prev,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav .owl-next {
    display: none;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-nav .owl-prev,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav .owl-prev {
  left: -70px;
}

@media (max-width: 1440px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .owl-nav .owl-prev,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav .owl-prev {
    left: 20px;
    opacity: 0;
    visibility: hidden;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-nav .owl-next,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav .owl-next {
  right: -70px;
}

@media (max-width: 1440px) {
  .wpo-testimonial-section .wpo-testimonial-wrap .owl-nav .owl-next,
  .wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav .owl-next {
    right: 20px;
    opacity: 0;
    visibility: hidden;
  }
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-dots,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-dots {
  text-align: center;
  bottom: -20px;
  position: relative;
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-dots button,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-dots button {
  width: 10px;
  height: 12px;
  border: 0;
  background: #d4e6f5;
  margin-right: 10px;
  border-radius: 50%;
}

.wpo-testimonial-section .wpo-testimonial-wrap .owl-dots .owl-dot.active,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-dots .owl-dot.active {
  background-color: #4540E1;
}

.wpo-testimonial-section .wpo-testimonial-wrap:hover .owl-nav .owl-prev,
.wpo-testimonial-section .wpo-testimonial-wrap:hover .owl-nav .owl-next,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap:hover .owl-nav .owl-prev,
.wpo-testimonial-section-s2 .wpo-testimonial-wrap:hover .owl-nav .owl-next {
  opacity: 1;
  visibility: visible;
}

.wpo-testimonial-section .shape-1,
.wpo-testimonial-section-s2 .shape-1 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -11;
}

@media (max-width: 1199px) {
  .wpo-testimonial-section .shape-1,
  .wpo-testimonial-section-s2 .shape-1 {
    max-width: 50%;
  }
}

.wpo-testimonial-section .shape-2,
.wpo-testimonial-section-s2 .shape-2 {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -11;
}

@media (max-width: 1199px) {
  .wpo-testimonial-section .shape-2,
  .wpo-testimonial-section-s2 .shape-2 {
    max-width: 50%;
  }
}

/* 3.6 wpo-team-section */
.wpo-team-section {
  position: relative;
  z-index: 1;
  padding-bottom: 90px;
}

.wpo-team-section.s2 {
  background: #FCF9EF;
}

@media (max-width: 991px) {
  .wpo-team-section {
    padding-bottom: 60px;
  }
}

@media (max-width: 767px) {
  .wpo-team-section {
    padding-bottom: 50px;
  }
}

.wpo-team-section.pb-big {
  padding-bottom: 230px;
}

@media (max-width: 1199px) {
  .wpo-team-section.pb-big {
    padding-bottom: 90px;
  }
}

@media (max-width: 991px) {
  .wpo-team-section.pb-big {
    padding-bottom: 60px;
  }
}

.wpo-team-section .wpo-team-wrap .wpo-team-item {
  text-align: center;
  border-radius: 50%;
  position: relative;
  -webkit-transition: all .3s;
  transition: all .3s;
  max-width: 295px;
  margin: 0 auto;
  margin-bottom: 30px;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img {
  border-radius: 150px;
  position: relative;
  z-index: 1;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img:before {
  position: absolute;
  left: 4%;
  top: 3%;
  width: 92%;
  height: 94%;
  content: "";
  border: 1px solid #fff;
  z-index: 1;
  border-radius: 135px;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img .wpo-team-img-box {
  border-radius: 150px;
  overflow: hidden;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img .wpo-team-img-box img {
  width: 100%;
  border-radius: 150px;
  -webkit-transform: scale(1);
          transform: scale(1);
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img .wpo-team-img-box ul {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  bottom: 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  list-style: none;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all .3s;
  transition: all .3s;
  z-index: 11;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img .wpo-team-img-box ul li {
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img .wpo-team-img-box ul li + li {
  margin-left: 10px;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img .wpo-team-img-box ul li:nth-child(1) {
  margin-top: -10px;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img .wpo-team-img-box ul li:nth-child(2) {
  margin-top: -20px;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img .wpo-team-img-box ul li:nth-child(3) {
  margin-top: -30px;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img .wpo-team-img-box ul li a {
  display: block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  background: #15A280;
  border-radius: 50%;
  color: #fff;
  font-size: 14px;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img::after {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: #fff;
  z-index: -1;
  border-radius: 50%;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-img img {
  width: 100%;
  border-radius: 50%;
  -webkit-transform: scale(1);
          transform: scale(1);
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-text {
  padding-top: 15px;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-text span {
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  color: #F2BC00;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-text h2 {
  font-size: 28px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-align: center;
  color: #1A1729;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-text h2 a {
  color: #1A1729;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-text h2 a:hover {
  color: #4540E1;
}

@media (max-width: 1200px) {
  .wpo-team-section .wpo-team-wrap .wpo-team-item .wpo-team-text h2 {
    font-size: 25px;
  }
}

.wpo-team-section .wpo-team-wrap .wpo-team-item:hover .wpo-team-img .wpo-team-img-box img, .wpo-team-section .wpo-team-wrap .wpo-team-item.active .wpo-team-img .wpo-team-img-box img {
  -webkit-transform: scale(1.2);
          transform: scale(1.2);
}

.wpo-team-section .wpo-team-wrap .wpo-team-item:hover .wpo-team-img .wpo-team-img-box ul, .wpo-team-section .wpo-team-wrap .wpo-team-item.active .wpo-team-img .wpo-team-img-box ul {
  opacity: 1;
  visibility: visible;
}

.wpo-team-section .wpo-team-wrap .wpo-team-item:hover .wpo-team-img .wpo-team-img-box ul li, .wpo-team-section .wpo-team-wrap .wpo-team-item.active .wpo-team-img .wpo-team-img-box ul li {
  margin-top: 0;
}

.wpo-team-section .shape-1 {
  position: absolute;
  left: 15%;
  top: 20%;
  z-index: -1;
  -webkit-animation: bounce 3s linear infinite;
          animation: bounce 3s linear infinite;
}

@media (max-width: 991px) {
  .wpo-team-section .shape-1 {
    top: 8%;
  }
}

.wpo-team-section .shape-2 {
  position: absolute;
  left: 50px;
  bottom: 50px;
  z-index: -1;
  -webkit-animation: zoom-in-zoom-out 2s ease infinite;
          animation: zoom-in-zoom-out 2s ease infinite;
}

.wpo-team-section .shape-3 {
  position: absolute;
  right: -30px;
  top: 40px;
  z-index: -1;
  -webkit-animation: bounceX 3s linear infinite;
          animation: bounceX 3s linear infinite;
}

.wpo-team-section .shape-4 {
  position: absolute;
  right: 50px;
  bottom: 15%;
  z-index: -1;
  -webkit-animation: circle 20s linear infinite;
          animation: circle 20s linear infinite;
}

/* 3.7 wpo-choose-section */
.wpo-choose-section,
.wpo-choose-section-s2 {
  background: #FCF9EF;
  text-align: center;
  position: relative;
  z-index: 1;
}

@media (max-width: 991px) {
  .wpo-choose-section,
  .wpo-choose-section-s2 {
    padding-bottom: 80px;
  }
}

@media (max-width: 1199px) {
  .wpo-choose-section .row,
  .wpo-choose-section-s2 .row {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

.wpo-choose-section .wpo-choose-wrap,
.wpo-choose-section-s2 .wpo-choose-wrap {
  padding: 100px 0 60px;
  padding-right: 30px;
}

@media (max-width: 991px) {
  .wpo-choose-section .wpo-choose-wrap,
  .wpo-choose-section-s2 .wpo-choose-wrap {
    padding-right: 0px;
    padding: 80px 0 0;
  }
}

@media (max-width: 767px) {
  .wpo-choose-section .wpo-choose-wrap,
  .wpo-choose-section-s2 .wpo-choose-wrap {
    padding: 70px 0 0;
  }
}

.wpo-choose-section .wpo-choose-wrap .wpo-section-title-s2,
.wpo-choose-section-s2 .wpo-choose-wrap .wpo-section-title-s2 {
  text-align: left;
  margin-bottom: 30px;
}

@media (max-width: 575px) {
  .wpo-choose-section .wpo-choose-wrap .wpo-section-title-s2,
  .wpo-choose-section-s2 .wpo-choose-wrap .wpo-section-title-s2 {
    text-align: center;
  }
}

.wpo-choose-section .wpo-choose-wrap .grid,
.wpo-choose-section-s2 .wpo-choose-wrap .grid {
  width: 50%;
  float: left;
  position: relative;
  text-align: left;
  margin-bottom: 40px;
  padding-right: 20px;
}

@media (max-width: 767px) {
  .wpo-choose-section .wpo-choose-wrap .grid,
  .wpo-choose-section-s2 .wpo-choose-wrap .grid {
    width: 50%;
  }
}

@media (max-width: 575px) {
  .wpo-choose-section .wpo-choose-wrap .grid,
  .wpo-choose-section-s2 .wpo-choose-wrap .grid {
    width: 100%;
    text-align: center;
  }
}

.wpo-choose-section .wpo-choose-wrap .grid .icon,
.wpo-choose-section-s2 .wpo-choose-wrap .grid .icon {
  width: 70px;
  height: 70px;
  line-height: 70px;
  background: #F2BC00;
  border-radius: 50%;
  text-align: center;
  font-size: 35px;
  color: #fff;
  margin-bottom: 20px;
}

@media (max-width: 575px) {
  .wpo-choose-section .wpo-choose-wrap .grid .icon,
  .wpo-choose-section-s2 .wpo-choose-wrap .grid .icon {
    margin: 0 auto;
    margin-bottom: 20px;
  }
}

.wpo-choose-section .wpo-choose-wrap .grid:nth-child(2) .icon,
.wpo-choose-section-s2 .wpo-choose-wrap .grid:nth-child(2) .icon {
  background-color: #15A280;
}

.wpo-choose-section .wpo-choose-wrap .grid:nth-child(3) .icon,
.wpo-choose-section-s2 .wpo-choose-wrap .grid:nth-child(3) .icon {
  background-color: #0467FD;
}

.wpo-choose-section .wpo-choose-wrap .grid:nth-child(4) .icon,
.wpo-choose-section-s2 .wpo-choose-wrap .grid:nth-child(4) .icon {
  background-color: #F9406C;
}

.wpo-choose-section .wpo-choose-wrap .grid h3,
.wpo-choose-section-s2 .wpo-choose-wrap .grid h3 {
  font-size: 25px;
  font-style: normal;
  font-weight: 700;
  line-height: 40px;
  color: #1A1729;
  margin: 0 0 0.3em;
}

@media (max-width: 1199px) {
  .wpo-choose-section .wpo-choose-wrap .grid h3,
  .wpo-choose-section-s2 .wpo-choose-wrap .grid h3 {
    font-size: 20px;
    font-size: 1.33333rem;
  }
}

.wpo-choose-section .wpo-choose-wrap .grid h3 span,
.wpo-choose-section-s2 .wpo-choose-wrap .grid h3 span {
  font-family: "Urbanist";
}

.wpo-choose-section .wpo-choose-wrap .grid h3 + p,
.wpo-choose-section-s2 .wpo-choose-wrap .grid h3 + p {
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;
  margin: 0;
  color: #625F71;
  font-family: "Urbanist";
}

@media (max-width: 1399px) {
  .wpo-choose-section .wpo-choose-wrap .grid h3 + p,
  .wpo-choose-section-s2 .wpo-choose-wrap .grid h3 + p {
    font-size: 16px;
    font-size: 1.06667rem;
  }
}

.wpo-choose-section .right-img,
.wpo-choose-section-s2 .right-img {
  position: relative;
  padding: 20px;
  border: 1px dashed #CB9E00;
  border-radius: 20px;
  top: -120px;
  margin-right: -170px;
}

@media (max-width: 1199px) {
  .wpo-choose-section .right-img,
  .wpo-choose-section-s2 .right-img {
    top: 0px;
    margin-right: -70px;
  }
}

@media (max-width: 991px) {
  .wpo-choose-section .right-img,
  .wpo-choose-section-s2 .right-img {
    margin-right: 0px;
  }
}

.wpo-choose-section .right-img img,
.wpo-choose-section-s2 .right-img img {
  border-radius: 20px;
}

.wpo-choose-section .right-img .video-btn,
.wpo-choose-section-s2 .right-img .video-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  line-height: 96px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  z-index: 1;
  border: 2px solid #fff;
}

.wpo-choose-section .right-img .video-btn .fi::before,
.wpo-choose-section-s2 .right-img .video-btn .fi::before {
  font-size: 35px;
  position: relative;
  right: -3px;
  color: #fff;
  z-index: 1;
}

/* 3.8 wpo-blog-section */
.wpo-blog-section,
.wpo-blog-section-s2 {
  padding-bottom: 90px;
  position: relative;
  z-index: 1;
}

@media (max-width: 767px) {
  .wpo-blog-section,
  .wpo-blog-section-s2 {
    padding-bottom: 60px;
  }
}

.wpo-blog-section .wpo-blog-item,
.wpo-blog-section-s2 .wpo-blog-item {
  margin-bottom: 30px;
  padding: 10px;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
  border-radius: 10px;
  padding-bottom: 0;
}

.wpo-blog-section .wpo-blog-item .wpo-blog-img,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-img {
  overflow: hidden;
  position: relative;
  border-radius: 10px;
}

.wpo-blog-section .wpo-blog-item .wpo-blog-img img,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-img img {
  width: 100%;
  -webkit-filter: grayscale(0);
  -moz-filter: grayscale(0);
  -o-filter: grayscale(0);
  -ms-filter: grayscale(0);
  filter: grayscale(0);
  -webkit-transition: all .3s;
  transition: all .3s;
  -webkit-transform: scale(1);
          transform: scale(1);
  border-radius: 10px;
}

.wpo-blog-section .wpo-blog-item:hover .wpo-blog-img img,
.wpo-blog-section-s2 .wpo-blog-item:hover .wpo-blog-img img {
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  filter: grayscale(100%);
  -webkit-transform: scale(1.2);
          transform: scale(1.2);
}

.wpo-blog-section .wpo-blog-item .wpo-blog-content,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content {
  padding: 30px 20px;
}

@media (max-width: 1199px) {
  .wpo-blog-section .wpo-blog-item .wpo-blog-content,
  .wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content {
    padding: 20px 5px;
  }
}

.wpo-blog-section .wpo-blog-item .wpo-blog-content ul,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content ul {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 15px;
}

.wpo-blog-section .wpo-blog-item .wpo-blog-content ul li,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content ul li {
  color: #625F71;
  font-size: 16px;
}

.wpo-blog-section .wpo-blog-item .wpo-blog-content ul li:first-child,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content ul li:first-child {
  padding-right: 30px;
  position: relative;
}

.wpo-blog-section .wpo-blog-item .wpo-blog-content ul li:first-child:before,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content ul li:first-child:before {
  position: absolute;
  right: 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 6px;
  height: 6px;
  content: "";
  background: #625F71;
  border-radius: 50%;
}

.wpo-blog-section .wpo-blog-item .wpo-blog-content ul li a,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content ul li a {
  color: #625F71;
}

.wpo-blog-section .wpo-blog-item .wpo-blog-content ul li a:hover,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content ul li a:hover {
  color: #4540E1;
}

.wpo-blog-section .wpo-blog-item .wpo-blog-content h2,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content h2 {
  font-family: "Urbanist";
  margin-bottom: 20px;
  font-size: 25px;
  font-style: normal;
  font-weight: 700;
  line-height: 35px;
  margin-top: 10px;
}

@media (max-width: 1399px) {
  .wpo-blog-section .wpo-blog-item .wpo-blog-content h2,
  .wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content h2 {
    font-size: 22px;
    line-height: 30px;
    margin-bottom: 20px;
  }
}

@media (max-width: 1199px) {
  .wpo-blog-section .wpo-blog-item .wpo-blog-content h2,
  .wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content h2 {
    font-size: 20px;
  }
}

.wpo-blog-section .wpo-blog-item .wpo-blog-content h2 a,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content h2 a {
  color: #1A1729;
}

.wpo-blog-section .wpo-blog-item .wpo-blog-content h2 a:hover,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content h2 a:hover {
  color: #4540E1;
}

.wpo-blog-section .wpo-blog-item .wpo-blog-content a.more,
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content a.more {
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
  color: #F2BC00;
  display: inline-block;
  padding-bottom: 0px;
  border-bottom: 1px solid #F2BC00;
}

@media (max-width: 1199px) {
  .wpo-blog-section .wpo-blog-item .wpo-blog-content a.more,
  .wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content a.more {
    font-size: 16px;
  }
}

/* 3.9 wpo-subscribe-section  */
.wpo-subscribe-section,
.wpo-subscribe-section-s2 {
  padding-top: 0;
}

.wpo-subscribe-section .wpo-subscribe-wrap,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap {
  padding: 70px 100px;
  background: url(../../images/noise.png), #CB9E00 50%/cover no-repeat;
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  background-size: cover;
  z-index: 1;
}

@media (max-width: 1199px) {
  .wpo-subscribe-section .wpo-subscribe-wrap,
  .wpo-subscribe-section-s2 .wpo-subscribe-wrap {
    padding: 70px;
  }
}

@media (max-width: 991px) {
  .wpo-subscribe-section .wpo-subscribe-wrap,
  .wpo-subscribe-section-s2 .wpo-subscribe-wrap {
    padding: 50px 30px;
  }
}

@media (max-width: 575px) {
  .wpo-subscribe-section .wpo-subscribe-wrap,
  .wpo-subscribe-section-s2 .wpo-subscribe-wrap {
    padding: 40px 15px;
    border-radius: 30px;
  }
}

.wpo-subscribe-section .wpo-subscribe-wrap .subscribe-text,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-text {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
  margin-bottom: 40px;
}

@media (max-width: 991px) {
  .wpo-subscribe-section .wpo-subscribe-wrap .subscribe-text,
  .wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-text {
    margin-bottom: 20px;
  }
}

.wpo-subscribe-section .wpo-subscribe-wrap .subscribe-text p,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-text p {
  font-size: 21px;
  color: #fff;
}

@media (max-width: 991px) {
  .wpo-subscribe-section .wpo-subscribe-wrap .subscribe-text p,
  .wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-text p {
    font-size: 16px;
  }
}

.wpo-subscribe-section .wpo-subscribe-wrap .subscribe-text h3,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-text h3 {
  font-size: 40px;
  color: #fff;
  margin-top: 10px;
  margin-bottom: 10px;
  font-weight: 700;
}

@media (max-width: 1199px) {
  .wpo-subscribe-section .wpo-subscribe-wrap .subscribe-text h3,
  .wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-text h3 {
    font-size: 32px;
  }
}

@media (max-width: 991px) {
  .wpo-subscribe-section .wpo-subscribe-wrap .subscribe-text h3,
  .wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-text h3 {
    font-size: 28px;
    margin-top: 0;
  }
}

@media (max-width: 575px) {
  .wpo-subscribe-section .wpo-subscribe-wrap .subscribe-text h3,
  .wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-text h3 {
    font-size: 24px;
  }
}

.wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form {
  -ms-flex-preferred-size: 70%;
      flex-basis: 70%;
}

.wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form .input-field,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form .input-field {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form .input-field input,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form .input-field input {
  width: 100%;
  padding: 40px;
  padding-right: 190px;
  height: 75px;
  border-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
}

@media (max-width: 767px) {
  .wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form .input-field input,
  .wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form .input-field input {
    height: 55px;
    padding: 30px;
    padding-right: 145px;
  }
}

@media (max-width: 450px) {
  .wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form .input-field input,
  .wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form .input-field input {
    padding: 10px;
    padding-right: 100px;
  }
}

.wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form .input-field input:focus,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form .input-field input:focus {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form .input-field button,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form .input-field button {
  position: absolute;
  right: 8px;
  top: 8px;
  height: 65px;
  width: 170px;
  border: 0;
  background: #fff;
  border-radius: 3px;
  color: #F2BC00;
  font-weight: 600;
}

@media (max-width: 767px) {
  .wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form .input-field button,
  .wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form .input-field button {
    height: 45px;
    width: 130px;
  }
}

@media (max-width: 450px) {
  .wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form .input-field button,
  .wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form .input-field button {
    width: 80px;
    height: 40px;
  }
}

.wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form .input-field ::-webkit-input-placeholder,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form .input-field ::-webkit-input-placeholder {
  color: #fff;
}

.wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form .input-field :-ms-input-placeholder,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form .input-field :-ms-input-placeholder {
  color: #fff;
}

.wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form .input-field ::-ms-input-placeholder,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form .input-field ::-ms-input-placeholder {
  color: #fff;
}

.wpo-subscribe-section .wpo-subscribe-wrap .subscribe-form .input-field ::placeholder,
.wpo-subscribe-section-s2 .wpo-subscribe-wrap .subscribe-form .input-field ::placeholder {
  color: #fff;
}

/*======================================
5. Home-style-2
 =======================================*/
/* 5.1 wpo-features-area */
@media (max-width: 991px) {
  .wpo-features-area,
  .wpo-features-area-s2,
  .wpo-features-area-s3 {
    padding: 120px 0 0;
  }
}

@media (max-width: 767px) {
  .wpo-features-area,
  .wpo-features-area-s2,
  .wpo-features-area-s3 {
    padding: 80px 0 0;
  }
}

.wpo-features-area .features-wrap,
.wpo-features-area-s2 .features-wrap,
.wpo-features-area-s3 .features-wrap {
  max-width: 1420px;
  margin: 0 auto;
  margin-top: -115px;
  z-index: 9;
  position: relative;
  background: #fff;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
  border-radius: 10px;
}

@media (max-width: 991px) {
  .wpo-features-area .features-wrap,
  .wpo-features-area-s2 .features-wrap,
  .wpo-features-area-s3 .features-wrap {
    margin-top: 0;
  }
}

.wpo-features-area .features-wrap .feature-item-wrap,
.wpo-features-area-s2 .features-wrap .feature-item-wrap,
.wpo-features-area-s3 .features-wrap .feature-item-wrap {
  position: relative;
}

.wpo-features-area .features-wrap .feature-item-wrap .feature-item,
.wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden {
  padding: 30px 40px;
  text-align: center;
  position: relative;
}

.wpo-features-area .features-wrap .feature-item-wrap .feature-item .icon,
.wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden .icon,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item .icon,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden .icon,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item .icon,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden .icon {
  width: 90px;
  height: 90px;
  line-height: 90px;
  background: #FAF5E6;
  text-align: center;
  border-radius: 50%;
  margin: 0 auto;
  margin-bottom: 20px;
}

.wpo-features-area .features-wrap .feature-item-wrap .feature-item .icon .fi,
.wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden .icon .fi,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item .icon .fi,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden .icon .fi,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item .icon .fi,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden .icon .fi {
  color: #D0A202;
  font-size: 40px;
}

.wpo-features-area .features-wrap .feature-item-wrap .feature-item h2,
.wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden h2,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item h2,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden h2,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item h2,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden h2 {
  font-style: normal;
  font-size: 30px;
  font-style: normal;
  font-weight: 600;
}

@media (max-width: 991px) {
  .wpo-features-area .features-wrap .feature-item-wrap .feature-item h2,
  .wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden h2,
  .wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item h2,
  .wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden h2,
  .wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item h2,
  .wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden h2 {
    font-size: 25px;
  }
}

.wpo-features-area .features-wrap .feature-item-wrap .feature-item h2 a,
.wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden h2 a,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item h2 a,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden h2 a,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item h2 a,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden h2 a {
  color: #1A1729;
}

@media (max-width: 1199px) {
  .wpo-features-area .features-wrap .feature-item-wrap .feature-item p,
  .wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden p,
  .wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item p,
  .wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden p,
  .wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item p,
  .wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden p {
    font-size: 16px;
  }
}

@media (max-width: 1399px) {
  .wpo-features-area .features-wrap .feature-item-wrap .feature-item,
  .wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item,
  .wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item {
    padding: 30px;
  }
}

@media (max-width: 1199px) {
  .wpo-features-area .features-wrap .feature-item-wrap .feature-item,
  .wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item,
  .wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item {
    padding: 20px 10px;
  }
}

@media (max-width: 991px) {
  .wpo-features-area .features-wrap .feature-item-wrap .feature-item,
  .wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item,
  .wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  }
}

.wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden {
  position: absolute;
  left: 0;
  top: -30px;
  width: 100%;
  padding: 40px;
  background: #F2BC00;
  border-radius: 20px;
  z-index: 11;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transition: all .3s;
  transition: all .3s;
}

@media (max-width: 1399px) {
  .wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden,
  .wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden,
  .wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden {
    top: -17px;
    padding: 30px;
  }
}

@media (max-width: 1199px) {
  .wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden,
  .wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden,
  .wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden {
    padding: 20px 10px;
  }
}

.wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden .icon,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden .icon,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden .icon {
  background: #fff;
}

.wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden::before,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden::before,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden::before {
  display: none;
}

.wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden h2 a,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden h2 a,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden h2 a {
  color: #fff;
}

.wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden p,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden p,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden p {
  margin-bottom: 0;
  color: #fff;
}

.wpo-features-area .features-wrap .feature-item-wrap .feature-item-hidden a.more,
.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item-hidden a.more,
.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item-hidden a.more {
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 28px;
  color: #fff;
  display: block;
  margin-top: 15px;
}

.wpo-features-area .features-wrap .feature-item-wrap:hover .feature-item-hidden, .wpo-features-area .features-wrap .feature-item-wrap.active .feature-item-hidden,
.wpo-features-area-s2 .features-wrap .feature-item-wrap:hover .feature-item-hidden,
.wpo-features-area-s2 .features-wrap .feature-item-wrap.active .feature-item-hidden,
.wpo-features-area-s3 .features-wrap .feature-item-wrap:hover .feature-item-hidden,
.wpo-features-area-s3 .features-wrap .feature-item-wrap.active .feature-item-hidden {
  opacity: 1;
  visibility: visible;
  -webkit-transform: scale(1);
          transform: scale(1);
}

.wpo-features-area .features-wrap .col,
.wpo-features-area-s2 .features-wrap .col,
.wpo-features-area-s3 .features-wrap .col {
  padding: 0;
}

@media (max-width: 575px) {
  .wpo-features-area .features-wrap .col,
  .wpo-features-area-s2 .features-wrap .col,
  .wpo-features-area-s3 .features-wrap .col {
    padding: 0 15px;
  }
}

.wpo-features-area .features-wrap .col:first-child .feature-item,
.wpo-features-area-s2 .features-wrap .col:first-child .feature-item,
.wpo-features-area-s3 .features-wrap .col:first-child .feature-item {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}

@media (max-width: 991px) {
  .wpo-features-area .features-wrap .col:first-child .feature-item,
  .wpo-features-area-s2 .features-wrap .col:first-child .feature-item,
  .wpo-features-area-s3 .features-wrap .col:first-child .feature-item {
    border-radius: 0;
  }
}

.wpo-features-area .features-wrap .col:first-child .feature-item:before,
.wpo-features-area-s2 .features-wrap .col:first-child .feature-item:before,
.wpo-features-area-s3 .features-wrap .col:first-child .feature-item:before {
  display: none;
}

@media (max-width: 991px) {
  .wpo-features-area .features-wrap .col:nth-child(2) .feature-item,
  .wpo-features-area-s2 .features-wrap .col:nth-child(2) .feature-item,
  .wpo-features-area-s3 .features-wrap .col:nth-child(2) .feature-item {
    border-radius: 0;
  }
}

.wpo-features-area .features-wrap .col:nth-child(2) .feature-item .icon,
.wpo-features-area-s2 .features-wrap .col:nth-child(2) .feature-item .icon,
.wpo-features-area-s3 .features-wrap .col:nth-child(2) .feature-item .icon {
  background: #EEEDFC;
}

.wpo-features-area .features-wrap .col:nth-child(2) .feature-item .icon .fi,
.wpo-features-area-s2 .features-wrap .col:nth-child(2) .feature-item .icon .fi,
.wpo-features-area-s3 .features-wrap .col:nth-child(2) .feature-item .icon .fi {
  color: #4540E1;
}

.wpo-features-area .features-wrap .col:nth-child(2) .feature-item-hidden,
.wpo-features-area-s2 .features-wrap .col:nth-child(2) .feature-item-hidden,
.wpo-features-area-s3 .features-wrap .col:nth-child(2) .feature-item-hidden {
  background: #4540E1;
}

.wpo-features-area .features-wrap .col:nth-child(2) .feature-item-hidden .icon .fi,
.wpo-features-area-s2 .features-wrap .col:nth-child(2) .feature-item-hidden .icon .fi,
.wpo-features-area-s3 .features-wrap .col:nth-child(2) .feature-item-hidden .icon .fi {
  color: #4540E1;
}

@media (max-width: 991px) {
  .wpo-features-area .features-wrap .col:nth-child(3) .feature-item,
  .wpo-features-area-s2 .features-wrap .col:nth-child(3) .feature-item,
  .wpo-features-area-s3 .features-wrap .col:nth-child(3) .feature-item {
    border-radius: 0;
  }
}

.wpo-features-area .features-wrap .col:nth-child(3) .feature-item .icon,
.wpo-features-area-s2 .features-wrap .col:nth-child(3) .feature-item .icon,
.wpo-features-area-s3 .features-wrap .col:nth-child(3) .feature-item .icon {
  background: #DEF2ED;
}

.wpo-features-area .features-wrap .col:nth-child(3) .feature-item .icon .fi,
.wpo-features-area-s2 .features-wrap .col:nth-child(3) .feature-item .icon .fi,
.wpo-features-area-s3 .features-wrap .col:nth-child(3) .feature-item .icon .fi {
  color: #15A280;
}

.wpo-features-area .features-wrap .col:nth-child(3) .feature-item-hidden,
.wpo-features-area-s2 .features-wrap .col:nth-child(3) .feature-item-hidden,
.wpo-features-area-s3 .features-wrap .col:nth-child(3) .feature-item-hidden {
  background: #15A280;
}

.wpo-features-area .features-wrap .col:nth-child(3) .feature-item-hidden .icon .fi,
.wpo-features-area-s2 .features-wrap .col:nth-child(3) .feature-item-hidden .icon .fi,
.wpo-features-area-s3 .features-wrap .col:nth-child(3) .feature-item-hidden .icon .fi {
  color: #15A280;
}

@media (max-width: 991px) {
  .wpo-features-area .features-wrap .col:last-child .feature-item,
  .wpo-features-area-s2 .features-wrap .col:last-child .feature-item,
  .wpo-features-area-s3 .features-wrap .col:last-child .feature-item {
    border-radius: 0;
  }
}

.wpo-features-area .features-wrap .col:last-child .feature-item .icon,
.wpo-features-area-s2 .features-wrap .col:last-child .feature-item .icon,
.wpo-features-area-s3 .features-wrap .col:last-child .feature-item .icon {
  background: #FFEDF1;
}

.wpo-features-area .features-wrap .col:last-child .feature-item .icon .fi,
.wpo-features-area-s2 .features-wrap .col:last-child .feature-item .icon .fi,
.wpo-features-area-s3 .features-wrap .col:last-child .feature-item .icon .fi {
  color: #F9406C;
}

.wpo-features-area .features-wrap .col:last-child .feature-item-hidden,
.wpo-features-area-s2 .features-wrap .col:last-child .feature-item-hidden,
.wpo-features-area-s3 .features-wrap .col:last-child .feature-item-hidden {
  background: #F9406C;
}

.wpo-features-area .features-wrap .col:last-child .feature-item-hidden .icon .fi,
.wpo-features-area-s2 .features-wrap .col:last-child .feature-item-hidden .icon .fi,
.wpo-features-area-s3 .features-wrap .col:last-child .feature-item-hidden .icon .fi {
  color: #F9406C;
}

.wpo-about-section-s2,
.wpo-about-section-s3,
.wpo-about-section-s4,
.wpo-about-section-s5 {
  padding-top: 60px;
  padding-bottom: 60px;
}

.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img-wrap,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img-wrap,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img-wrap,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img-wrap {
  margin-left: 0;
}

.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img {
  margin-left: -180px;
  border-radius: 0;
  position: relative;
  z-index: 1;
  padding-right: 60px;
}

@media (max-width: 1399px) {
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-img,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-img,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-img,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img {
    margin-left: -120px;
  }
}

@media (max-width: 1199px) {
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-img,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-img,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-img,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img {
    margin-left: -100px;
  }
}

@media (max-width: 991px) {
  .wpo-about-section-s2 .wpo-about-wrap .wpo-about-img,
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-img,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-img,
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img {
    margin-left: 0px;
    padding-right: 0;
  }
}

.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img .back-shape,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img .back-shape,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img .back-shape,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img .back-shape {
  position: absolute;
  left: 0px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: -1;
}

.wpo-about-section-s2 .wpo-about-wrap .wpo-about-img .back-shape img,
.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img .back-shape img,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img .back-shape img,
.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img .back-shape img {
  -webkit-animation: circle 60s linear infinite;
          animation: circle 60s linear infinite;
  width: 100%;
}

.wpo-about-section-s2 .wpo-about-wrap .theme-btn-s2,
.wpo-about-section-s3 .wpo-about-wrap .theme-btn-s2,
.wpo-about-section-s4 .wpo-about-wrap .theme-btn-s2,
.wpo-about-section-s5 .wpo-about-wrap .theme-btn-s2 {
  margin-top: 30px;
}

/* 5.3 wpo-courses-section-s2 */
.wpo-courses-section-s2,
.wpo-courses-section-s4 {
  padding-bottom: 90px;
}

@media (max-width: 991px) {
  .wpo-courses-section-s2,
  .wpo-courses-section-s4 {
    padding-bottom: 60px;
  }
}

@media (max-width: 767px) {
  .wpo-courses-section-s2,
  .wpo-courses-section-s4 {
    padding-bottom: 50px;
  }
}

.wpo-courses-section-s2 .wpo-courses-wrap .wpo-courses-item,
.wpo-courses-section-s4 .wpo-courses-wrap .wpo-courses-item {
  margin-bottom: 30px;
  text-align: center;
}

.wpo-courses-section-s2 .wpo-courses-wrap .wpo-courses-item .courses-icon,
.wpo-courses-section-s4 .wpo-courses-wrap .wpo-courses-item .courses-icon {
  margin: 0 auto;
  margin-bottom: 15px;
}

/* 5.4 wpo-choose-section-s2 */
.wpo-choose-section-s2 {
  background: none;
  padding-bottom: 90px;
}

@media (max-width: 991px) {
  .wpo-choose-section-s2 {
    padding-bottom: 60px;
  }
}

@media (max-width: 767px) {
  .wpo-choose-section-s2 {
    padding-bottom: 50px;
  }
}

.wpo-choose-section-s2 .right-img {
  margin-right: 0;
  top: 0;
  margin-bottom: 60px;
}

@media (max-width: 575px) {
  .wpo-choose-section-s2 .right-img img {
    min-height: 200px;
    -o-object-fit: cover;
       object-fit: cover;
  }
}

.wpo-choose-section-s2 .wpo-choose-wrap {
  padding: 0;
  margin: 0 -40px;
}

@media (max-width: 1399px) {
  .wpo-choose-section-s2 .wpo-choose-wrap {
    margin: 0 -15px;
  }
}

.wpo-choose-section-s2 .wpo-choose-wrap .grid {
  width: 25%;
  padding: 0 40px;
}

@media (max-width: 1399px) {
  .wpo-choose-section-s2 .wpo-choose-wrap .grid {
    padding: 0 15px;
  }
}

@media (max-width: 991px) {
  .wpo-choose-section-s2 .wpo-choose-wrap .grid {
    width: 50%;
  }
}

@media (max-width: 575px) {
  .wpo-choose-section-s2 .wpo-choose-wrap .grid {
    width: 100%;
  }
}

.wpo-choose-section-s2 .wpo-choose-wrap .grid .info {
  max-width: 270px;
}

@media (max-width: 575px) {
  .wpo-choose-section-s2 .wpo-choose-wrap .grid .info {
    margin: 0 auto;
  }
}

/* 5.6 wpo-subscribe-section-s2 */
.wpo-subscribe-section-s2 {
  padding-bottom: 0;
}

.wpo-subscribe-section-s2 .container-fluid {
  padding: 0;
}

.wpo-subscribe-section-s2 .container-fluid .wpo-subscribe-wrap {
  border-radius: 0;
}

/*======================================
6. Home-style-3
 =======================================*/
/* 6.1 wpo-features-area-s2*/
.wpo-features-area-s2 .features-wrap {
  margin-top: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item {
  background: #FCF7E6;
  border-radius: 10px;
  text-align: center;
}

@media (max-width: 1199px) {
  .wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item h2 {
    font-size: 25px;
  }
}

@media (max-width: 991px) {
  .wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item {
    margin-bottom: 20px;
  }
}

.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item .icon {
  background: #F2BC00;
}

.wpo-features-area-s2 .features-wrap .feature-item-wrap .feature-item .icon .fi {
  color: #fff;
}

.wpo-features-area-s2 .features-wrap .col {
  padding-right: calc(var(--bs-gutter-x)/ 2);
  padding-left: calc(var(--bs-gutter-x)/ 2);
}

.wpo-features-area-s2 .features-wrap .col:nth-child(2) .feature-item {
  background: #F3F2FF;
}

.wpo-features-area-s2 .features-wrap .col:nth-child(2) .feature-item .icon {
  background: #4540E1;
}

.wpo-features-area-s2 .features-wrap .col:nth-child(2) .feature-item .icon .fi {
  color: #fff;
}

.wpo-features-area-s2 .features-wrap .col:nth-child(3) .feature-item {
  background: #E6FAF5;
}

.wpo-features-area-s2 .features-wrap .col:nth-child(3) .feature-item .icon {
  background: #15A280;
}

.wpo-features-area-s2 .features-wrap .col:nth-child(3) .feature-item .icon .fi {
  color: #fff;
}

.wpo-features-area-s2 .features-wrap .col:nth-child(4) .feature-item {
  background: #FFEEF2;
}

.wpo-features-area-s2 .features-wrap .col:nth-child(4) .feature-item .icon {
  background: #F9406C;
}

.wpo-features-area-s2 .features-wrap .col:nth-child(4) .feature-item .icon .fi {
  color: #fff;
}

/* 6.2 wpo-about-section-s3*/
.wpo-about-section-s3,
.wpo-about-section-s4 {
  padding: 80px 0 100px;
}

@media (max-width: 575px) {
  .wpo-about-section-s3,
  .wpo-about-section-s4 {
    padding: 50px 0 80px;
  }
}

.wpo-about-section-s3 .wpo-about-wrap .wpo-about-img,
.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img {
  margin-left: -300px;
  padding-right: 0;
}

@media (max-width: 1399px) {
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-img,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-img {
    margin-left: -100px;
  }
}

@media (max-width: 991px) {
  .wpo-about-section-s3 .wpo-about-wrap .wpo-about-img,
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-img {
    margin-left: 0px;
    margin-top: 0px;
  }
}

/* 6.3 wpo-courses-section-s3*/
.wpo-courses-section-s3 .wpo-courses-wrap .wpo-courses-item {
  border-radius: 10px;
  background: #fff;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
  padding: 10px;
}

.wpo-courses-section-s3 .wpo-courses-wrap .wpo-courses-item .courses-img img {
  border-radius: 10px;
}

.wpo-courses-section-s3 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text {
  padding: 20px 15px 0;
}

.wpo-courses-section-s3 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 {
  font-size: 25px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 10px;
}

.wpo-courses-section-s3 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 a {
  color: #1A1729;
}

.wpo-courses-section-s3 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text h2 a:hover {
  color: #F2BC00;
}

.wpo-courses-section-s3 .wpo-courses-wrap .wpo-courses-item .wpo-courses-text p {
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 28px;
}

.wpo-courses-section-s3 .wpo-courses-wrap .owl-stage-outer {
  margin: -14px;
}

.wpo-courses-section-s3 .wpo-courses-wrap .owl-stage {
  margin: 14px;
}

.wpo-courses-section-s3 .wpo-courses-wrap .owl-controls {
  width: 100%;
  margin: 0;
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.wpo-courses-section-s3 .wpo-courses-wrap .owl-nav [class*=owl-] {
  padding: 0;
  margin: 0;
  color: #4540E1;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border: 0;
  background: none;
}

.wpo-courses-section-s3 .wpo-courses-wrap .owl-nav .owl-prev,
.wpo-courses-section-s3 .wpo-courses-wrap .owl-nav .owl-next {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background: #fff;
  width: 50px;
  height: 50px;
  line-height: 48px;
  -webkit-transition: all .3s;
  transition: all .3s;
  border: 1px solid #DDDDDD;
  border-radius: 50%;
  font-size: 20px;
}

.wpo-courses-section-s3 .wpo-courses-wrap .owl-nav .owl-prev:hover,
.wpo-courses-section-s3 .wpo-courses-wrap .owl-nav .owl-next:hover {
  background: #4540E1;
  border-color: #4540E1;
  color: #fff;
}

@media (max-width: 767px) {
  .wpo-courses-section-s3 .wpo-courses-wrap .owl-nav .owl-prev,
  .wpo-courses-section-s3 .wpo-courses-wrap .owl-nav .owl-next {
    display: none;
  }
}

.wpo-courses-section-s3 .wpo-courses-wrap .owl-nav .owl-prev {
  left: -70px;
}

@media (max-width: 1440px) {
  .wpo-courses-section-s3 .wpo-courses-wrap .owl-nav .owl-prev {
    left: 20px;
    opacity: 0;
    visibility: hidden;
  }
}

.wpo-courses-section-s3 .wpo-courses-wrap .owl-nav .owl-next {
  right: -70px;
}

@media (max-width: 1440px) {
  .wpo-courses-section-s3 .wpo-courses-wrap .owl-nav .owl-next {
    right: 20px;
    opacity: 0;
    visibility: hidden;
  }
}

.wpo-courses-section-s3 .wpo-courses-wrap .owl-dots {
  text-align: center;
  bottom: -20px;
  position: relative;
}

.wpo-courses-section-s3 .wpo-courses-wrap .owl-dots button {
  width: 10px;
  height: 12px;
  border: 0;
  background: #d4e6f5;
  margin-right: 10px;
  border-radius: 50%;
}

.wpo-courses-section-s3 .wpo-courses-wrap .owl-dots .owl-dot.active {
  background-color: #4540E1;
}

.wpo-courses-section-s3 .wpo-courses-wrap:hover .owl-nav .owl-prev,
.wpo-courses-section-s3 .wpo-courses-wrap:hover .owl-nav .owl-next {
  opacity: 1;
  visibility: visible;
}

/* 6.4 wpo-popular-area-s2 */
.wpo-popular-area-s2,
.wpo-popular-area-s3 {
  background: #FCF9EF;
}

.wpo-popular-area-s2 .shape-1,
.wpo-popular-area-s3 .shape-1 {
  position: absolute;
  left: 15%;
  top: 20%;
  z-index: -1;
  -webkit-animation: bounce 3s linear infinite;
          animation: bounce 3s linear infinite;
}

@media (max-width: 991px) {
  .wpo-popular-area-s2 .shape-1,
  .wpo-popular-area-s3 .shape-1 {
    top: 8%;
  }
}

.wpo-popular-area-s2 .shape-2,
.wpo-popular-area-s3 .shape-2 {
  position: absolute;
  left: 50px;
  bottom: 50px;
  z-index: -1;
  -webkit-animation: zoom-in-zoom-out 2s ease infinite;
          animation: zoom-in-zoom-out 2s ease infinite;
}

.wpo-popular-area-s2 .shape-3,
.wpo-popular-area-s3 .shape-3 {
  position: absolute;
  right: -30px;
  top: 40px;
  z-index: -1;
  -webkit-animation: bounceX 3s linear infinite;
          animation: bounceX 3s linear infinite;
}

.wpo-popular-area-s2 .shape-4,
.wpo-popular-area-s3 .shape-4 {
  position: absolute;
  right: 50px;
  bottom: 40px;
  z-index: -1;
  -webkit-animation: circle 20s linear infinite;
          animation: circle 20s linear infinite;
}

/* 6.5 wpo-event-area */
.wpo-event-area {
  background: #FCF9EF;
  position: relative;
  z-index: 1;
  padding-bottom: 90px;
}

@media (max-width: 991px) {
  .wpo-event-area {
    padding-bottom: 60px;
  }
}

@media (max-width: 767px) {
  .wpo-event-area {
    padding-bottom: 50px;
  }
}

.wpo-event-area .wpo-event-wrap .wpo-event-item {
  padding: 15px;
  border-radius: 10px;
  background: #fff;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
  margin-bottom: 30px;
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-img {
  position: relative;
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-img img {
  border-radius: 10px;
  width: 100%;
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-img .thumb {
  width: 80px;
  height: 80px;
  background: #4540E1;
  position: absolute;
  right: 15px;
  top: 15px;
  text-align: center;
  border-radius: 5px;
  padding: 15px 0;
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-img .thumb h5 {
  font-size: 35px;
  font-style: normal;
  font-weight: 700;
  color: #fff;
  line-height: 20px;
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-img .thumb span {
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  color: #fff;
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content {
  padding: 20px 15px 0;
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content ul {
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content ul li {
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #625F71;
}

@media (max-width: 1199px) {
  .wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content ul li {
    font-size: 15px;
  }
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content ul li + li {
  margin-left: 20px;
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content ul li i {
  margin-right: 5px;
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content h2 {
  font-size: 30px;
  font-style: normal;
  font-weight: 700;
  line-height: 40px;
  margin-top: 15px;
  margin-bottom: 15px;
}

@media (max-width: 1399px) {
  .wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content h2 {
    font-size: 22px;
    line-height: 35px;
  }
}

@media (max-width: 1199px) {
  .wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content h2 {
    font-size: 20px;
    line-height: 28px;
  }
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content h2 a {
  color: #1A1729;
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content h2 a:hover {
  color: #4540E1;
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content a.more {
  position: relative;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  padding-right: 30px;
  color: #4540E1;
}

@media (max-width: 1199px) {
  .wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content a.more {
    font-size: 16px;
  }
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content a.more:before {
  position: absolute;
  font-family: "Flaticon";
  content: "\f125";
  top: 0px;
  right: 0px;
  font-size: 18px;
  -webkit-transition: all .3s;
  transition: all .3s;
}

@media (max-width: 1199px) {
  .wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content a.more:before {
    top: -3px;
  }
}

.wpo-event-area .wpo-event-wrap .wpo-event-item .wpo-event-content a.more:hover:before {
  right: -5px;
}

.wpo-event-area .wpo-event-wrap .col:nth-child(2) .wpo-event-item .wpo-event-img .thumb {
  background: #15A280;
}

.wpo-event-area .wpo-event-wrap .col:nth-child(3) .wpo-event-item .wpo-event-img .thumb {
  background: #F9406C;
}

.wpo-event-area .shape-1 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -11;
}

@media (max-width: 1199px) {
  .wpo-event-area .shape-1 {
    max-width: 50%;
  }
}

.wpo-event-area .shape-2 {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -11;
}

@media (max-width: 1199px) {
  .wpo-event-area .shape-2 {
    max-width: 50%;
  }
}

/*======================================
7. Home-style-4
 =======================================*/
/* 7.1 wpo-about-section-s4 */
@media (max-width: 991px) {
  .wpo-about-section-s4 {
    padding-top: 70px;
  }
}

.wpo-about-section-s4 .wpo-about-wrap .wpo-about-img {
  margin-left: -100px;
  padding-right: 50px;
}

@media (max-width: 1199px) {
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-img {
    margin-left: -80px;
    padding-right: 30px;
  }
}

@media (max-width: 991px) {
  .wpo-about-section-s4 .wpo-about-wrap .wpo-about-img {
    margin-left: 0px;
    padding-right: 0px;
  }
}

.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .wpo-section-title small {
  color: #625F71;
}

.wpo-about-section-s4 .wpo-about-wrap .wpo-about-text .theme-btn-s4 {
  margin-top: 20px;
}

/* 7.2 wpo-fun-fact-section */
.wpo-fun-fact-section,
.wpo-fun-fact-section-s2 {
  position: relative;
  padding-bottom: 120px;
}

@media (max-width: 767px) {
  .wpo-fun-fact-section,
  .wpo-fun-fact-section-s2 {
    padding-bottom: 80px;
  }
}

.wpo-fun-fact-section .wpo-fun-fact-grids,
.wpo-fun-fact-section-s2 .wpo-fun-fact-grids {
  border: 1px solid #1A1729;
  padding: 70px 40px;
  position: relative;
  z-index: 1;
  background: url(../../images/noise.png), #fff 50%/cover no-repeat;
  border-radius: 20px;
}

@media (max-width: 767px) {
  .wpo-fun-fact-section .wpo-fun-fact-grids,
  .wpo-fun-fact-section-s2 .wpo-fun-fact-grids {
    padding: 40px 15px 0;
  }
}

.wpo-fun-fact-section .wpo-fun-fact-grids .grid,
.wpo-fun-fact-section-s2 .wpo-fun-fact-grids .grid {
  width: 25%;
  float: left;
}

@media (max-width: 767px) {
  .wpo-fun-fact-section .wpo-fun-fact-grids .grid,
  .wpo-fun-fact-section-s2 .wpo-fun-fact-grids .grid {
    width: 50%;
    margin-bottom: 50px;
  }
}

.wpo-fun-fact-section .grid h3,
.wpo-fun-fact-section-s2 .grid h3 {
  font-size: 60px;
  font-weight: 700;
  line-height: normal;
  color: #1A1729;
  margin: 0 0 0.1em;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.wpo-fun-fact-section .grid h3 span,
.wpo-fun-fact-section-s2 .grid h3 span {
  font-family: "Urbanist";
}

@media (max-width: 1200px) {
  .wpo-fun-fact-section .grid h3,
  .wpo-fun-fact-section-s2 .grid h3 {
    font-size: 50px;
    ont-size: 3.33333rem;
  }
}

@media (max-width: 991px) {
  .wpo-fun-fact-section .grid h3,
  .wpo-fun-fact-section-s2 .grid h3 {
    font-size: 40px;
    font-size: 2.66667rem;
  }
}

@media (max-width: 767px) {
  .wpo-fun-fact-section .grid h3,
  .wpo-fun-fact-section-s2 .grid h3 {
    font-size: 35px;
    font-size: 2.33333rem;
  }
}

.wpo-fun-fact-section .grid h3 + p,
.wpo-fun-fact-section-s2 .grid h3 + p {
  font-size: 25px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin: 0;
  color: #1A1729;
  font-family: "Urbanist";
  text-align: center;
}

@media (max-width: 1200px) {
  .wpo-fun-fact-section .grid h3 + p,
  .wpo-fun-fact-section-s2 .grid h3 + p {
    font-size: 18px;
    font-size: 1.2rem;
  }
}

@media (max-width: 991px) {
  .wpo-fun-fact-section .grid h3 + p,
  .wpo-fun-fact-section-s2 .grid h3 + p {
    font-size: 16px;
    font-size: 1.06667rem;
  }
}

/* 7.3 wpo-courses-section-s3 */
.wpo-courses-section-s4 {
  background: #F5F5F5;
}

.wpo-courses-section-s4 .row.wpo-courses-wrap .wpo-courses-item {
  background: none;
  border: 2px solid #625F71;
}

.wpo-courses-section-s4 .row.wpo-courses-wrap .wpo-courses-item .wpo-courses-text .courses-icon {
  background: #EEEEEE;
  color: #625F71;
  border: 1px solid #625F71;
}

.wpo-courses-section-s4 .row.wpo-courses-wrap .grid:hover .wpo-courses-item {
  background: #1A1729;
}

.wpo-courses-section-s4 .row.wpo-courses-wrap .grid:hover .wpo-courses-item .wpo-courses-text .courses-icon {
  color: #1A1729;
}

/* 7.4 wpo-popular-area-s3 */
.wpo-popular-area-s3 {
  background: #fff;
}

.wpo-popular-area-s3 .wpo-popular-item {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 2px solid #625F71;
}

.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-img img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
  border: 2px solid #625F71;
}

.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-img .thumb {
  border: 1px solid #625F71;
}

.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-img .thumb span {
  background: #1A1729 !important;
}

.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content .wpo-popular-text-bottom {
  border-top: 2px solid #625F71;
}

.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-text-top ul li img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}

.wpo-popular-area-s3 .wpo-popular-item .wpo-popular-content h2 a:hover {
  color: #1A1729;
}

/* 7.5 wpo-testimonial-section-s2 */
.wpo-testimonial-section-s2 {
  background: #F5F5F5;
}

.wpo-testimonial-section-s2 .wpo-testimonial-wrap .wpo-testimonial-item .wpo-testimonial-text .fi.quote {
  color: #625F71;
}

.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-] {
  color: #1A1729;
}

.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-] .fi {
  color: #1A1729;
}

.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-].owl-next .fi, .wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-].owl-prev .fi {
  color: #1A1729;
}

.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-]:hover {
  background: #1A1729;
}

.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-]:hover.owl-next, .wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-]:hover.owl-prev {
  border-color: #1A1729;
}

.wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-]:hover.owl-next .fi, .wpo-testimonial-section-s2 .wpo-testimonial-wrap .owl-nav [class*=owl-]:hover.owl-prev .fi {
  color: #fff;
}

.wpo-testimonial-section-s2 .wpo-testimonial-text-btm-img img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}

/* 7.6 wpo-blog-section-s2 */
.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-img img {
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}

.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content a.more {
  color: #625F71;
  border-color: #625F71;
}

.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content h2 a:hover {
  color: #1A1729;
}

.wpo-blog-section-s2 .wpo-blog-item .wpo-blog-content ul li a:hover {
  color: #1A1729;
}

/*======================================
8. Home-style-5
 =======================================*/
/* 8.1 wpo-features-area-s3 */
.wpo-features-area-s3 .features-wrap {
  -webkit-box-shadow: none;
          box-shadow: none;
  background: none;
  max-width: 1320px;
  margin-top: -200px;
}

@media (max-width: 1399px) {
  .wpo-features-area-s3 .features-wrap {
    margin-top: -100px;
  }
}

@media (max-width: 991px) {
  .wpo-features-area-s3 .features-wrap {
    margin-top: 0px;
  }
}

.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item {
  background: #F2BC00;
  border-radius: 10px;
  text-align: center;
  border-radius: 162px 162px 10px 10px;
  padding: 60px 30px 30px;
}

.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item h2 a {
  color: #fff;
}

@media (max-width: 1199px) {
  .wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item h2 {
    font-size: 25px;
  }
}

.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item p {
  color: #fff;
}

@media (max-width: 991px) {
  .wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item {
    margin-bottom: 20px;
  }
}

.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item .icon {
  background: #fff;
}

.wpo-features-area-s3 .features-wrap .feature-item-wrap .feature-item .icon .fi {
  color: #F2BC00;
}

.wpo-features-area-s3 .features-wrap .col {
  padding-right: calc(var(--bs-gutter-x)/ 2);
  padding-left: calc(var(--bs-gutter-x)/ 2);
}

.wpo-features-area-s3 .features-wrap .col:nth-child(1) .feature-item {
  border-radius: 162px 162px 10px 10px;
}

@media (max-width: 991px) {
  .wpo-features-area-s3 .features-wrap .col:nth-child(1) .feature-item {
    border-radius: 0;
  }
}

.wpo-features-area-s3 .features-wrap .col:nth-child(2) .feature-item {
  background: #4540E1;
}

.wpo-features-area-s3 .features-wrap .col:nth-child(2) .feature-item .icon {
  background: #fff;
}

.wpo-features-area-s3 .features-wrap .col:nth-child(2) .feature-item .icon .fi {
  color: #4540E1;
}

.wpo-features-area-s3 .features-wrap .col:nth-child(3) .feature-item {
  background: #15A280;
}

.wpo-features-area-s3 .features-wrap .col:nth-child(3) .feature-item .icon {
  background: #fff;
}

.wpo-features-area-s3 .features-wrap .col:nth-child(3) .feature-item .icon .fi {
  color: #15A280;
}

.wpo-features-area-s3 .features-wrap .col:nth-child(4) .feature-item {
  background: #F9406C;
}

.wpo-features-area-s3 .features-wrap .col:nth-child(4) .feature-item .icon {
  background: #fff;
}

.wpo-features-area-s3 .features-wrap .col:nth-child(4) .feature-item .icon .fi {
  color: #F9406C;
}

/* 8.2 wpo-about-section-s5 */
.wpo-about-section-s5 {
  padding: 150px 0;
}

@media (max-width: 1199px) {
  .wpo-about-section-s5 {
    padding: 100px 0;
  }
}

@media (max-width: 575px) {
  .wpo-about-section-s5 {
    padding: 80px 0;
  }
}

.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img {
  margin-left: -130px;
  max-width: 100%;
  padding-right: 0;
}

@media (max-width: 1600px) {
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img {
    margin-left: -40px;
  }
}

@media (max-width: 991px) {
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img {
    margin-right: 0;
    margin-left: 0;
    max-width: 450px;
    margin: 0 auto;
    margin-bottom: 50px;
  }
}

@media (max-width: 500px) {
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img {
    max-width: 300px;
  }
}

.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img img {
  width: auto;
  max-width: 635px;
  width: 100%;
}

.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img .frame {
  position: absolute;
  left: -105px;
  top: -72px;
  width: 135%;
  height: 100%;
}

@media (max-width: 1199px) {
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img .frame {
    left: -75px;
    top: -60px;
  }
}

@media (max-width: 500px) {
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-img .frame {
    left: -50px;
    top: -40px;
  }
}

.wpo-about-section-s5 .wpo-about-wrap .wpo-about-img .frame img {
  max-width: 100%;
  width: auto;
}

@media (max-width: 991px) {
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-text {
    text-align: center;
  }
}

@media (max-width: 991px) {
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-section-title {
    text-align: center;
  }
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-text .wpo-section-title h2 {
    text-align: center;
  }
}

.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text ul {
  list-style: none;
  margin-top: 30px;
}

.wpo-about-section-s5 .wpo-about-wrap .wpo-about-text ul li {
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  padding-bottom: 15px;
}

@media (max-width: 575px) {
  .wpo-about-section-s5 .wpo-about-wrap .wpo-about-text ul li {
    font-size: 18px;
  }
}

/* 8.3 wpo-fun-fact-section-s2 */
.wpo-fun-fact-section-s2 {
  background: url(../../images/noise.png), #F9406C 50%/cover no-repeat;
  padding: 80px 0;
  overflow: hidden;
}

@media (max-width: 767px) {
  .wpo-fun-fact-section-s2 {
    padding-bottom: 40px;
  }
}

.wpo-fun-fact-section-s2 .wpo-fun-fact-grids {
  background: none;
  border: 0;
  padding: 0;
}

.wpo-fun-fact-section-s2 .wpo-fun-fact-grids .grid h3 {
  color: #fff;
}

.wpo-fun-fact-section-s2 .wpo-fun-fact-grids .grid p {
  color: #fff;
}

.wpo-fun-fact-section-s2 .shape {
  position: absolute;
  top: -15px;
  width: 100%;
}

@media (max-width: 1399px) {
  .wpo-fun-fact-section-s2 .shape {
    top: -12px;
    width: 120%;
  }
}

@media (max-width: 991px) {
  .wpo-fun-fact-section-s2 .shape {
    top: -12px;
    width: 150%;
  }
}

@media (max-width: 575px) {
  .wpo-fun-fact-section-s2 .shape {
    width: 200%;
  }
}

.wpo-fun-fact-section-s2 .shape-2 {
  position: absolute;
  bottom: -15px;
  width: 100%;
}

@media (max-width: 1399px) {
  .wpo-fun-fact-section-s2 .shape-2 {
    bottom: -12px;
    width: 120%;
  }
}

@media (max-width: 991px) {
  .wpo-fun-fact-section-s2 .shape-2 {
    bottom: -12px;
    width: 150%;
  }
}

@media (max-width: 575px) {
  .wpo-fun-fact-section-s2 .shape-2 {
    width: 200%;
  }
}

/* 8.4 wpo-courses-section-s5 */
.wpo-courses-section-s5 {
  background: #fff;
}

.wpo-courses-section-s5 .wpo-courses-wrap .wpo-courses-item {
  border-radius: 10px;
  background: #fff;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
}

/* 8.5 wpo-popular-area-s4 */
.wpo-popular-area-s4 {
  background: #FFF8F9;
}

.wpo-popular-area-s4 .vector-1 {
  position: absolute;
  top: 50%;
  left: 12%;
  z-index: -1;
}

@media (max-width: 1199px) {
  .wpo-popular-area-s4 .vector-1 {
    top: -8%;
  }
}

@media (max-width: 575px) {
  .wpo-popular-area-s4 .vector-1 {
    max-width: 10%;
  }
}

.wpo-popular-area-s4 .vector-2 {
  position: absolute;
  top: 10%;
  left: 20%;
  z-index: -1;
}

@media (max-width: 1199px) {
  .wpo-popular-area-s4 .vector-2 {
    left: 2%;
    top: 15%;
  }
}

@media (max-width: 575px) {
  .wpo-popular-area-s4 .vector-2 {
    max-width: 10%;
  }
}

.wpo-popular-area-s4 .vector-3 {
  position: absolute;
  bottom: 10%;
  left: 3%;
  z-index: -1;
}

@media (max-width: 575px) {
  .wpo-popular-area-s4 .vector-3 {
    max-width: 10%;
  }
}

.wpo-popular-area-s4 .vector-4 {
  position: absolute;
  bottom: 12%;
  left: 30%;
  z-index: -1;
}

@media (max-width: 575px) {
  .wpo-popular-area-s4 .vector-4 {
    max-width: 10%;
  }
}

.wpo-popular-area-s4 .vector-5 {
  position: absolute;
  bottom: 8%;
  left: 70%;
  z-index: -1;
}

@media (max-width: 575px) {
  .wpo-popular-area-s4 .vector-5 {
    max-width: 10%;
  }
}

.wpo-popular-area-s4 .vector-6 {
  position: absolute;
  top: 10%;
  right: 20%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: -1;
}

@media (max-width: 575px) {
  .wpo-popular-area-s4 .vector-6 {
    max-width: 10%;
  }
}

.wpo-popular-area-s4 .vector-7 {
  position: absolute;
  bottom: 30%;
  right: -13%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: -1;
}

@media (max-width: 575px) {
  .wpo-popular-area-s4 .vector-7 {
    max-width: 10%;
  }
}

.wpo-popular-area-s4 .vector-8 {
  position: absolute;
  bottom: 28%;
  right: 20px;
  z-index: -1;
}

@media (max-width: 575px) {
  .wpo-popular-area-s4 .vector-8 {
    max-width: 10%;
  }
}

.wpo-popular-area-s4 .vector-9 {
  position: absolute;
  top: 40%;
  right: 2%;
  z-index: -1;
}

@media (max-width: 575px) {
  .wpo-popular-area-s4 .vector-9 {
    max-width: 10%;
  }
}

.wpo-popular-area-s4 .vector-10 {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
}

.wpo-popular-area-s4 .vector-11 {
  position: absolute;
  left: 6%;
  top: 20%;
  z-index: -1;
}

.wpo-popular-area-s4 .shape-1 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: -11;
}

@media (max-width: 1199px) {
  .wpo-popular-area-s4 .shape-1 {
    max-width: 50%;
  }
}

.wpo-popular-area-s4 .shape-2 {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -11;
}

@media (max-width: 1199px) {
  .wpo-popular-area-s4 .shape-2 {
    max-width: 50%;
  }
}

.more-btn {
  margin-top: 20px;
}

/*--------------------------------------------------------------
9. wpo-course-page
--------------------------------------------------------------*/
/*------------------------------------
	course sidebar
------------------------------------*/
.wpo-course-sidebar {
  padding: 20px;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
  border-radius: 10px;
  /*** widget_price_filter ***/
}

@media (max-width: 991px) {
  .wpo-course-sidebar {
    margin-top: 50px;
  }
}

.wpo-course-sidebar .widget h3 {
  font-size: 18px;
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0 0 1.5em;
  position: relative;
  text-transform: none;
  text-transform: uppercase;
  font-family: "Urbanist";
}

.wpo-course-sidebar > .widget + .widget {
  margin-top: 45px;
}

.wpo-course-sidebar .product-categories .cat-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  margin-top: 5px;
  position: relative;
  padding-bottom: 5px;
  border-bottom: 1px solid #ebebeb;
}

.wpo-course-sidebar .product-categories .cat-item input {
  margin-right: 10px;
}

.wpo-course-sidebar .product-categories .cat-item label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-preferred-size: 90%;
      flex-basis: 90%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-course-sidebar .product-categories .cat-item label span {
  display: block;
  width: 35px;
  height: 35px;
  line-height: 35px;
  background: #f7f7f7;
  text-align: center;
  border-radius: 50%;
}

.wpo-course-sidebar .product-categories .cat-item.last {
  border: 0;
}

.wpo-course-sidebar .widget_price_filter .ui-corner-all,
.wpo-course-sidebar .widget_price_filter .ui-corner-bottom,
.wpo-course-sidebar .widget_price_filter .ui-corner-right,
.wpo-course-sidebar .widget_price_filter .ui-corner-br {
  border-radius: 30px;
}

.wpo-course-sidebar .widget_price_filter .filter-price {
  position: relative;
  font-size: 14px;
}

.wpo-course-sidebar .widget_price_filter .filter-price input {
  border: 0;
  padding: 0;
  display: inline-block;
  width: auto;
  position: absolute;
}

.wpo-course-sidebar .widget_price_filter .filter-price input:focus {
  border: 0;
  outline: none;
}

.wpo-course-sidebar .widget_price_filter .filter-price button {
  font-family: "Urbanist";
  background: transparent;
  color: #4540E1;
  font-size: 18px;
  font-size: 1.2rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0;
  display: inline-block;
  border: 0;
  outline: none;
  position: absolute;
  right: 0;
  z-index: 99;
  text-decoration: underline;
}

.wpo-course-sidebar .widget_price_filter .filter-price button:hover, .wpo-course-sidebar .widget_price_filter .filter-price button:focus, .wpo-course-sidebar .widget_price_filter .filter-price button:active {
  text-decoration: underline;
}

.wpo-course-sidebar .widget_price_filter .filter-price #amount {
  color: #000;
}

.wpo-course-sidebar .widget_price_filter #slider-range {
  height: 3px;
  background: #FCF9EF;
  border: 0;
  border-radius: 50px;
  margin-bottom: 25px;
  margin-left: 8px;
}

.wpo-course-sidebar .widget_price_filter .ui-state-default,
.wpo-course-sidebar .widget_price_filter .ui-widget-content .ui-state-default,
.wpo-course-sidebar .widget_price_filter .ui-widget-header .ui-state-default {
  width: 10px;
  height: 10px;
  border: none;
  background: #F2BC00;
  top: -4px;
}

.wpo-course-sidebar .widget_price_filter .ui-state-default:focus,
.wpo-course-sidebar .widget_price_filter .ui-widget-content .ui-state-default:focus,
.wpo-course-sidebar .widget_price_filter .ui-widget-header .ui-state-default:focus {
  outline: none;
}

.wpo-course-sidebar .widget_price_filter .ui-widget-header {
  background: #F2BC00;
}

.wpo-course-page {
  /*** search-widget ***/
}

.wpo-course-page .search-widget form div {
  position: relative;
}

.wpo-course-page .search-widget input {
  font-size: 14px;
  font-size: 0.93333rem;
  width: 100%;
  height: 60px;
  border: 1px solid #DDDDDD;
  padding: 10px;
}

.wpo-course-page .search-widget input:focus {
  outline: none;
  border-color: #4540E1;
}

.wpo-course-page .search-widget form button {
  background-color: #4540E1;
  color: #fff;
  padding: 0;
  border: 0;
  outline: 0;
  position: absolute;
  right: 0px;
  top: 0px;
  border-radius: 0;
  width: 60px;
  height: 60px;
}

.wpo-course-page .search-widget form button i {
  font-size: 18px;
  font-size: 1.2rem;
}

.wpo-course-page .wpo-product-section {
  padding: 0;
  padding-left: 30px;
}

@media (max-width: 991px) {
  .wpo-course-page .wpo-product-section {
    padding-left: 0;
  }
}

.wpo-course-page .wpo-product-section .wpo-popular-item .wpo-popular-img {
  position: relative;
}

.wpo-course-page .wpo-product-section .wpo-popular-item .wpo-popular-img .cart-btn {
  position: absolute;
  left: 50%;
  top: 52%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  padding: 10px 25px;
  text-transform: capitalize;
  border-radius: 6px;
  background: #F9406C;
  color: #fff;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all .3s;
  transition: all .3s;
  border: 5px solid #fff;
}

.wpo-course-page .wpo-product-section .wpo-popular-item:hover .wpo-popular-img .cart-btn {
  opacity: 1;
  visibility: visible;
}

.wpo-course-page .sorting-section {
  padding-bottom: 20px;
}

.wpo-course-page .sorting-section ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  list-style: none;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.wpo-course-page .sorting-section ul li {
  margin: 10px 0;
}

.wpo-course-page .sorting-section .nice-select ul {
  display: block;
}

.wpo-course-page .sorting-section .filter button {
  background: #4540E1;
  padding: 10px 25px;
  border: 0;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 5px;
}

.wpo-course-page .sorting-section .filter button i {
  margin-left: 10px;
}

.wpo-course-page.s2 .wpo-product-section {
  padding-left: 0;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.wpo-course-page.s2 .wpo-course-sidebar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 30px;
  margin-top: 0;
}

@media (max-width: 991px) {
  .wpo-course-page.s2 .wpo-course-sidebar {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}

.wpo-course-page.s2 .wpo-course-sidebar .widget {
  -ms-flex-preferred-size: 25%;
      flex-basis: 25%;
  padding: 0 20px;
  margin-top: 0;
}

@media (max-width: 991px) {
  .wpo-course-page.s2 .wpo-course-sidebar .widget {
    -ms-flex-preferred-size: 50%;
        flex-basis: 50%;
    margin-bottom: 20px;
  }
}

@media (max-width: 500px) {
  .wpo-course-page.s2 .wpo-course-sidebar .widget {
    -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    padding: 0 10px;
  }
}

.wpo-course-page.s2 .wpo-course-sidebar .widget ul li:last-child {
  border: 0;
}

@media (max-width: 1199px) {
  .wpo-course-page.s2 .wpo-course-sidebar .widget ul li {
    font-size: 15px;
  }
}

@media (max-width: 1199px) {
  .wpo-course-page.s2 .wpo-course-sidebar .widget h3 {
    font-size: 16px;
  }
}

.wpo-course-page.s2 .search-widget input {
  height: 45px;
}

.wpo-course-page.s2 .search-widget button {
  height: 45px;
  line-height: 50px;
}

.wpo-course-page.s2 #filterOpen {
  display: none;
}

.wpo-course-page.s2 .sorting-section {
  padding: 10px 20px;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
  margin-bottom: 30px;
  border-radius: 10px;
}

.wpo-course-page.s2 .sorting-section ul li:nth-child(2) {
  -ms-flex-preferred-size: 30%;
      flex-basis: 30%;
}

@media (max-width: 500px) {
  .wpo-course-page.s2 .sorting-section ul li:nth-child(2) {
    -ms-flex-preferred-size: 58%;
        flex-basis: 58%;
  }
}

.wpo-course-page.s3 .wpo-product-section {
  padding-left: 0;
  padding-right: 20px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

/*----------------------------------------------
10. course-details-page
----------------------------------------------*/
.wpo-course-details-tab {
  padding: 15px 30px;
  border: 1px solid #ebebeb;
  margin-top: 40px;
}

.wpo-course-details-tab .nav li a {
  display: block;
  width: 160px;
  height: 45px;
  line-height: 30px;
  text-align: center;
  border-radius: 40px;
  color: #2d4277;
  -webkit-transition: all .5s;
  transition: all .5s;
  font-weight: 600;
  -webkit-box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.1);
  margin-right: 10px;
  border: none;
  text-transform: capitalize;
}

.wpo-course-details-tab .nav li a.active,
.wpo-course-details-tab .nav li a:hover {
  background: #F2BC00;
  color: #fff;
  outline: none;
  border-radius: 40px;
  border: none;
}

.nav-tabs {
  border-bottom: none;
}

@media (max-width: 767px) {
  .nav-tabs {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.wpo-course-details-text {
  margin-top: 50px;
}

.course-b-text {
  margin-top: 40px;
}

#Description {
  padding: 15px 30px;
  border: 1px solid #ebebeb;
}

@media (max-width: 575px) {
  #Description {
    padding: 15px 2px;
  }
}

.wpo-course-details-text .wpo-course-text-top p {
  margin-bottom: 30px;
}

.wpo-course-details-tab .event-b-text {
  margin-top: 40px;
}

.wpo-course-details-text .wpo-course-content h2 {
  font-size: 30px;
  margin-bottom: 20px;
}

.wpo-course-details-text .wpo-course-content h3 {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 20px;
}

.course-bb-text ul {
  display: unset;
  list-style: none;
}

.course-bb-text ul li {
  font-size: 18px;
  position: relative;
  padding-left: 20px;
  padding-bottom: 10px;
}

.course-bb-text ul li:before {
  content: "";
  background: #4540E1;
  width: 6px;
  height: 6px;
  position: absolute;
  left: 0;
  top: 7px;
}

.wpo-course-details-area .wpo-blog-single-section .comments-area {
  margin-top: 0;
}

@media (max-width: 992px) {
  .wpo-event-details-wrap {
    margin-bottom: 30px;
  }
}

.wpo-course-details-text .wpo-course-content .course-curriculam {
  margin-top: 20px;
}

.wpo-course-details-text .wpo-course-content .course-curriculam ul {
  list-style: none;
}

.wpo-course-details-text .wpo-course-content .course-curriculam ul li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid #D9D9D9;
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.wpo-course-details-text .wpo-course-content .course-curriculam ul li span {
  font-size: 20px;
  color: #1A1729;
  font-weight: 700;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-course-details-text .wpo-course-content .course-curriculam ul li span a {
  font-size: 16px;
  display: inline-block;
  padding: 5px 10px;
  background: #F9406C;
  color: #fff;
  margin-left: 10px;
  border-radius: 20px;
}

.wpo-course-details-text .wpo-course-content .course-curriculam ul li span i {
  margin-right: 10px;
  color: #F9406C;
}

.wpo-course-details-text .wpo-course-content .course-curriculam ul li span i.flaticon-play-button {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border: 1px solid #F9406C;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  color: #F9406C;
}

.wpo-course-details-text .wpo-course-content .course-curriculam ul li span i.flaticon-e-learning, .wpo-course-details-text .wpo-course-content .course-curriculam ul li span i.flaticon-knowledge {
  font-size: 30px;
}

.wpo-course-details-text .wpo-course-content .course-curriculam ul li small {
  font-size: 18px;
}

@media (max-width: 590px) {
  .wpo-course-details-text .wpo-course-content h2 {
    font-size: 18px;
  }
  .wpo-course-details-tab .nav li a {
    margin-bottom: 20px;
  }
  .wpo-course-details-tab .nav li:last-child a {
    margin-bottom: 0;
  }
  .wpo-course-details-text .wpo-course-text-top {
    padding: 8px;
  }
}

.features-widget {
  padding: 20px;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
  border-radius: 5px;
}

.features-widget .features-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-bottom: 20px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.features-widget .features-top h4 {
  font-size: 22px;
  margin-bottom: 0;
  font-weight: 700;
}

.features-widget .features-top h4 del {
  color: #6b7385;
  font-weight: 400;
}

.features-widget .features-top span {
  display: inline-block;
  padding: 5px 15px;
  background: #EEF9F6;
}

.features-widget .cart-btn a {
  width: 100%;
}

.features-widget ul {
  list-style: none;
  margin-top: 30px;
}

.features-widget ul li {
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid #ebeaea;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.features-widget ul li span {
  display: inline-block;
  padding: 3px 10px;
  background: #f7f7f7;
  border-radius: 3px;
  font-size: 15px;
}

.features-widget ul li:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: 0;
}

.wpo-course-details-area .recent-post-widget {
  padding: 20px;
  -webkit-box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
          box-shadow: 0px 2px 8px 0px rgba(0, 12, 55, 0.1);
  border-radius: 5px;
}

.wpo-course-details-area .course-sitebar {
  min-height: 100%;
}

.wpo-course-details-area .course-sitebar .blog-sidebar {
  position: -webkit-sticky;
  position: sticky;
  top: 100px;
}

@media (max-width: 1199px) {
  .wpo-course-details-area .course-sitebar .blog-sidebar {
    padding-left: 0;
  }
}

/*--------------------------------------------------------------
11. wpo-lesson-section
--------------------------------------------------------------*/
.wpo-lesson-section .container-fluid {
  padding: 0;
}

.wpo-lesson-section .col {
  padding: 0;
}

.wpo-lesson-section .wpo-lesson-sidebar {
  height: 100%;
  border-right: 1px solid #e1e1e1;
}

@media (max-width: 991px) {
  .wpo-lesson-section .wpo-lesson-sidebar {
    height: auto;
  }
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item {
  border: 0;
  border: 1px solid #e1e1e1;
  border-top: 0;
  border-right: 0;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body {
  padding-left: 0;
  padding-right: 0;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item button {
  padding: 20px;
  border: 0;
  border-radius: 0;
  font-size: 18px;
  font-weight: 700;
  color: #fff;
  text-align: left;
  font-family: "Urbanist";
  background: #1A1729;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (max-width: 991px) {
  .wpo-lesson-section .wpo-lesson-sidebar .accordion-item button {
    font-size: 15px;
  }
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item button span {
  font-size: 14px;
  display: inline-block;
  margin-left: 20px;
  position: relative;
  top: 2px;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item button::after {
  background: none;
  font-family: "themify";
  content: "\e622";
  font-size: 15px;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  font-weight: 700;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item button.collapsed {
  color: #373b3e;
  background: #fff;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item button.collapsed::after {
  content: "\e61a";
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item button:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body {
  padding: 20px;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item {
  list-style: none;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li {
  color: #1A1729;
  margin-bottom: 20px;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li:last-child {
  margin-bottom: 0;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  color: #1A1729;
  margin-left: 5px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a i.flaticon-play-button {
  width: 22px;
  height: 22px;
  line-height: 20px;
  border: 1px solid #F9406C;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  color: #F9406C;
  margin-right: 10px;
  font-size: 12px;
  margin-left: 10px;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a span i {
  margin-left: 10px;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a .fa-check-circle {
  color: #4540E1;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a .fa-circle-thin {
  color: #cfc9c9;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-collapse {
  border: 0;
}

.wpo-lesson-section .video-area {
  height: 100%;
}

@media (max-width: 991px) {
  .wpo-lesson-section .video-area {
    height: auto;
  }
}

.wpo-lesson-section .video-area .video-heading {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 8px 20px;
  background: #1A1729;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.wpo-lesson-section .video-area .video-heading h2 {
  font-size: 20px;
  font-weight: 700;
  color: #fff;
}

.wpo-lesson-section .video-area .video-heading .theme-btn, .wpo-lesson-section .video-area .video-heading .view-cart-btn {
  padding: 10px 25px;
}

.wpo-lesson-section .video-area video {
  width: 100%;
}

@media (max-width: 991px) {
  .wpo-lesson-section .video-area video {
    height: auto;
    -o-object-fit: contain;
       object-fit: contain;
  }
}

.wpo-lesson-section .video-area .video-details {
  max-width: 800px;
  padding: 30px;
}

.wpo-lesson-section .video-area .video-details h2 {
  font-size: 30px;
  font-weight: 600;
  margin-bottom: 10px;
}

.wpo-lesson-section .video-area .video-details-pagination {
  padding: 20px;
  background: #FCF9EF;
}

.wpo-lesson-section .video-area .video-details-pagination ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  list-style: none;
}

.wpo-lesson-section .video-area .video-details-pagination ul li {
  margin: 10px;
}

.wpo-lesson-section .video-area .video-details-pagination ul li a {
  display: block;
  padding: 10px 30px;
  background: #ededed;
  color: #625F71;
  border-radius: 5px;
}

.wpo-lesson-section .video-area .video-details-pagination ul li a:hover {
  background: #4540E1;
  color: #fff;
}

/*--------------------------------------------------------------
12. teacher-page
--------------------------------------------------------------*/
.teacher-wrap {
  padding-bottom: 120px;
}

.volunter-img {
  padding: 30px;
}

.volunter-img img {
  width: 100%;
}

.teacher-img-wrap {
  -webkit-box-shadow: 0px 1px 14px 0px rgba(0, 0, 0, 0.15);
          box-shadow: 0px 1px 14px 0px rgba(0, 0, 0, 0.15);
}

.teacher-contact-form form input,
.teacher-contact-form form select,
.teacher-contact-form form label,
.teacher-contact-form form textarea {
  border: 1px solid #d0d4df;
  padding-left: 15px;
  height: 50px;
  width: 100%;
}

.teacher-contact-form form label {
  line-height: 50px;
  font-weight: 400;
  font-size: 14px;
}

.teacher-contact-form form label.error {
  height: 15px;
  border: 0;
  line-height: 10px;
  padding-left: 0;
}

.teacher-contact-form .form-group-in input {
  opacity: 0;
  visibility: hidden;
  position: absolute;
}

.teacher-contact-form form textarea {
  height: 100px;
}

.teacher-contact-form .form-group-in {
  position: relative;
}

.teacher-contact-form .form-group-in i {
  position: absolute;
  right: 30px;
  top: 17px;
  font-size: 20px;
}

.teacher-contact-form .form-group {
  margin-bottom: 30px;
}

.teacher-contact-form {
  max-width: 760px;
  margin: auto;
  padding: 50px 40px;
  background: #fff;
  margin-top: -295px;
  position: relative;
  z-index: 1;
}

.teacher-contact-form:after {
  position: absolute;
  left: 0px;
  bottom: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: #fff;
  z-index: -1;
}

.teacher-contact-form:before {
  position: absolute;
  left: -7px;
  bottom: -8px;
  width: 102%;
  height: 47%;
  content: "";
  background: #ffc4ad;
  z-index: -1;
}

.teacher-contact-form h2 {
  margin-top: 0;
  font-size: 36px;
  text-align: center;
  margin-bottom: 40px;
}

@media (max-width: 1200px) {
  .teacher-contact-form {
    max-width: 635px;
    margin-top: -210px;
  }
  .teacher-contact-form:before {
    height: 62%;
    width: 103%;
  }
}

@media (max-width: 767px) {
  .teacher-contact-form {
    margin-top: 0px;
    padding: 14px 16px;
    -webkit-box-shadow: 0px 1px 14px 0px rgba(0, 0, 0, 0.15);
            box-shadow: 0px 1px 14px 0px rgba(0, 0, 0, 0.15);
    margin-top: 50px;
    padding-top: 30px;
  }
  .teacher-contact-form h2 {
    font-size: 23px;
  }
  .teacher-area {
    padding: 70px 0 0;
  }
}

.teacher-contact-form .submit-area {
  text-align: center;
}

/*==========================================
13. wpo-team-single
===========================================*/
.team-pg-area {
  margin: 0 auto;
  padding-top: 200px;
  padding-bottom: 100px;
}

@media (max-width: 1199px) {
  .team-pg-area {
    padding: 100px 0;
  }
}

@media (max-width: 991px) {
  .team-pg-area {
    padding: 90px 0;
  }
}

@media (max-width: 767px) {
  .team-pg-area {
    padding: 70px 0;
  }
}

.team-single-wrap {
  background: #fff;
  padding: 100px;
  padding-top: 0;
  -webkit-box-shadow: 0px 1px 15px 0px rgba(62, 65, 159, 0.15);
          box-shadow: 0px 1px 15px 0px rgba(62, 65, 159, 0.15);
}

@media (max-width: 1199px) {
  .team-single-wrap {
    padding: 50px;
  }
}

@media (max-width: 767px) {
  .team-single-wrap {
    padding: 30px;
  }
}

@media (max-width: 575px) {
  .team-single-wrap {
    padding: 15px;
  }
}

.team-info-img {
  margin-top: -100px;
  max-width: 480px;
}

@media (max-width: 1199px) {
  .team-info-img {
    margin-top: 0;
  }
}

@media (max-width: 991px) {
  .team-info-img {
    max-width: 100%;
  }
}

.team-info-img img {
  width: 100%;
}

.team-info-text {
  padding-left: 30px;
  padding-top: 30px;
}

@media (max-width: 991px) {
  .team-info-text {
    padding-left: 0;
  }
}

.team-info-text h2 {
  font-weight: 400;
  font-size: 40px;
  line-height: 55px;
  color: #1A1729;
  margin-top: 0;
  margin-bottom: 30px;
}

@media (max-width: 575px) {
  .team-info-text h2 {
    font-size: 30px;
    line-height: 45px;
  }
}

.team-info-text ul li {
  font-weight: 400;
  font-size: 20px;
  line-height: 27px;
  color: #001635;
  margin-bottom: 15px;
  list-style: none;
  font-family: "Urbanist";
}

@media (max-width: 575px) {
  .team-info-text ul li {
    font-size: 16px;
    line-height: 27px;
  }
}

.team-info-text ul li:last-child {
  padding-bottom: 0;
}

.team-info-text ul li span {
  font-size: 18px;
  color: #8c8c8c;
  display: inline-block;
  position: relative;
  padding-left: 5px;
  font-family: "Urbanist";
}

.team-info-text ul li:last-child {
  margin-bottom: 0;
}

.team-info-text .certificates-wrap {
  margin-top: 40px;
}

.team-info-text .certificates-wrap h2 {
  font-weight: 400;
  font-size: 27px;
  line-height: 37px;
}

.team-info-text .certificates-wrap .certificates-items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.team-info-text .certificates-wrap .certificates-items .certificates-item + .certificates-item {
  margin-left: 20px;
}

.team-widget {
  padding-top: 40px;
  margin-top: 40px;
  border-top: 1px solid #D9D9D9;
}

.team-widget h2 {
  font-weight: 400;
  font-size: 35px;
  line-height: 48px;
  margin-bottom: 30px;
}

@media (max-width: 767px) {
  .team-widget h2 {
    font-size: 25px;
    line-height: 35px;
    margin-bottom: 15px;
  }
}

.team-exprience-area .exprience-wrap {
  max-width: 560px;
}

.team-exprience-area .education-area {
  padding-left: 50px;
}

@media (max-width: 991px) {
  .team-exprience-area .education-area {
    padding-left: 0;
    margin-bottom: 40px;
  }
}

.team-exprience-area .education-area .education-area {
  max-width: 600px;
}

.team-exprience-area .education-area ul {
  list-style: none;
}

.team-exprience-area .education-area ul li {
  padding-bottom: 15px;
  position: relative;
  padding-left: 30px;
  font-weight: 400;
  font-size: 18px;
  line-height: 30px;
}

.team-exprience-area .education-area ul li:before {
  position: absolute;
  left: 0;
  top: 5px;
  content: "\e661";
  font-family: 'themify';
  font-size: 14px;
}

.team-exprience-area .education-area .ex-wiget {
  padding-top: 60px;
}

.team-exprience-area .education-area .ex-wiget h2 {
  font-size: 30px;
  font-weight: 700;
  color: #333333;
  margin-bottom: 30px;
}

.skills-area {
  padding-top: 40px;
  margin-bottom: 40px;
  padding-bottom: 40px;
  border-top: 1px solid #D9D9D9;
  border-bottom: 1px solid #D9D9D9;
}

@media (max-width: 991px) {
  .skills-area {
    padding-top: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;
  }
}

.skills-area .wpo-skill-progress .wpo-progress-single {
  margin-bottom: 30px;
  position: relative;
}

.skills-area .wpo-skill-progress .wpo-progress-single h5 {
  font-weight: 400;
  font-size: 18px;
  line-height: 23px;
  color: #525252;
  font-family: "Urbanist";
}

@media (max-width: 767px) {
  .skills-area .wpo-skill-progress .wpo-progress-single h5 {
    font-size: 18px;
  }
}

.skills-area .wpo-skill-progress .wpo-progress-single:last-child {
  margin-bottom: 0;
}

.skills-area .wpo-skill-progress .wpo-progress-single .progress {
  background: #FCF9EF;
  height: 6px;
  position: relative;
}

.skills-area .wpo-skill-progress .wpo-progress-single .progress .progress-bar {
  background-color: #4540E1;
}

.skills-area .wpo-skill-progress .progress-number {
  position: absolute;
  right: 0;
  top: -10px;
  font-weight: 400;
  font-size: 18px;
  line-height: 23px;
  color: #525252;
}

@media (max-width: 767px) {
  .skills-area .wpo-skill-progress .progress-number {
    font-size: 18px;
    top: -5px;
  }
}

.exprience-wrap p {
  color: #5d5851;
  margin-bottom: 30px;
}

@media (max-width: 991px) {
  .exprience-wrap p {
    margin-bottom: 15px;
  }
}

.exprience-wrap {
  padding-bottom: 40px;
}

.wpo-contact-area {
  padding-top: 50px;
}

@media (max-width: 991px) {
  .wpo-contact-area {
    padding-top: 20px;
  }
}

.wpo-contact-area .wpo-contact-title {
  max-width: 580px;
  margin: 0 auto;
  margin-bottom: 40px;
  text-align: center;
}

.wpo-contact-area .quote-form {
  max-width: 890px;
  margin: 0 auto;
}

.wpo-contact-area .quote-form .form-control {
  height: 55px;
  border: 0;
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f5f5;
}

.wpo-contact-area .quote-form .form-control:focus {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #4540E1;
}

.wpo-contact-area .quote-form textarea.form-control {
  height: 125px;
}

.wpo-contact-area .quote-form select.form-control {
  -webkit-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  appearance: none;
  -moz-appearance: none;
  background: #f5f5f5 url(../../images/select-icon2.png) no-repeat calc(100% - 15px) center;
  position: relative;
}

.half-col {
  float: left;
  width: 50%;
  padding: 0 10px 5px;
}

@media (max-width: 575px) {
  .half-col {
    width: 100%;
  }
}

.full-col {
  padding: 0 10px 5px;
}

.exprience-area {
  padding-top: 60px;
}

/*======================================
14. wpo-gallery-page
 =======================================*/
/* 6.2 wpo-portfolio-section */
.wpo-portfolio-section {
  position: relative;
}

.wpo-portfolio-section .portfolio-grids {
  margin: 0 -7.5px;
}

.wpo-portfolio-section .portfolio-grids .grid {
  width: 33.33%;
  float: left;
  padding: 0 7.5px 15px;
}

@media (max-width: 1500px) {
  .wpo-portfolio-section .portfolio-grids .grid {
    padding: 0 5.5px 10px;
  }
}

@media (max-width: 991px) {
  .wpo-portfolio-section .portfolio-grids .grid {
    padding: 0 4.5px 8px;
  }
}

@media (max-width: 1199px) {
  .wpo-portfolio-section .portfolio-grids .grid {
    width: 50%;
  }
}

@media (max-width: 600px) {
  .wpo-portfolio-section .portfolio-grids .grid {
    width: 100%;
  }
}

.wpo-portfolio-section .portfolio-grids .grid img {
  width: 100%;
}

.wpo-portfolio-section .grid .img-holder {
  position: relative;
}

.wpo-portfolio-section .grid .img-holder .hover-content {
  -webkit-transition: all .3s;
  transition: all .3s;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  opacity: 0;
  text-align: center;
  width: 260px;
}

.wpo-portfolio-section .grid .img-holder .hover-content span {
  color: #6788a7;
}

.wpo-portfolio-section .grid .img-holder .hover-content h4 {
  font-size: 30px;
  font-weight: 600;
}

.wpo-portfolio-section .grid .img-holder .hover-content h4 a {
  color: #1A1729;
}

.wpo-portfolio-section .grid .img-holder .hover-content h4 a:hover {
  color: #4540E1;
}

.wpo-portfolio-section .grid .img-holder .hover-content i {
  font-size: 30px;
  color: #fff;
}

.wpo-portfolio-section .grid .img-holder:before {
  position: absolute;
  left: 2%;
  top: 2%;
  width: 96%;
  height: 96%;
  content: "";
  background: #15A280;
  opacity: 0;
  -webkit-transition: all .3s;
  transition: all .3s;
  -webkit-transform: scale(0);
          transform: scale(0);
}

.wpo-portfolio-section .img-holder:hover .hover-content {
  opacity: 1;
}

.wpo-portfolio-section .img-holder:hover:before {
  opacity: .8;
  -webkit-transform: scale(1);
          transform: scale(1);
}

/*--------------------------------------------------------------
15. wpo-shop-page
--------------------------------------------------------------*/
.wpo-shop-section .shop-grids {
  margin: 0 -15px;
}

@media (max-width: 767px) {
  .wpo-shop-section .shop-grids {
    margin: 0 -7px;
  }
}

.wpo-shop-section .grid {
  width: calc(33.33% - 30px);
  float: left;
  margin: 0 15px 30px;
}

@media (max-width: 991px) {
  .wpo-shop-section .grid {
    width: calc(50% - 30px);
  }
}

@media (max-width: 767px) {
  .wpo-shop-section .grid {
    width: calc(50% - 15px);
    margin: 0 7px 15px;
  }
}

@media (max-width: 550px) {
  .wpo-shop-section .grid {
    width: calc(100% - 15px);
    float: none;
  }
}

.wpo-shop-section .grid img {
  width: 100%;
}

.wpo-shop-section .details {
  background-color: #f7f7f7;
  text-align: center;
  padding: 35px 15px;
}

.wpo-shop-section .details h3 {
  font-size: 20px;
  font-size: 1.33333rem;
  margin: 0 0 0.5em;
  font-weight: 700;
}

@media (max-width: 991px) {
  .wpo-shop-section .details h3 {
    font-size: 16px;
    font-size: 1.06667rem;
  }
}

.wpo-shop-section .details h3 a {
  color: #1A1729;
}

.wpo-shop-section .details h3 a:hover {
  color: #4540E1;
}

.wpo-shop-section .details del {
  color: #625F71;
}

.wpo-shop-section .details del + span {
  display: inline-block;
  padding-left: 20px;
}

.wpo-shop-section .add-to-cart {
  margin-top: 20px;
}

.wpo-shop-section .add-to-cart a {
  font-family: "Urbanist";
  font-size: 15px;
  font-size: 1rem;
  font-weight: 600;
  color: #4540E1;
  padding: 12px 20px;
  border: 2px solid #4540E1;
  display: inline-block;
  border-radius: 50px;
}

@media (max-width: 767px) {
  .wpo-shop-section .add-to-cart a {
    font-size: 12px;
    font-size: 0.8rem;
    padding: 8px 18px;
  }
}

.wpo-shop-section .add-to-cart a:hover {
  background-color: #4540E1;
  color: #fff;
}

.wpo-shop-section .add-to-cart a i {
  font-size: 15px;
  font-size: 1rem;
  display: inline-block;
  padding-left: 5px;
}

.wpo-shop-section .grid:hover .add-to-cart a {
  background-color: #4540E1;
  color: #fff;
}

/*--------------------------------------------------------------
16. wpo-shop-single-page
--------------------------------------------------------------*/
.wpo-shop-single-section {
  /*** product slider ***/
  /*** product info ***/
}

.wpo-shop-single-section .shop-single-slider .slider-for {
  text-align: center;
}

.wpo-shop-single-section .shop-single-slider .slider-for img {
  display: inline-block;
  width: 100%;
  max-height: 600px;
  -o-object-fit: cover;
     object-fit: cover;
}

.wpo-shop-single-section .shop-single-slider .slider-nav {
  padding: 0 25px;
  margin-top: 35px;
}

.wpo-shop-single-section .shop-single-slider .slider-nav > i {
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 100;
}

.wpo-shop-single-section .shop-single-slider .slider-nav > i:hover {
  cursor: pointer;
}

.wpo-shop-single-section .shop-single-slider .slider-nav .nav-btn-rt {
  left: auto;
  right: 0;
}

.wpo-shop-single-section .shop-single-slider .slider-nav .slick-slide {
  text-align: center;
  padding: 0 3px;
}

.wpo-shop-single-section .shop-single-slider .slider-nav .slick-slide img {
  display: inline-block;
  width: 100%;
}

.wpo-shop-single-section .product-details {
  padding: 15px 30px 80px;
  /*** product option ***/
}

@media (max-width: 1199px) {
  .wpo-shop-single-section .product-details {
    padding: 10px 30px 15px;
  }
}

@media (max-width: 991px) {
  .wpo-shop-single-section .product-details {
    margin-top: 45px;
    padding: 40px 30px;
  }
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-details {
    padding: 0;
  }
}

.wpo-shop-single-section .product-details h2 {
  font-size: 35px;
  font-size: 2.33333rem;
  line-height: 1.4em;
  margin: 0 0 0.33em;
  font-weight: 600;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-details h2 {
    font-size: 22px;
    font-size: 1.46667rem;
  }
}

.wpo-shop-single-section .product-details .price {
  font-size: 25px;
  font-size: 1.66667rem;
  color: #4540E1;
  margin: 7px 0 20px;
  font-weight: 600;
}

@media (max-width: 991px) {
  .wpo-shop-single-section .product-details .price {
    font-size: 30px;
    font-size: 2rem;
  }
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-details .price {
    font-size: 25px;
    font-size: 1.66667rem;
  }
}

.wpo-shop-single-section .product-details .price .old {
  font-size: 24px;
  font-size: 1.6rem;
  font-weight: normal;
  color: #9592a4;
  text-decoration: line-through;
  display: inline-block;
  margin-left: 5px;
  font-weight: 500;
}

@media (max-width: 991px) {
  .wpo-shop-single-section .product-details .price .old {
    font-size: 20px;
    font-size: 1.33333rem;
  }
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-details .price .old {
    font-size: 18px;
    font-size: 1.2rem;
  }
}

.wpo-shop-single-section .product-details .product-rt {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 20px;
}

.wpo-shop-single-section .product-details .product-rt .rating {
  margin-right: 10px;
}

.wpo-shop-single-section .product-details .product-rt .rating i {
  color: #f9d322;
}

.wpo-shop-single-section .product-details p {
  margin-bottom: 1.3em;
}

.wpo-shop-single-section .product-details ul {
  list-style: none;
}

.wpo-shop-single-section .product-details ul li {
  padding-left: 20px;
  position: relative;
  margin-bottom: 10px;
}

.wpo-shop-single-section .product-details ul li:before {
  position: absolute;
  left: 0;
  top: 6px;
  width: 8px;
  height: 8px;
  background: #4540E1;
  content: "";
  border-radius: 50%;
}

.wpo-shop-single-section .product-details .product-option {
  margin-top: 30px;
}

.wpo-shop-single-section .product-details .tg-btm {
  margin-top: 25px;
}

.wpo-shop-single-section .product-details .tg-btm p {
  margin-bottom: 0px;
}

.wpo-shop-single-section .product-details .tg-btm p span {
  font-weight: 600;
  color: #1A1729;
  margin-right: 5px;
}

.wpo-shop-single-section .product-details .product-option .product-row button:after {
  display: none;
}

.wpo-shop-single-section .product-details .product-option .product-row > div {
  height: 35px;
  display: inline-block;
}

.wpo-shop-single-section .product-details .product-option .product-row > div + div {
  margin-left: 15px;
}

.wpo-shop-single-section .product-details .product-option .product-row > div + div .theme-btn, .wpo-shop-single-section .product-details .product-option .product-row > div + div .view-cart-btn {
  border-radius: 0;
  border-width: 1px;
  height: 40px;
  padding: 0 18px;
  color: #625F71;
}

.wpo-shop-single-section .product-details .product-option .product-row > div + div .theme-btn:hover, .wpo-shop-single-section .product-details .product-option .product-row > div + div .view-cart-btn:hover {
  color: #fff;
}

.wpo-shop-single-section .product-details .product-option .product-row > div:first-child {
  width: 85px;
}

.wpo-shop-single-section .product-details .product-option .product-row > div:last-child .theme-btn:hover, .wpo-shop-single-section .product-details .product-option .product-row > div:last-child .view-cart-btn:hover {
  background: #4540E1;
  border-color: #4540E1;
  color: #fff;
}

.wpo-shop-single-section .product-details .product-option .theme-btn, .wpo-shop-single-section .product-details .product-option .view-cart-btn {
  background-color: transparent;
  color: #fff;
  font-size: 14px;
  font-size: 0.93333rem;
  padding: 0 20px;
  height: 35px;
  line-height: 32px;
  outline: 0;
  border: 2px solid #e6e6e6;
  border-radius: 0;
}

.wpo-shop-single-section .product-details .product-option .theme-btn:hover, .wpo-shop-single-section .product-details .product-option .view-cart-btn:hover {
  background-color: #4540E1;
  color: #fff;
  border-color: #4540E1;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-details .product-option .theme-btn, .wpo-shop-single-section .product-details .product-option .view-cart-btn {
    font-size: 14px;
    font-size: 0.93333rem;
  }
}

.wpo-shop-single-section .product-details .product-option .theme-btn:before, .wpo-shop-single-section .product-details .product-option .view-cart-btn:before {
  display: none;
}

.wpo-shop-single-section .product-details .product-option .heart-btn i {
  font-size: 15px;
  font-size: 1rem;
  color: #625F71;
}

.wpo-shop-single-section .product-details .product-option .heart-btn:hover i {
  color: #fff;
}

.wpo-shop-single-section .product-details .product-option .product-row > div:last-child .theme-btn, .wpo-shop-single-section .product-details .product-option .product-row > div:last-child .view-cart-btn {
  background-color: #fff;
  font-size: 18px;
  font-size: 1.2rem;
  color: #625F71;
  border: 1px solid #e6e6e6;
}

.wpo-shop-single-section .product-details #product-count {
  border-radius: 0;
  border: 1px solid #e6e6e6;
}

.wpo-shop-single-section .product-details #product-count:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none;
}

.wpo-shop-single-section .product-details .bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-up,
.wpo-shop-single-section .product-details .bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-down {
  border-radius: 0;
  border-color: #e6e6e6;
  display: block;
  padding: 1px 4px;
  font-size: 10px;
}

.wpo-shop-single-section .product-details .bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-up:hover,
.wpo-shop-single-section .product-details .bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-down:hover {
  background-color: #4540E1;
  color: #fff;
}

.wpo-shop-single-section .product-info {
  margin-top: 75px;
  /*** tabs ***/
}

@media (max-width: 991px) {
  .wpo-shop-single-section .product-info {
    margin-top: 40px;
  }
}

.wpo-shop-single-section .product-info h4 {
  font-size: 18px;
  font-size: 1.2rem;
  margin: 0;
  line-height: 1.7em;
  font-weight: 600;
  margin-bottom: 10px;
}

.wpo-shop-single-section .product-info p {
  margin-bottom: 1.3em;
}

.wpo-shop-single-section .product-info .tab-pane p:last-child {
  margin-bottom: 0;
}

.wpo-shop-single-section .product-info .nav-tabs {
  font-family: "Urbanist";
  border: 0;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-info .nav-tabs {
    margin-bottom: 20px;
  }
}

.wpo-shop-single-section .product-info .nav-tabs li {
  margin-right: 1px;
}

.wpo-shop-single-section .product-info .nav-tabs li a.active {
  border: 0;
  outline: 0;
}

.wpo-shop-single-section .product-info .nav-tabs a {
  font-size: 15px;
  font-size: 1rem;
  color: #625F71;
  border: 0;
  border-radius: 0;
  margin: 0;
  display: block;
  padding: 12px 20px 11px;
  font-family: "Urbanist";
  position: relative;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-info .nav-tabs a {
    font-size: 14px;
    font-size: 0.93333rem;
    font-weight: normal;
    padding: 10px 10px 8px;
    text-transform: none;
  }
}

.wpo-shop-single-section .product-info .nav-tabs a:before {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 3px;
  background: #4540E1;
  border-radius: 4px;
  content: "";
  opacity: 0;
  visibility: hidden;
}

.wpo-shop-single-section .product-info .nav-tabs a:hover:before,
.wpo-shop-single-section .product-info .nav-tabs a.active:before {
  opacity: 1;
  visibility: visible;
}

.wpo-shop-single-section .product-info .nav-tabs .active,
.wpo-shop-single-section .product-info .nav-tabs li:hover {
  border-color: #4540E1;
}

.wpo-shop-single-section .product-info .tab-content {
  border-top: 1px solid rgba(69, 64, 225, 0.3);
  padding-top: 30px;
  margin-top: -1px;
}

@media (max-width: 767px) {
  .wpo-shop-single-section .product-info .tab-content {
    border: 0;
    padding: 0;
    margin: 0;
  }
}

.wpo-shop-single-section .slider-nav .slick-slide:focus {
  outline: none;
}

/*** client rv ***/
.client-rv {
  overflow: hidden;
  margin-bottom: 30px;
}

.client-rv:last-child {
  margin-bottom: 0;
}

.client-rv .client-pic {
  width: 100px;
  float: left;
}

.client-rv .client-pic img {
  border-radius: 50%;
}

@media (max-width: 767px) {
  .client-rv .client-pic {
    width: 100%;
    float: none;
    margin-bottom: 10px;
  }
}

.client-rv .details {
  width: calc(100% - 130px);
  float: right;
}

@media (max-width: 767px) {
  .client-rv .details {
    width: 100%;
    float: none;
  }
}

.client-rv .name-rating .product-rt {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (max-width: 767px) {
  .client-rv .name-rating-time {
    padding-bottom: 3px;
  }
}

.client-rv .name-rating-time > div,
.client-rv .name-rating > div {
  display: inline-block;
  font-size: 14px;
  font-size: 0.93333rem;
}

@media (max-width: 767px) {
  .client-rv .name-rating-time > div,
  .client-rv .name-rating > div {
    font-size: 12px;
    font-size: 0.8rem;
    display: block;
  }
}

.client-rv .rating {
  font-size: 12px;
  padding-left: 12px;
}

.client-rv .rating i {
  color: #F2BC00;
}

@media (max-width: 767px) {
  .client-rv .rating {
    padding-left: 0;
    margin: 4px 0 7px;
  }
}

.client-rv .name-rating-time .time {
  float: right;
  color: #b3b3b3;
  text-transform: uppercase;
}

@media (max-width: 767px) {
  .client-rv .name-rating-time .time {
    float: none;
  }
}

.client-rv .review-body {
  padding-top: 12px;
}

/*** review form ***/
.review-form {
  margin-top: 45px;
}

.review-form h4 {
  font-size: 30px;
  font-weight: 600;
  text-transform: capitalize;
}

.review-form p {
  margin-bottom: 1.73em;
}

.review-form .give-rat-sec {
  margin-bottom: 10px;
}

.review-form .give-rat-sec p {
  margin-bottom: 0;
}

.review-form .give-rating {
  display: inline-block;
  position: relative;
  height: 50px;
  line-height: 50px;
  font-size: 30px;
  margin-bottom: 10px;
}

.review-form .give-rating label {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  cursor: pointer;
}

.review-form .give-rating label:last-child {
  position: static;
}

.review-form .give-rating label:nth-child(1) {
  z-index: 5;
}

.review-form .give-rating label:nth-child(2) {
  z-index: 4;
}

.review-form .give-rating label:nth-child(3) {
  z-index: 3;
}

.review-form .give-rating label:nth-child(4) {
  z-index: 2;
}

.review-form .give-rating label:nth-child(5) {
  z-index: 1;
}

.review-form .give-rating label input {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
}

.review-form .give-rating label .icon {
  float: left;
  color: transparent;
}

.review-form .give-rating label:last-child .icon {
  color: #ddd;
}

.review-form .give-rating:not(:hover) label input:checked ~ .icon,
.review-form .give-rating:hover label:hover input ~ .icon {
  color: #FFD400;
}

.review-form .give-rating label input:focus:not(:checked) ~ .icon:last-child {
  color: #ddd;
  text-shadow: 0 0 5px #FFD400;
}

.review-form form input,
.review-form form textarea {
  background: transparent;
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  height: 50px;
  border: 1px solid #efefef;
}

.review-form form input:focus,
.review-form form textarea:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none;
}

.review-form form textarea {
  height: 130px;
}

.review-form form > div {
  margin-bottom: 27px;
}

.review-form form > div:last-child {
  margin-bottom: 0;
}

.review-form form .theme-btn-s4 {
  background-color: transparent;
  color: #908f8f;
  outline: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.review-form form .theme-btn-s4:hover {
  background-color: #4540E1;
  color: #fff;
}

@media screen and (min-width: 767px) {
  .review-form form .theme-btn-s4 {
    font-size: 15px;
    font-size: 1rem;
    padding: 0 20px;
  }
}

.wpo-course-details-text .team-info-img {
  margin-top: 0;
}

/*=====================================================
9. wpo-service-single
======================================================*/
.wpo-service-single-area .wpo-service-single-wrap .wpo-service-single-title h3 {
  font-weight: 600;
  font-size: 35px;
  line-height: 130.5%;
  margin-bottom: 20px;
  text-transform: capitalize;
}

@media (max-width: 767px) {
  .wpo-service-single-area .wpo-service-single-wrap .wpo-service-single-title h3 {
    font-size: 25px;
  }
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-single-item {
  margin-bottom: 40px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-single-item .wpo-service-single-main-img {
  margin-bottom: 30px;
}

.wpo-service-single-area .wpo-service-single-wrap .list-widget {
  max-width: 590px;
}

.wpo-service-single-area .wpo-service-single-wrap .list-widget ul {
  list-style: none;
}

.wpo-service-single-area .wpo-service-single-wrap .list-widget ul li {
  padding: 10px 0;
  padding-left: 35px;
  position: relative;
}

.wpo-service-single-area .wpo-service-single-wrap .list-widget ul li:before {
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 25px;
  height: 25px;
  line-height: 25px;
  border-radius: 50%;
  background: rgba(69, 64, 225, 0.1);
  content: "\e64c";
  font-family: "themify";
  text-align: center;
  color: #4540E1;
  font-size: 14px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-p-details-img {
  margin-bottom: 10px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-area {
  padding-bottom: 0;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item {
  padding: 20px;
  -webkit-box-shadow: 0px 0px 20px 0px rgba(20, 33, 43, 0.1);
          box-shadow: 0px 0px 20px 0px rgba(20, 33, 43, 0.1);
}

@media (max-width: 1200px) {
  .wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item {
    padding: 20px 10px;
  }
}

@media (max-width: 991px) {
  .wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item {
    margin-bottom: 20px;
  }
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item h2 {
  font-weight: 600;
  font-size: 22px;
  margin-bottom: 12px;
  margin-top: 10px;
}

@media (max-width: 1400px) {
  .wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item h2 {
    font-size: 19px;
    margin-bottom: 10px;
  }
}

@media (max-width: 1200px) {
  .wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item h2 {
    font-size: 17px;
    margin-bottom: 10px;
  }
}

@media (max-width: 991px) {
  .wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item h2 {
    font-size: 22px;
  }
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item p {
  margin-bottom: 5px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-area .wpo-service-item .fi:before {
  font-size: 40px;
  color: #4540E1;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area {
  padding: 30px;
  -webkit-box-shadow: 0px 2px 8px 2px rgba(22, 14, 71, 0.1);
          box-shadow: 0px 2px 8px 2px rgba(22, 14, 71, 0.1);
  border-radius: 20px;
  background: #fff;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-title {
  margin-bottom: 30px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-title h2 {
  font-weight: 600;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area .col {
  margin-bottom: 30px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area .form-control {
  height: 50px;
  border: 0;
  border-bottom: 1px solid #DDDDDD;
  border-radius: 0;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area .form-control:focus {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area textarea.form-control {
  height: 120px;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area select.form-control {
  background: transparent url(../../images/select-icon2.png) no-repeat calc(100% - 15px) center;
}

.wpo-service-single-area .wpo-service-single-wrap .wpo-service-contact-area .wpo-contact-form-area .submit-area button {
  border-radius: 6px;
}

/*===============================
18. wpo-checkout-page-style
================================*/
.wpo-checkout-area .checkout-wrap ul {
  list-style: none;
}

.wpo-checkout-area #open2 {
  display: none;
}

.wpo-checkout-area #open3 {
  display: none;
}

.wpo-checkout-area #open4 {
  display: none;
}

.wpo-checkout-area .create-account p {
  margin-bottom: 15px;
  color: #625F71;
}

.wpo-checkout-area .create-account {
  display: none;
  padding: 20px;
}

.wpo-checkout-area .create-account span {
  margin-bottom: 20px;
  display: block;
  color: #625F71;
}

.wpo-checkout-area .input-wrap {
  position: relative;
}

.wpo-checkout-area .create-account input {
  width: 100%;
  height: 50px;
  border: 1px solid #ebebeb;
  margin-bottom: 25px;
  padding-left: 20px;
  border-radius: 0;
}

.wpo-checkout-area .create-account input:focus {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.wpo-checkout-area .create-account button {
  position: absolute;
  right: 0;
  top: 0;
  height: 50px;
  background: #333;
  color: #fff;
  width: 108px;
  border: none;
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  cursor: pointer;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.wpo-checkout-area .input-wrap.s1 {
  position: unset;
}

.wpo-checkout-area .input-wrap.s1 button {
  position: unset;
  color: #fff;
  border: none;
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  cursor: pointer;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  height: 50px;
  background: #333;
  color: #fff;
  width: 108px;
  -webkit-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
}

.wpo-checkout-area .create-account button:hover {
  background: #4540E1;
}

.wpo-checkout-area .coupon {
  position: relative;
  cursor: pointer;
  cursor: pointer;
  background: #FCF9EF;
}

.wpo-checkout-area .rating-wrapper .theme-btn-s2 {
  background: #4540E1;
  color: #fff;
}

.wpo-checkout-area .rating-wrapper .theme-btn-s2:hover {
  background: #2b26dd;
}

.wpo-checkout-area .coupon #toggle1, .wpo-checkout-area #toggle2 {
  margin-bottom: 0;
  font-weight: 500;
  font-size: 18px;
  color: #625F71;
  cursor: pointer;
  padding: 20px 20px;
  border: 1px solid #D9D9D9;
  -webkit-transition: all .3s;
  transition: all .3s;
  width: 100%;
}

@media (max-width: 767px) {
  .wpo-checkout-area .coupon #toggle1, .wpo-checkout-area #toggle2 {
    font-size: 15px;
  }
}

.wpo-checkout-area .coupon #toggle1 .text, .wpo-checkout-area #toggle2 .text {
  font-weight: 600;
  font-size: 18px;
  line-height: 18px;
  color: #1A1729;
  margin-bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-checkout-area .coupon #toggle1 .text i.fi::before, .wpo-checkout-area #toggle2 .text i.fi::before {
  line-height: 30px;
  font-size: 30px;
  color: #1A1729;
  padding-right: 5px;
}

.wpo-checkout-area .coupon #toggle1 .text span, .wpo-checkout-area #toggle2 .text span {
  color: #F2BC00;
  padding-left: 5px;
}

.wpo-checkout-area .caupon-wrap {
  background: #fff;
  margin-bottom: 20px;
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-checkout-area .active-border {
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-checkout-area .coupon.coupon-3 {
  background: transparent;
  margin-top: 40px;
  margin-bottom: 30px;
  cursor: unset;
}

.wpo-checkout-area .coupon.coupon-3 h2 {
  font-weight: 600;
  font-size: 25px;
}

.wpo-checkout-area .billing-adress .form-style input,
.wpo-checkout-area .billing-adress .form-style select {
  width: 100%;
  margin-bottom: 15px;
  padding-left: 10px;
  height: 50px;
  background: #fff;
  border-radius: 5px;
  border: none;
  border: 1px solid #D9D9D9;
}

.wpo-checkout-area .billing-adress .form-style input:focus,
.wpo-checkout-area .billing-adress .form-style select:focus {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.wpo-checkout-area .billing-adress .form-style select {
  -webkit-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  appearance: none;
  -moz-appearance: none;
  background: transparent url(../../images/select-icon2.png) no-repeat calc(100% - 15px) center;
  position: relative;
}

.wpo-checkout-area .billing-adress {
  padding: 30px;
  border: 1px solid #D9D9D9;
  margin-bottom: 30px;
}

.wpo-checkout-area .note-area textarea {
  width: 100%;
  height: 150px;
  padding-top: 10px;
  margin-bottom: 0;
  padding-left: 10px;
  border: 1px solid #ebebeb;
  border-radius: 5px;
}

.wpo-checkout-area .note-area textarea:focus {
  outline: none;
}

.wpo-checkout-area .note-area p {
  color: #625F71;
}

.wpo-checkout-area .biling-item-2 label.fontsize {
  margin-bottom: 20px;
  position: relative;
  padding-left: 10px;
}

.wpo-checkout-area .biling-item-3 label.fontsize {
  margin-bottom: 20px;
  position: relative;
  padding-left: 10px;
}

.wpo-checkout-area .biling-item-2 .billing-adress {
  padding: 0 27px;
}

.wpo-checkout-area .biling-item-2 .billing-adress .form-style input {
  margin: 30px 0;
}

.wpo-checkout-area .payment-name ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  margin-top: 0;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
}

@media (max-width: 575px) {
  .wpo-checkout-area .payment-name ul {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}

.wpo-checkout-area .payment-area form {
  overflow: unset !important;
}

.wpo-checkout-area .payment-name ul li {
  width: 100px;
  height: 60px;
  text-align: center;
  line-height: 60px;
}

.wpo-checkout-area .payment-name ul li input {
  margin-right: 0;
}

.wpo-checkout-area .payment-name ul li {
  margin-right: 15px;
}

@media (max-width: 575px) {
  .wpo-checkout-area .payment-name ul li {
    margin-bottom: 10px;
  }
}

.wpo-checkout-area .payment-area h2 {
  padding-bottom: 40px;
  margin-bottom: 0;
}

.wpo-checkout-area .payment-select {
  padding: 40px 0;
  padding-top: 0;
}

.wpo-checkout-area .payment-select ul {
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-checkout-area .biling-item-2 input {
  background: transparent;
  height: 16px;
  width: 16px;
  -moz-appearance: none;
       appearance: none;
  -webkit-appearance: none;
  border: 3px solid #FFF;
  -webkit-box-shadow: 0 0 0 1px #4540E1;
          box-shadow: 0 0 0 1px #4540E1;
  margin-right: 10px;
}

.wpo-checkout-area .biling-item-2 input:checked {
  background: #F2BC00;
}

.wpo-checkout-area .biling-item-2 .contact-form input {
  -webkit-box-shadow: 0 0 0 1px transparent;
          box-shadow: 0 0 0 1px transparent;
}

.wpo-checkout-area .biling-item-3 input {
  background: transparent;
  height: 16px;
  width: 16px;
  -moz-appearance: none;
       appearance: none;
  -webkit-appearance: none;
  border: 3px solid #FFF;
  -webkit-box-shadow: 0 0 0 1px #4540E1;
          box-shadow: 0 0 0 1px #4540E1;
  margin-right: 10px;
}

.wpo-checkout-area .biling-item-3 input:checked {
  background: #F2BC00;
}

.wpo-checkout-area .biling-item-3 .contact-form input {
  -webkit-box-shadow: 0 0 0 1px transparent;
          box-shadow: 0 0 0 1px transparent;
}

.wpo-checkout-area .payment-select ul li {
  margin-right: 10px;
  font-size: 15px;
  font-weight: 500;
  color: #5B5B5B;
}

.wpo-checkout-area .payment-select ul li input {
  background: transparent;
  border-radius: 100%;
  height: 16px;
  width: 16px;
  -moz-appearance: none;
       appearance: none;
  -webkit-appearance: none;
  border: 3px solid #FFF;
  -webkit-box-shadow: 0 0 0 1px #4540E1;
          box-shadow: 0 0 0 1px #4540E1;
  margin-right: 10px;
}

.wpo-checkout-area .payment-select ul li input:checked {
  background: #F2BC00;
}

.wpo-checkout-area .payment-select label {
  font-size: 18px;
  font-weight: 600;
}

@media (max-width: 575px) {
  .wpo-checkout-area .payment-select label {
    font-size: 14px;
  }
}

.wpo-checkout-area .payment-area h2 {
  font-size: 20px;
  color: #878787;
  font-weight: 700;
}

.wpo-checkout-area .payment-area h2 span {
  font-size: 30px;
  color: #ff493c;
}

.wpo-checkout-area .payment-area {
  width: 100%;
}

.wpo-checkout-area label {
  color: #625F71;
  font-weight: 400;
  font-size: 18px;
}

.wpo-checkout-area .payment-name.active {
  -webkit-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out;
}

.wpo-checkout-area .payment-name ul li input {
  margin-right: 0;
  position: absolute;
  z-index: -1;
}

.wpo-checkout-area .payment-name ul li input:checked ~ label {
  border: 1px solid #F2BC00;
}

.wpo-checkout-area .payment-name label {
  width: 100%;
  border: 1px solid transparent;
  -webkit-transition: all .3s;
  transition: all .3s;
}

.wpo-checkout-area .payment-name .visa label {
  border: 1px solid #0057A0;
}

.wpo-checkout-area .payment-name .mas label {
  border: 1px solid #CC0000;
}

.wpo-checkout-area .payment-name .ski label {
  border: 1px solid #691A5F;
}

.wpo-checkout-area .payment-name .pay label {
  border: 1px solid #019CDE;
}

.wpo-checkout-area .payment-option {
  padding: 30px;
  border: 1px solid #D9D9D9;
}

.wpo-checkout-area .payment-option h3 {
  position: relative;
  text-align: center;
  margin-bottom: 20px;
  font-weight: 500;
  font-size: 25px;
  border-bottom: 2px solid #F1E2CC;
  padding-bottom: 15px;
}

.wpo-checkout-area .payment-option h3::before {
  position: absolute;
  content: "";
  bottom: -3px;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  height: 2px;
  width: 100px;
  background: #F2BC00;
}

.wpo-checkout-area .payment-name {
  display: none;
}

.wpo-checkout-area .payment-name.active {
  display: block;
}

.wpo-checkout-area .payment-option.active .payment-name {
  display: none !important;
}

.wpo-checkout-area .payment-area .form-style input,
.wpo-checkout-area .payment-area .form-style select {
  width: 100%;
  margin-bottom: 15px;
  padding-left: 10px;
  height: 50px;
  background: #fff;
  border-radius: 2px;
  border: none;
  border: 1px solid #ebebeb;
}

.wpo-checkout-area .payment-area .form-style input:focus,
.wpo-checkout-area .payment-area .form-style select:focus {
  outline: none;
}

.wpo-checkout-area .payment-area .contact-form {
  margin-top: 40px;
}

.wpo-checkout-area .cout-order-area {
  padding: 30px;
  border: 1px solid #D9D9D9;
  margin-bottom: 30px;
}

.wpo-checkout-area .cout-order-area h3 {
  position: relative;
  text-align: center;
  margin-bottom: 60px;
  font-weight: 500;
  font-size: 25px;
  border-bottom: 2px solid #F1E2CC;
  padding-bottom: 15px;
}

.wpo-checkout-area .cout-order-area h3::before {
  position: absolute;
  content: "";
  bottom: -3px;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  height: 2px;
  width: 100px;
  background: #F2BC00;
}

.wpo-checkout-area .oreder-item .title {
  border-bottom: 1px solid #D9D9D9;
  padding-bottom: 10px;
}

.wpo-checkout-area .oreder-item .title h2 {
  font-family: "Urbanist";
  font-weight: 600;
  font-size: 18px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-checkout-area .oreder-item .title.s2 {
  border-bottom: 1px solid transparent;
  padding-bottom: 0;
  padding-top: 15px;
}

.wpo-checkout-area .oreder-item .title.s2 h2 {
  margin-bottom: 0;
}

.wpo-checkout-area .oreder-item .oreder-product {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  border-bottom: 1px solid #D9D9D9;
  padding: 20px 0;
}

.wpo-checkout-area .oreder-item .oreder-product .images {
  width: 100px;
  background: #F3F3F3;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.wpo-checkout-area .oreder-item .oreder-product .images img {
  width: 100%;
}

.wpo-checkout-area .oreder-item .oreder-product .product ul .first-cart {
  font-family: "Urbanist";
  font-weight: 600;
  font-size: 18px;
  line-height: 23px;
  color: #233D50;
}

.wpo-checkout-area .oreder-item .oreder-product .product ul li .rating-product i {
  font-size: 13px;
  background: -webkit-gradient(linear, left top, left bottom, from(#FED700), to(#F78914));
  background: linear-gradient(180deg, #FED700 0%, #F78914 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.wpo-checkout-area .oreder-item .oreder-product .product ul li .rating-product span {
  font-family: "Urbanist";
  font-weight: 600;
  font-size: 14px;
  line-height: 19px;
  color: #625F71;
  padding-left: 10px;
}

.wpo-checkout-area .oreder-item .oreder-product span {
  font-weight: 600;
  font-size: 20px;
  color: #233D50;
}

.wpo-checkout-area .create-account.active {
  display: block;
}

.wpo-checkout-area .contact-form .form-control {
  -webkit-appearance: auto;
     -moz-appearance: auto;
          appearance: auto;
}

.wpo-checkout-area .contact-form input,
.wpo-checkout-area .contact-form select {
  margin-top: 10px;
}

.wpo-checkout-area .submit-btn-area {
  margin-top: 20px;
}

.wpo-checkout-area .submit-btn-area .theme-btn, .wpo-checkout-area .submit-btn-area .view-cart-btn {
  width: 100%;
  border: none;
  background: #4540E1;
  color: #fff;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  outline: none;
}

.wpo-checkout-area .submit-btn-area .theme-btn::before, .wpo-checkout-area .submit-btn-area .view-cart-btn::before {
  display: none;
  position: unset;
}

/*===========================
17. wpo-cart-page-style
===========================*/
.cart-area .cart-wrapper {
  -webkit-box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
          box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
}

@media (max-width: 991px) {
  .cart-area .cart-wrapper table {
    width: 1022px;
  }
}

@media (max-width: 991px) {
  .cart-area .cart-wrapper form {
    overflow: scroll;
    overflow-y: hidden;
  }
}

.cart-area .cart-wrap td,
.cart-area .cart-wrap th {
  width: 10%;
  border-bottom: 1px solid #f0f0f094;
  text-align: center;
  font-weight: 400;
  color: #414141;
}

.cart-area .cart-wrap td ul,
.cart-area .cart-wrap th ul {
  list-style: none;
}

.cart-area .cart-wrap td.images img,
.cart-area .cart-wrap th.images img {
  max-height: 150px;
}

.cart-area .cart-wrap th {
  border-bottom: 1px solid #f0f0f094;
  padding: 40px 0;
}

.cart-area .cart-wrap thead {
  background: #fff;
  color: #505050;
  padding: 40px;
}

.cart-area .cart-area form {
  margin: auto;
  text-align: center;
  background: #fff;
}

.cart-area .cart-wrap .product {
  width: 15%;
}

.cart-area .cart-wrap .product ul {
  text-align: left;
  padding-left: 30px;
  list-style: none;
}

.cart-area .cart-wrap .product ul li {
  padding-bottom: 5px;
}

.cart-area .cart-wrap .product a {
  font-size: 16px;
  color: #a5a4a4;
}

.cart-area .cart-wrap .product-2 {
  text-align: left;
  padding-left: 55px;
}

.cart-area .cart-wrap .product a:hover {
  color: #4540E1;
}

.cart-area .cart-wrap th {
  height: 80px;
  font-weight: 700;
}

.cart-area .cart-wrap td {
  padding: 40px 25px;
}

.cart-area .cart-wrap td.action ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  list-style: none;
}

.cart-area .cart-wrap td.action ul .w-btn-view a {
  line-height: 45px;
}

.cart-area .cart-wrap td span {
  display: block;
  width: 100px;
  height: 38px;
  line-height: 34px;
  color: #ee9902;
  font-size: 14px;
  border: 1.5px solid #ee9902;
  border-radius: 4px;
}

.cart-area .cart-wrap td.Del span {
  color: #4ABA4E;
  border: 1.5px solid #4ABA4E;
}

.cart-area .cart-wrap td.can span {
  color: #D85656;
  border: 1.5px solid #D85656;
}

.cart-area .cart-wrap td.pro span {
  color: #691A5F;
  border: 1.5px solid #691A5F;
}

.cart-area .cart-wrap .name {
  width: 15%;
}

.cart-area .cart-wrap .action a {
  display: block;
  width: 40px;
  height: 38px;
  line-height: 45px;
  background: #414141;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
}

.cart-area .cart-wrap .action a .fi::before {
  font-size: 20px;
}

.cart-area .cart-wrap .action a:hover {
  background: #4540E1;
}

.cart-area .cart-wrap .action li.c-btn {
  margin-right: 10px;
}

.cart-area .cart-wrap .action li.c-btn a {
  background-color: #4540E1;
}

.cart-area .order-wrap {
  padding: 0;
}

.cart-area .cart-wrap tr:nth-child(even) {
  background: #FCFCFC;
}

.cart-area .cart-wrap .quantity {
  position: relative;
  max-width: 110px;
  margin: 0 auto;
}

.cart-area .quantity input {
  width: 105px;
  padding: 0px 35px;
  text-align: center;
  height: 36px;
  position: relative;
  background: #f2f2f5;
  border: none;
  border-radius: 40px;
}

.cart-area .quantity .qtybutton {
  position: absolute;
  top: 0;
  left: 0px;
  height: 30px;
  width: 30px;
  text-align: center;
  line-height: 28px;
  font-size: 18px;
  cursor: pointer;
  color: #333;
  background: #fafaff;
  border-radius: 30px;
}

.cart-area .cart-wrap .quantity .qtybutton {
  top: 50%;
  left: 5px;
  transform: translateY(-51%);
  -webkit-transform: translateY(-51%);
  -moz-transform: translateY(-51%);
}

.cart-area .cart-wrap .quantity .qtybutton.dec {
  border-left: none;
}

.cart-area .cart-wrap .quantity .qtybutton.inc {
  right: 5px;
  left: auto;
  border-right: none;
}

.cart-area .submit-btn-area {
  padding: 40px;
}

.cart-area .submit-btn-area ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  list-style: none;
}

@media (max-width: 450px) {
  .cart-area .submit-btn-area ul {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.cart-area .submit-btn-area ul li:last-child {
  margin-left: 20px;
}

@media (max-width: 450px) {
  .cart-area .submit-btn-area ul li:last-child {
    margin-left: 3px;
    margin-top: 5px;
  }
}

.cart-area .submit-btn-area button {
  padding: 17px 40px;
  border: none;
  background: #333;
  color: #fff;
  -webkit-transition: all .3s;
  transition: all .3s;
  outline: none;
  border-radius: 6px;
}

.cart-area .submit-btn-area button:hover {
  background: #4540E1;
}

.cart-area .submit-btn-area .theme-btn, .cart-area .submit-btn-area .view-cart-btn {
  text-transform: capitalize;
  font-size: 15px;
  font-weight: 500;
  line-height: 27px;
}

.cart-area .cart-product-list ul {
  border-top: 1px solid #f0f0f094;
  padding-top: 20px;
  list-style: none;
}

.cart-area .cart-product-list ul li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 0px 30px;
  font-weight: 300;
  font-size: 18px;
  line-height: 21px;
  color: #414141;
  padding-bottom: 30px;
}

.cart-area .cart-product-list ul li.cart-b {
  border-top: 1px solid #f0f0f094;
  border-bottom: 1px solid #f0f0f094;
  color: #4540E1;
  padding-top: 30px;
  font-weight: 600;
}

/*--------------------------------------------------------------
19. wpo-faq-page
--------------------------------------------------------------*/
.wpo-faq-section .wpo-section-title {
  text-align: center;
}

.wpo-faq-section .wpo-section-title h2 {
  display: inline-block;
}

.wpo-faq-section .accordion-item {
  border: 0;
  border: 1px solid #e1e1e1;
  padding: 5px 20px;
  margin-bottom: 20px;
}

.wpo-faq-section .accordion-item .accordion-body {
  padding-left: 0;
  padding-right: 0;
}

.wpo-faq-section .accordion-item button {
  padding: 20px;
  border: 0;
  border-radius: 0;
  padding-left: 0;
  padding-right: 0;
  font-size: 18px;
  color: #1A1729;
  text-align: left;
  font-family: "Urbanist";
}

.wpo-faq-section .accordion-item button::after {
  background: none;
  font-family: "themify";
  content: "\e622";
  font-size: 15px;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  font-weight: 700;
}

.wpo-faq-section .accordion-item button.collapsed {
  color: #373b3e;
}

.wpo-faq-section .accordion-item button.collapsed::after {
  content: "\e61a";
}

.wpo-faq-section .accordion-item button:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none;
  border-color: #e1e1e1;
}

.wpo-faq-section .accordion-item .accordion-collapse {
  border: 0;
}

.wpo-faq-section .accordion-button {
  background: transparent;
}

/*-----------------------------------------------------
#4.1 faq section
------------------------------------------------------*/
.question-area {
  background: #f9f9f9;
  padding-top: 100px;
}

@media (max-width: 991px) {
  .question-area {
    padding-top: 80px;
  }
}

@media (max-width: 767px) {
  .question-area {
    padding-top: 60px;
  }
}

.question-area .wpo-section-title {
  text-align: center;
}

.question-area .wpo-section-title h2 {
  display: inline-block;
}

.question-touch {
  max-width: 570px;
  margin: auto;
  -webkit-box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
  box-shadow: 0px 5px 15px 0px rgba(68, 68, 68, 0.1);
  padding: 50px;
  text-align: center;
  padding-top: 60px;
  background: #fff;
  border-radius: 5px;
}

@media (max-width: 590px) {
  .question-touch {
    padding: 15px;
  }
}

@media (max-width: 991px) {
  .faq-pb {
    margin-top: 15px;
  }
}

.question-touch h2 {
  font-size: 30px;
  font-weight: 700;
  text-align: center;
  margin-top: 0;
  margin-bottom: 35px;
}

.question-touch .half-col {
  width: 100%;
}

.question-touch input,
.question-touch textarea {
  width: 100%;
  height: 50px;
  border: none;
  outline: none;
  background: none;
  border-radius: 0;
  border-bottom: 1px solid #ddd;
  border-top: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  margin-bottom: 22px;
  padding: 0;
}

.question-touch input:focus,
.question-touch textarea:focus {
  outline: none;
  background: none;
  border-radius: 0;
  border-bottom: 1px solid #eeeeee;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.question-touch textarea {
  height: 160px;
}

.question-touch ::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #888;
  font-size: 14px;
}

.question-touch ::-moz-placeholder {
  /* Firefox 19+ */
  color: #888;
  font-size: 14px;
}

.question-touch :-ms-input-placeholder {
  /* IE 10+ */
  color: #888;
  font-size: 14px;
}

.question-touch :-moz-placeholder {
  /* Firefox 18- */
  color: #888;
  font-size: 14px;
}

/*--------------------------------------------------------------
20. wpo-blog-pg-section
--------------------------------------------------------------*/
.wpo-blog-pg-section {
  /*** format-standard ***/
  /*** format-gallery ***/
  /*** format-quote ***/
  /*** format-video ***/
}

.wpo-blog-pg-section .wpo-blog-content .post {
  margin-bottom: 70px;
}

@media (max-width: 991px) {
  .wpo-blog-pg-section .wpo-blog-content .post {
    margin-bottom: 50px;
  }
}

@media (max-width: 767px) {
  .wpo-blog-pg-section .wpo-blog-content .post {
    margin-bottom: 40px;
  }
}

.wpo-blog-pg-section .entry-meta {
  list-style: none;
  overflow: hidden;
  margin: 35px 0;
}

@media (max-width: 767px) {
  .wpo-blog-pg-section .entry-meta {
    margin: 25px 0;
  }
}

.wpo-blog-pg-section .entry-meta ul {
  list-style: none;
}

.wpo-blog-pg-section .entry-meta ul li {
  font-weight: 500;
  font-size: 16px;
  font-size: 1.06667rem;
  float: left;
  text-transform: uppercase;
}

.wpo-blog-pg-section .entry-meta ul li a {
  color: #636893;
}

.wpo-blog-pg-section .entry-meta ul li a:hover {
  color: #4540E1;
}

.wpo-blog-pg-section .entry-meta ul li i {
  position: relative;
  margin-right: 3px;
}

.wpo-blog-pg-section .entry-meta ul li i:before {
  font-size: 13px;
}

.wpo-blog-pg-section .entry-meta ul li + li {
  margin-left: 20px;
  padding-left: 20px;
  position: relative;
}

.wpo-blog-pg-section .entry-meta ul li + li:before {
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 7px;
  height: 7px;
  content: "";
  background: #4540E1;
  border-radius: 50%;
}

@media (max-width: 440px) {
  .wpo-blog-pg-section .entry-meta ul li + li:before {
    display: none;
  }
}

@media (max-width: 440px) {
  .wpo-blog-pg-section .entry-meta ul li + li {
    margin-left: 0;
    padding-left: 0;
  }
}

@media (max-width: 767px) {
  .wpo-blog-pg-section .entry-meta ul li {
    font-size: 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 440px) {
  .wpo-blog-pg-section .entry-meta ul li {
    float: none;
    display: block;
    margin-bottom: 5px;
  }
}

.wpo-blog-pg-section .post h3 {
  font-size: 34px;
  font-size: 2.26667rem;
  line-height: 1.2em;
  font-weight: 600;
  margin: -0.27em 0 0.7em;
  font-family: "Urbanist";
}

@media (max-width: 991px) {
  .wpo-blog-pg-section .post h3 {
    font-size: 25px;
    font-size: 1.66667rem;
  }
}

@media (max-width: 767px) {
  .wpo-blog-pg-section .post h3 {
    font-size: 22px;
    font-size: 1.46667rem;
  }
}

.wpo-blog-pg-section .post h3 a {
  color: #1A1729;
}

.wpo-blog-pg-section .post h3 a:hover {
  color: #4540E1;
}

.wpo-blog-pg-section .post p {
  margin-bottom: 1.5em;
}

@media (max-width: 991px) {
  .wpo-blog-pg-section .post p {
    font-size: 16px;
    font-size: 1.06667rem;
  }
}

.wpo-blog-pg-section .post a.read-more {
  color: #625F71;
  text-transform: capitalize;
  font-size: 16px;
}

.wpo-blog-pg-section .post a.read-more:hover {
  color: #4540E1;
}

.wpo-blog-pg-section .entry-media img {
  width: 100%;
}

.wpo-blog-pg-section .format-standard,
.wpo-blog-pg-section .format-quote {
  background-color: #f2f2f2;
  padding: 25px 35px 45px;
}

@media (max-width: 767px) {
  .wpo-blog-pg-section .format-standard,
  .wpo-blog-pg-section .format-quote {
    padding: 25px 20px 45px;
  }
}

.wpo-blog-pg-section .format-standard {
  position: relative;
}

.wpo-blog-pg-section .format-standard:before {
  position: absolute;
  right: 20px;
  top: 10px;
  content: "\f101";
  font-family: "Flaticon";
  font-size: 90px;
  line-height: 80px;
  color: #e7e7e7;
}

.wpo-blog-pg-section .format-gallery {
  position: relative;
}

.wpo-blog-pg-section .format-gallery .owl-controls {
  width: 100%;
  margin: 0;
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.wpo-blog-pg-section .format-gallery .owl-nav [class*=owl-]:hover {
  background: #4540E1;
  color: #fff;
}

.wpo-blog-pg-section .format-gallery .owl-nav [class*=owl-] {
  background: #fff;
  width: 50px;
  height: 50px;
  line-height: 58px;
  padding: 0;
  margin: 0;
  border-radius: 50%;
  color: #4540E1;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  border: 0;
}

.wpo-blog-pg-section .format-gallery .owl-nav [class*=owl-] .fi::before {
  font-size: 20px;
}

.wpo-blog-pg-section .format-gallery .owl-nav .owl-prev,
.wpo-blog-pg-section .format-gallery .owl-nav .owl-next {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.wpo-blog-pg-section .format-gallery .owl-nav .owl-prev {
  left: 15px;
}

.wpo-blog-pg-section .format-gallery .owl-nav .owl-next {
  right: 15px;
}

.wpo-blog-pg-section .format-quote {
  text-align: center;
  padding: 80px 60px;
  position: relative;
}

@media (max-width: 767px) {
  .wpo-blog-pg-section .format-quote {
    padding: 40px 20px;
  }
}

.wpo-blog-pg-section .format-quote p {
  margin-bottom: 0;
}

.wpo-blog-pg-section .format-quote:before {
  font-family: "Flaticon";
  content: "\f120";
  font-size: 250px;
  font-size: 16.66667rem;
  color: #ecf3fb;
  margin-left: 0;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.wpo-blog-pg-section .format-quote h3,
.wpo-blog-pg-section .format-quote p {
  position: relative;
}

.wpo-blog-pg-section .format-video .video-holder {
  position: relative;
  text-align: center;
}

.wpo-blog-pg-section .format-video .video-holder:before {
  content: "";
  background-color: #163967;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  opacity: 0.5;
}

.wpo-blog-pg-section .format-video .video-holder:hover:before {
  opacity: 0.8;
}

.wpo-blog-pg-section .format-video .video-holder a {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 63px;
  height: 63px;
  line-height: 70px;
  background: #fff;
  border-radius: 50%;
  z-index: 1;
}

.wpo-blog-pg-section .format-video .video-holder a:after {
  position: absolute;
  left: -12px;
  top: -12px;
  width: 140%;
  height: 140%;
  background: rgba(255, 255, 255, 0.3);
  content: "";
  -webkit-animation: spineer 2s infinite;
  animation: spineer 2s infinite;
  border-radius: 50%;
  z-index: -1;
}

.wpo-blog-pg-section .format-video .video-holder a:before {
  content: "";
  width: 0px;
  height: 0px;
  border-top: 9px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 14px solid #2f426b;
  position: absolute;
  left: 52%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.wpo-blog-pg-section .format-video .video-holder .fi:before {
  font-size: 20px;
  font-size: 1.33333rem;
  color: #4540E1;
}

@media screen and (min-width: 1200px) {
  .blog-pg-left-sidebar .blog-sidebar {
    padding-right: 45px;
    padding-left: 0;
  }
}

@media screen and (min-width: 1200px) {
  .blog-pg-fullwidth .wpo-blog-content {
    padding: 0;
  }
}

@-webkit-keyframes save-the-date-pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(0.95, 0.95, 0.95);
    transform: scale3d(0.95, 0.95, 0.95);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes save-the-date-pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(0.95, 0.95, 0.95);
    transform: scale3d(0.95, 0.95, 0.95);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

/*--------------------------------------------------------------
21. wpo-blog-single-section
--------------------------------------------------------------*/
.wpo-blog-single-section {
  /*** tag-share ***/
  /*** author-box ***/
  /*** more-posts ***/
  /*** comments area ***/
  /*** comment-respond ***/
}

.wpo-blog-single-section .entry-meta {
  list-style: none;
  overflow: hidden;
  margin: 35px 0;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .entry-meta {
    margin: 25px 0;
  }
}

.wpo-blog-single-section .entry-meta ul {
  list-style: none;
}

.wpo-blog-single-section .entry-meta ul li {
  font-weight: 500;
  font-size: 16px;
  font-size: 1.06667rem;
  float: left;
  text-transform: uppercase;
}

.wpo-blog-single-section .entry-meta ul li a {
  color: #636893;
}

.wpo-blog-single-section .entry-meta ul li a:hover {
  color: #4540E1;
}

.wpo-blog-single-section .entry-meta ul li i {
  position: relative;
  top: 0;
  margin-right: 3px;
}

.wpo-blog-single-section .entry-meta ul li i.fi:before {
  font-size: 13px;
}

.wpo-blog-single-section .entry-meta ul li + li {
  margin-left: 20px;
  padding-left: 20px;
  position: relative;
}

.wpo-blog-single-section .entry-meta ul li + li:before {
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 7px;
  height: 7px;
  content: "";
  background: #4540E1;
  border-radius: 50%;
}

@media (max-width: 440px) {
  .wpo-blog-single-section .entry-meta ul li + li:before {
    display: none;
  }
}

@media (max-width: 440px) {
  .wpo-blog-single-section .entry-meta ul li + li {
    margin-left: 0;
    padding-left: 0;
  }
}

@media (max-width: 767px) {
  .wpo-blog-single-section .entry-meta ul li {
    font-size: 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 440px) {
  .wpo-blog-single-section .entry-meta ul li {
    float: none;
    display: block;
    margin-bottom: 5px;
  }
}

.wpo-blog-single-section .entry-media img {
  width: 100%;
}

.wpo-blog-single-section .post h2 {
  font-size: 35px;
  font-size: 2.33333rem;
  margin: -0.22em 0 0.7em;
  line-height: 1.3em;
  font-family: "Urbanist";
}

@media (max-width: 991px) {
  .wpo-blog-single-section .post h2 {
    font-size: 30px;
    font-size: 2rem;
  }
}

@media (max-width: 767px) {
  .wpo-blog-single-section .post h2 {
    font-size: 25px;
    font-size: 1.66667rem;
  }
}

.wpo-blog-single-section .post p {
  margin-bottom: 1.5em;
}

.wpo-blog-single-section .post h3 {
  font-size: 24px;
  font-size: 1.6rem;
  font-weight: 600;
  line-height: 1.3em;
  margin: 1.8em 0 1em;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .post h3 {
    font-size: 22px;
    font-size: 1.46667rem;
  }
}

@media (max-width: 767px) {
  .wpo-blog-single-section .post h3 {
    font-size: 20px;
    font-size: 1.33333rem;
  }
}

.wpo-blog-single-section .post blockquote {
  background-color: #f2f2f2;
  color: #233d62;
  font-size: 20px;
  line-height: 1.6em;
  padding: 65px;
  margin-top: 60px;
  margin-bottom: 40px;
  border: 0;
  text-align: center;
  position: relative;
  text-decoration: underline;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .post blockquote {
    padding: 55px 25px;
  }
}

.wpo-blog-single-section .post blockquote:before {
  content: "\f101";
  font-family: "Flaticon";
  font-size: 25px;
  font-size: 1.66667rem;
  color: rgba(255, 255, 255, 0.5);
  position: absolute;
  left: 50%;
  top: -30px;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  line-height: 60px;
  border: 2px solid #FCF9EF;
  background: #fff;
  color: #4540E1;
  border-radius: 50%;
}

.wpo-blog-single-section .post .gallery {
  overflow: hidden;
  margin: 40px -7.5px 0;
}

.wpo-blog-single-section .post .gallery > div {
  width: calc(50% - 15px);
  float: left;
  margin: 0 7.5px 15px;
}

.wpo-blog-single-section .post .gallery img {
  width: 100%;
}

.wpo-blog-single-section .tag-share,
.wpo-blog-single-section .tag-share-s2 {
  border-bottom: 1px solid #D9D9D9;
  margin: 75px 0 0;
  padding-bottom: 30px;
  color: #1A1729;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .tag-share,
  .wpo-blog-single-section .tag-share-s2 {
    margin-top: 40px;
  }
}

.wpo-blog-single-section .tag-share ul,
.wpo-blog-single-section .tag-share-s2 ul {
  list-style: none;
  display: inline-block;
  overflow: hidden;
}

.wpo-blog-single-section .tag-share ul li,
.wpo-blog-single-section .tag-share-s2 ul li {
  float: left;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .tag-share ul li,
  .wpo-blog-single-section .tag-share-s2 ul li {
    margin: 2px;
  }
}

.wpo-blog-single-section .tag-share ul > li + li,
.wpo-blog-single-section .tag-share-s2 ul > li + li {
  margin-left: 10px;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .tag-share ul > li + li,
  .wpo-blog-single-section .tag-share-s2 ul > li + li {
    margin: 2px;
  }
}

.wpo-blog-single-section .tag-share .tag,
.wpo-blog-single-section .tag-share-s2 .tag {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wpo-blog-single-section .tag-share .tag > span,
.wpo-blog-single-section .tag-share-s2 .tag > span {
  font-family: "Urbanist";
  color: #1A1729;
  font-weight: 600;
  display: inline-block;
  padding-right: 15px;
  text-transform: uppercase;
}

.wpo-blog-single-section .tag-share .tag ul,
.wpo-blog-single-section .tag-share-s2 .tag ul {
  list-style: none;
  position: relative;
}

.wpo-blog-single-section .tag-share .tag li,
.wpo-blog-single-section .tag-share-s2 .tag li {
  position: relative;
}

.wpo-blog-single-section .tag-share .tag a,
.wpo-blog-single-section .tag-share-s2 .tag a {
  font-size: 14px;
  font-size: 0.93333rem;
  display: inline-block;
  padding: 5px 18px;
  color: #1A1729;
  background: #f2f2f2;
  border-radius: 5px;
  text-transform: uppercase;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .tag-share .tag a,
  .wpo-blog-single-section .tag-share-s2 .tag a {
    font-size: 13px;
    font-size: 0.86667rem;
  }
}

.wpo-blog-single-section .tag-share .tag a:hover,
.wpo-blog-single-section .tag-share-s2 .tag a:hover {
  color: #4540E1;
}

.wpo-blog-single-section .tag-share-s2 {
  margin: 0;
  margin-top: 30px;
  border-bottom: 0;
}

.wpo-blog-single-section .tag-share-s2 .tag a {
  padding: 0;
  font-size: 16px;
  font-size: 1.06667rem;
  text-transform: capitalize;
  background: none;
  text-decoration: underline;
  color: #625F71;
}

.wpo-blog-single-section .author-box {
  margin: 35px 0 60px;
}

.wpo-blog-single-section .author-box .author-avatar {
  float: left;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .author-box .author-avatar {
    float: none;
  }
}

.wpo-blog-single-section .author-box .author-avatar img {
  border-radius: 50%;
}

.wpo-blog-single-section .author-box .author-content {
  display: block;
  overflow: hidden;
  padding-left: 25px;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .author-box .author-content {
    padding: 0;
    margin: 15px 0 0 0;
  }
}

.wpo-blog-single-section .author-box .author-content p {
  margin-bottom: 20px;
}

.wpo-blog-single-section .author-box .author-name {
  font-family: "Urbanist";
  font-size: 24px;
  font-size: 1.6rem;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 10px;
  color: #1A1729;
}

.wpo-blog-single-section .author-box .social-link {
  display: inline-block;
  list-style: none;
}

.wpo-blog-single-section .author-box .social-link li {
  float: left;
  margin-right: 12px;
}

.wpo-blog-single-section .author-box .social-link a {
  display: block;
  font-size: 13px;
  font-size: 0.86667rem;
  color: #1A1729;
}

.wpo-blog-single-section .author-box .social-link a:hover {
  color: #4540E1;
}

.wpo-blog-single-section .more-posts {
  overflow: hidden;
  border: 1px solid #DDDDDD;
  padding: 0 25px;
}

.wpo-blog-single-section .more-posts > div {
  width: 50%;
  float: left;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts > div {
    width: 100%;
    float: none;
  }
}

.wpo-blog-single-section .more-posts > div > a {
  display: inline-block;
}

.wpo-blog-single-section .more-posts .previous-post,
.wpo-blog-single-section .more-posts .next-post {
  padding: 40px 0;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts .previous-post,
  .wpo-blog-single-section .more-posts .next-post {
    padding: 25px 15px !important;
  }
}

.wpo-blog-single-section .more-posts .next-post {
  text-align: right;
  border-left: 1px solid #DDDDDD;
  padding-left: 15px;
  padding-right: 5px;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts .next-post {
    border-left: 0;
    text-align: left;
    border-top: 1px solid #DDDDDD;
  }
}

.wpo-blog-single-section .more-posts .next-post .post-control-link {
  padding-right: 25px;
  position: relative;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts .next-post .post-control-link {
    padding-right: 0;
  }
}

.wpo-blog-single-section .more-posts .next-post .post-control-link:before {
  font-family: "themify";
  content: "\e628";
  position: absolute;
  right: 0;
  top: 0;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts .next-post .post-control-link:before {
    display: none;
  }
}

.wpo-blog-single-section .more-posts .previous-post {
  padding-right: 15px;
  padding-left: 5px;
}

.wpo-blog-single-section .more-posts .previous-post .post-control-link {
  padding-left: 25px;
  position: relative;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts .previous-post .post-control-link {
    padding-left: 0;
  }
}

.wpo-blog-single-section .more-posts .previous-post .post-control-link:before {
  font-family: "themify";
  content: "\e629";
  position: absolute;
  left: 0;
  top: 0;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .more-posts .previous-post .post-control-link:before {
    display: none;
  }
}

.wpo-blog-single-section .more-posts .previous-post > a > span,
.wpo-blog-single-section .more-posts .next-post > a > span {
  display: block;
}

.wpo-blog-single-section .more-posts .post-control-link {
  font-size: 14px;
  font-size: 0.93333rem;
  color: #625F71;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 2px;
}

.wpo-blog-single-section .more-posts .post-name {
  font-size: 16px;
  font-size: 1.06667rem;
  color: #1A1729;
  margin: 0.7em 0 0;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .more-posts .post-name {
    font-size: 16px;
    font-size: 1.06667rem;
  }
}

.wpo-blog-single-section .more-posts a:hover .post-control-link {
  color: #4540E1;
}

.wpo-blog-single-section .comments-area {
  margin-top: 70px;
}

.wpo-blog-single-section .comments-area li > div {
  border-bottom: 1px solid #e4effb;
  padding: 35px;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .comments-area li > div {
    padding: 35px 25px;
  }
}

.wpo-blog-single-section .comments-area ol {
  list-style-type: none;
  padding-left: 0;
}

.wpo-blog-single-section .comments-area ol ul {
  padding-left: 30px;
  list-style-type: none;
}

.wpo-blog-single-section .comments-area ol > li:last-child div {
  border-bottom: 0;
}

.wpo-blog-single-section .comments-area .comments-title {
  font-size: 22px;
  font-size: 1.46667rem;
  font-weight: 500;
  font-family: "Urbanist";
  margin: 0 0 1em;
  text-transform: uppercase;
  letter-spacing: 3px;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .comments-area .comments-title {
    font-size: 20px;
    font-size: 1.33333rem;
  }
}

.wpo-blog-single-section .comments-area li > div {
  position: relative;
}

.wpo-blog-single-section .comments-area .comment-theme {
  position: absolute;
  left: 35px;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .comments-area .comment-theme {
    position: static;
  }
}

.wpo-blog-single-section .comments-area .comment-theme img {
  border-radius: 50%;
}

.wpo-blog-single-section .comments-area .comment-main-area {
  padding-left: 100px;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .comments-area .comment-main-area {
    padding-left: 0;
    margin-top: 25px;
  }
}

.wpo-blog-single-section .comments-area .comment-main-area p {
  margin-bottom: 20px;
}

.wpo-blog-single-section .comments-area .comments-meta h4 {
  font-family: "Urbanist";
  font-size: 20px;
  font-size: 1.33333rem;
  color: #1A1729;
  font-weight: 600;
  margin: 0 0 1em;
}

.wpo-blog-single-section .comments-area .comments-meta h4 span {
  font-size: 15px;
  font-size: 1rem;
  color: #625F71;
  font-weight: normal;
  text-transform: none;
  display: inline-block;
  padding-left: 5px;
  font-family: "Urbanist";
}

@media (max-width: 767px) {
  .wpo-blog-single-section .comments-area .comments-meta h4 span {
    padding-left: 0;
  }
}

.wpo-blog-single-section .comments-area .comment-reply-link {
  font-family: "Urbanist";
  font-size: 13px;
  font-size: 0.86667rem;
  font-weight: 600;
  color: #1A1729;
  text-align: center;
  border-radius: 50px;
  display: inline-block;
  font-family: "Urbanist";
  text-decoration: underline;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.wpo-blog-single-section .comments-area .comment-reply-link:hover {
  color: #4540E1;
}

.wpo-blog-single-section .comment-respond {
  margin-top: 70px;
}

.wpo-blog-single-section .comment-respond .comment-reply-title {
  font-size: 22px;
  font-size: 1.46667rem;
  margin: 0 0 1.5em;
  text-transform: uppercase;
  letter-spacing: 2px;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .comment-respond .comment-reply-title {
    font-size: 20px;
    font-size: 1.33333rem;
  }
}

.wpo-blog-single-section .comment-respond form input,
.wpo-blog-single-section .comment-respond form textarea {
  background-color: #fff;
  width: 100%;
  height: 55px;
  border: 1px solid #a4adbe;
  padding: 6px 15px;
  margin-bottom: 15px;
  outline: 0;
  border-radius: 30px;
  -webkit-box-shadow: none;
          box-shadow: none;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
}

.wpo-blog-single-section .comment-respond form input:focus,
.wpo-blog-single-section .comment-respond form textarea:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #4540E1;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .comment-respond form input,
  .wpo-blog-single-section .comment-respond form textarea {
    height: 40px;
  }
}

.wpo-blog-single-section .comment-respond form textarea {
  height: 220px;
  padding: 15px;
}

@media (max-width: 991px) {
  .wpo-blog-single-section .comment-respond form textarea {
    height: 150px;
  }
}

.wpo-blog-single-section .comment-respond .form-inputs {
  overflow: hidden;
}

.wpo-blog-single-section .comment-respond .form-inputs > input:nth-child(1) {
  width: 49%;
  float: left;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .comment-respond .form-inputs > input:nth-child(1) {
    width: 100%;
    float: none;
  }
}

.wpo-blog-single-section .comment-respond .form-inputs > input:nth-child(2) {
  width: 49%;
  float: right;
}

@media (max-width: 767px) {
  .wpo-blog-single-section .comment-respond .form-inputs > input:nth-child(2) {
    width: 100%;
    float: none;
  }
}

.wpo-blog-single-section .comment-respond .form-submit input {
  font-family: "Urbanist";
  max-width: 180px;
  background-color: #1e2845;
  color: #fff;
  margin-bottom: 0;
  border: 0;
  outline: 0;
  text-transform: uppercase;
  font-size: 15px;
  letter-spacing: 2px;
  border-radius: 30px;
}

.wpo-blog-single-section .comment-respond .form-submit input:hover {
  background-color: #1A1729;
}

@media screen and (min-width: 1200px) {
  .wpo-blog-single-left-sidebar-section .blog-sidebar {
    padding-right: 45px;
    padding-left: 0;
  }
}

/*--------------------------------------------------------------
22. wpo-contact-pg-section
--------------------------------------------------------------*/
.wpo-contact-pg-section {
  padding-bottom: 0px;
}

.wpo-contact-pg-section .wpo-contact-title {
  max-width: 440px;
  margin: 0 auto;
  text-align: center;
  margin-bottom: 50px;
}

@media (max-width: 767px) {
  .wpo-contact-pg-section .wpo-contact-title {
    margin-bottom: 30px;
  }
}

.wpo-contact-pg-section .wpo-contact-title h2 {
  font-size: 35px;
  font-size: 2.33333rem;
  font-weight: 600;
  margin-bottom: 20px;
}

@media (max-width: 575px) {
  .wpo-contact-pg-section .wpo-contact-title h2 {
    font-size: 25px;
    font-size: 1.66667rem;
  }
}

.wpo-contact-pg-section form input,
.wpo-contact-pg-section form select,
.wpo-contact-pg-section form textarea {
  background: transparent;
  width: 100%;
  height: 50px;
  border: 1px solid transparent;
  border-radius: 0px;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  padding-left: 25px;
  border: 1px solid #ebebeb;
}

@media (max-width: 991px) {
  .wpo-contact-pg-section form input,
  .wpo-contact-pg-section form select,
  .wpo-contact-pg-section form textarea {
    height: 45px;
  }
}

.wpo-contact-pg-section form input:focus,
.wpo-contact-pg-section form select:focus,
.wpo-contact-pg-section form textarea:focus {
  border-color: #4540E1;
  background: transparent;
}

.wpo-contact-pg-section form textarea {
  height: 180px;
  padding-top: 15px;
}

.wpo-contact-pg-section form {
  margin: 0 -15px;
  overflow: hidden;
}

.wpo-contact-pg-section form ::-webkit-input-placeholder {
  font-style: 15px;
  font-style: normal;
  color: #9d9c9c;
}

.wpo-contact-pg-section form :-moz-placeholder {
  font-style: 15px;
  font-style: normal;
  color: #9d9c9c;
}

.wpo-contact-pg-section form ::-moz-placeholder {
  font-style: 15px;
  font-style: normal;
  color: #9d9c9c;
}

.wpo-contact-pg-section form :-ms-input-placeholder {
  font-style: 15px;
  font-style: normal;
  color: #9d9c9c;
}

.wpo-contact-pg-section form select {
  display: inline-block;
  color: #a9a9a9;
  cursor: pointer;
  opacity: 1;
  padding: 6px 25px;
  font-size: 15px;
  font-size: 1rem;
  -webkit-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  appearance: none;
  -moz-appearance: none;
  background: transparent url(../../images/select-icon2.png) no-repeat calc(100% - 15px) center;
  position: relative;
}

.wpo-contact-pg-section form select:focus {
  background: transparent url(../../images/select-icon2.png) no-repeat calc(100% - 15px) center;
}

.wpo-contact-pg-section form .submit-area {
  text-align: center;
  width: 100%;
  margin-bottom: 10px;
  margin-left: 0;
}

@media (max-width: 767px) {
  .wpo-contact-pg-section form .submit-area {
    margin-bottom: 0;
  }
}

.wpo-contact-pg-section form .submit-area .theme-btn, .wpo-contact-pg-section form .submit-area .view-cart-btn {
  border-radius: 0px;
  font-family: "Urbanist";
  font-size: 16px;
}

.wpo-contact-pg-section form .submit-area .theme-btn:after, .wpo-contact-pg-section form .submit-area .view-cart-btn:after {
  border-radius: 0px;
}

.wpo-contact-pg-section form > div {
  width: calc(50% - 30px);
  float: left;
  margin: 0 15px 25px;
}

@media (max-width: 600px) {
  .wpo-contact-pg-section form > div {
    width: calc(100% - 25px);
    float: none;
  }
}

.wpo-contact-pg-section form .fullwidth {
  width: calc(100% - 25px);
  float: none;
  clear: both;
}

.wpo-contact-pg-section .office-info {
  padding-bottom: 100px;
}

@media (max-width: 767px) {
  .wpo-contact-pg-section .office-info {
    padding-bottom: 60px;
  }
}

@media (max-width: 1200px) {
  .wpo-contact-pg-section .office-info .col .office-info-item {
    margin-bottom: 30px;
  }
}

@media (max-width: 1200px) {
  .wpo-contact-pg-section .office-info .col:last-child .office-info-item {
    margin-bottom: 0px;
  }
}

.wpo-contact-pg-section .office-info .office-info-item {
  text-align: center;
  -webkit-box-shadow: 0px 5px 15px 0px rgba(62, 65, 159, 0.1);
          box-shadow: 0px 5px 15px 0px rgba(62, 65, 159, 0.1);
  padding: 40px;
}

.wpo-contact-pg-section .office-info .office-info-item .office-info-icon {
  width: 85px;
  height: 85px;
  background: #f5f5f5;
  line-height: 85px;
  text-align: center;
  margin: 0 auto;
  border-radius: 50%;
}

.wpo-contact-pg-section .office-info .office-info-item .office-info-icon .icon .fi:before {
  font-size: 35px;
}

.wpo-contact-pg-section .office-info .office-info-item .office-info-text h2 {
  font-size: 30px;
  font-weight: 500;
  margin: 20px 0;
}

.wpo-contact-pg-section .office-info .office-info-item .office-info-text p {
  margin-bottom: 0;
}

.wpo-contact-pg-section .wpo-contact-form-area {
  padding: 50px;
  background: #fff;
  -webkit-box-shadow: 0px 1px 15px 0px rgba(62, 65, 159, 0.1);
          box-shadow: 0px 1px 15px 0px rgba(62, 65, 159, 0.1);
  padding-bottom: 0;
  margin-bottom: -125px;
  position: relative;
  z-index: 99;
}

@media (max-width: 767px) {
  .wpo-contact-pg-section .wpo-contact-form-area {
    padding: 30px;
    padding-top: 50px;
  }
}

.wpo-contact-map-section .wpo-contact-map {
  height: 550px;
}

.wpo-contact-map-section iframe {
  width: 100%;
  height: 100%;
  border: 0;
  outline: 0;
}

.wpo-contact-map-section h2.hidden {
  display: none;
}

/*--------------------------------------------------------------
23. error-404-section
--------------------------------------------------------------*/
.error-404-section {
  text-align: center;
}

.error-404-section .error-message {
  margin-top: 70px;
  padding: 0 200px;
}

@media (max-width: 991px) {
  .error-404-section .error-message {
    margin-top: 50px;
    padding: 0 100px;
  }
}

@media (max-width: 767px) {
  .error-404-section .error-message {
    padding: 0;
  }
}

.error-404-section .error-message h3 {
  font-size: 30px;
  font-size: 2rem;
  margin: 0 0 0.8em;
}

.error-404-section .error-message p {
  margin-bottom: 1.8em;
}

/*--------------------------------------------------------------
24. wpo-terms-section
--------------------------------------------------------------*/
.wpo-terms-section h4 {
  font-size: 30px;
  margin-bottom: 30px;
}

.wpo-terms-section .wpo-terms-wrap .wpo-terms-img img {
  width: 100%;
}

.wpo-terms-section .wpo-terms-wrap .wpo-terms-text {
  padding-top: 30px;
}

.wpo-terms-section .wpo-terms-wrap .wpo-terms-text h2 {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 20px;
}

@media (max-width: 991px) {
  .wpo-terms-section .wpo-terms-wrap .wpo-terms-text h2 {
    font-size: 30px;
  }
}

@media (max-width: 767px) {
  .wpo-terms-section .wpo-terms-wrap .wpo-terms-text h2 {
    font-size: 25px;
  }
}

.wpo-terms-section .wpo-terms-wrap .wpo-terms-text ul {
  list-style: none;
  margin-top: 20px;
  margin-bottom: 40px;
}

.wpo-terms-section .wpo-terms-wrap .wpo-terms-text ul li {
  padding: 10px;
  position: relative;
  padding-left: 20px;
}

.wpo-terms-section .wpo-terms-wrap .wpo-terms-text ul li:before {
  position: absolute;
  left: 0;
  top: 50%;
  width: 8px;
  height: 8px;
  background: #4540E1;
  content: "";
  border-radius: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.wpo-terms-section .wpo-terms-wrap .wpo-terms-text .t-sub {
  padding: 20px 0;
}

.wpo-terms-section .wpo-terms-wrap .wpo-terms-text .t-sub .wpo-p-details-img {
  margin-bottom: 20px;
}

.wpo-terms-section .wpo-faq-section {
  margin-top: 30px;
}

.wpo-terms-section .wpo-faq-section {
  margin-top: 30px;
}

.wpo-terms-section .accordion-item {
  border: 0;
  border: 1px solid #FCF9EF;
  padding: 5px 20px;
  margin-bottom: 20px;
  background: #FCF9EF;
}

.wpo-terms-section .accordion-item .accordion-body {
  padding-left: 0;
  padding-right: 0;
  border-top: 1px solid rgba(255, 255, 255, 0.13);
}

.wpo-terms-section .accordion-item .accordion-body p {
  color: #625F71;
}

.wpo-terms-section .accordion-item button {
  padding: 20px;
  border: 0;
  border-radius: 0;
  padding-left: 0;
  padding-right: 0;
  font-size: 18px;
  color: #1A1729;
  text-align: left;
  font-family: "Urbanist";
}

.wpo-terms-section .accordion-item button::after {
  background: none;
  font-family: "themify";
  content: "\e622";
  font-size: 15px;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  font-weight: 700;
}

.wpo-terms-section .accordion-item button.collapsed {
  color: #1A1729;
}

.wpo-terms-section .accordion-item button.collapsed::after {
  content: "\e61a";
}

.wpo-terms-section .accordion-item button:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none;
  border-color: #e1e1e1;
}

.wpo-terms-section .accordion-item .accordion-collapse {
  border: 0;
}

.wpo-terms-section .accordion-button {
  background: transparent;
}

.wpo-terms-section .accordion-button:not(.collapsed) {
  -webkit-box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.13);
          box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.13);
}
/*# sourceMappingURL=style.css.map */