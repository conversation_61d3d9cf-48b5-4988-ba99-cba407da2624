package com.abonnements_microservices.services;

import com.abonnements_microservices.model.Abonnement;
import com.abonnements_microservices.model.Chapitre;
import com.abonnements_microservices.model.Cours;
import com.abonnements_microservices.repo.ChapitreRepository;
import com.abonnements_microservices.repo.CoursRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class CoursService {
    @Autowired
    private CoursRepository coursRepository;
    @Autowired
    private ChapitreRepository chapitreRepository;

    public List<Cours> getAllCours() {
        List<Cours> cours = coursRepository.findAll();
        // Ensure chapitre is loaded for each cours
        cours.forEach(c -> {
            if (c.getChapitre() != null) {
                c.getChapitre().getNomChapitre(); // Force load the chapitre
            }
        });
        return cours;
    }

    public Page<Cours> getAllCoursParPage(int page, int size) {
        Page<Cours> coursPage = coursRepository.findAll(PageRequest.of(page, size));
        // Ensure chapitre is loaded for each cours in the page
        coursPage.getContent().forEach(c -> {
            if (c.getChapitre() != null) {
                c.getChapitre().getNomChapitre(); // Force load the chapitre
            }
        });
        return coursPage;
    }

    public Optional<Cours> getCoursById(Long id) {
        return coursRepository.findById(id);
    }

    public List<Cours> getCoursByChapitre(Long chapitreId) {
        return coursRepository.findByChapitreId(chapitreId);
    }
     public List<Cours> getCoursByTitre(String titre) {
	        return coursRepository.findByTitreContainingIgnoreCase(titre);
	    }

    public Cours createCours(Cours cours, Long chapitreId) {
        Chapitre chapitre = chapitreRepository.findById(chapitreId)
                .orElseThrow(() -> new RuntimeException("Chapitre non trouvé avec l'ID: " + chapitreId));

        cours.setChapitre(chapitre);
        return coursRepository.save(cours);
    }

    @Transactional
    public Cours updateCours(Long id, Cours updatedCours) {
        Cours existingCours = coursRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Cours non trouvé avec l'ID: " + id));

        // Update basic fields if they are not null
        if (updatedCours.getTitre() != null && !updatedCours.getTitre().trim().isEmpty()) {
            existingCours.setTitre(updatedCours.getTitre().trim());
        }
        
        if (updatedCours.getDescription() != null) {
            existingCours.setDescription(updatedCours.getDescription().trim());
        }
        
        // Handle duree as Integer
        if (updatedCours.getDuree() != null) {
            existingCours.setDuree(updatedCours.getDuree());
        }
        
        // Handle dateCreation
        if (updatedCours.getDateCreation() != null) {
            existingCours.setDateCreation(updatedCours.getDateCreation());
        }
        
        // Handle video link
        if (updatedCours.getLien() != null) {
            existingCours.setLien(updatedCours.getLien().trim());
        }
        
        // Handle PDF - only update if new PDF is provided
        if (updatedCours.getPdf() != null && updatedCours.getPdf().length > 0) {
            existingCours.setPdf(updatedCours.getPdf());
        }

        // Handle chapitre relationship - only if trying to change chapter
        if (updatedCours.getChapitre() != null && updatedCours.getChapitre().getId() != null) {
            Chapitre chapitre = chapitreRepository.findById(updatedCours.getChapitre().getId())
                .orElseThrow(() -> new RuntimeException("Chapitre non trouvé avec l'ID: " + updatedCours.getChapitre().getId()));
            existingCours.setChapitre(chapitre);
        }

        try {
            return coursRepository.save(existingCours);
        } catch (Exception e) {
            throw new RuntimeException("Erreur lors de la sauvegarde du cours: " + e.getMessage());
        }
    }

    public void deleteCours(Long id) {
        coursRepository.deleteById(id);
    }

    public long countCours() {
        return coursRepository.count();
    }

    public long getNombreCoursParChapitre(Long chapitreId) {
        return coursRepository.countByChapitreId(chapitreId);
    }

    public void updateVideoLink(Long coursId, String videoUrl) {
        Cours cours = coursRepository.findById(coursId)
                .orElseThrow(() -> new RuntimeException("Cours non trouvé avec l'ID: " + coursId));
        cours.setLien(videoUrl);
        coursRepository.save(cours);
    }
}
