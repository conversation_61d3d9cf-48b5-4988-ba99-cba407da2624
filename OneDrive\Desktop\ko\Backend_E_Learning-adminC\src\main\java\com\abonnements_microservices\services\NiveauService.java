package com.abonnements_microservices.services;

import com.abonnements_microservices.dto.NiveauDTO;
import com.abonnements_microservices.model.Niveau;
import com.abonnements_microservices.repo.NiveauRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class NiveauService {
    
    @Autowired
    private NiveauRepository niveauRepository;

   /* public List<Niveau> getAllNiveaux() {
        return niveauRepository.findAll();
    }*/
    public List<Niveau> getAllNiveaux() {
        return niveauRepository.findAllWithRelations(); // Utilisez une méthode custom
    }

    public Optional<Niveau> getNiveauById(Long id) {
        return niveauRepository.findById(id);
    }

    public Niveau createNiveau(Niveau niveau) {
        return niveauRepository.save(niveau);
    }

    @Transactional
    public Niveau updateNiveau(Long id, Niveau updatedNiveau) {
        return niveauRepository.findById(id)
            .map(niveau -> {
                if (updatedNiveau.getNom() != null) {
                    niveau.setNom(updatedNiveau.getNom());
                }
                return niveauRepository.save(niveau);
            })
            .orElseThrow(() -> new EntityNotFoundException("Niveau not found with id: " + id));
    }

    @Transactional
    public void deleteNiveau(Long id) {
        Niveau niveau = niveauRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Niveau not found with id: " + id));
        niveauRepository.delete(niveau);
    }


    public Niveau createNiveauFromDTO(NiveauDTO dto) {
        Niveau niveau = new Niveau();
        niveau.setNom(dto.getNom());
        // Gestion des relations si nécessaire
        return niveauRepository.save(niveau);
    }

    public long countNiveaux() {
        return niveauRepository.count();
    }
}

