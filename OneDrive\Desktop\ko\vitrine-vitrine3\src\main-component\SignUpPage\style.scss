// Variables - Using the provided color palette
$theme-primary-color: #37A7DF;
$theme-primary-color-dark: #2176d8; 
$theme-secondary-color: #F2BC00;
$theme-secondary-color-dark: #e0ad00; 
$theme-accent-color: #248E39;
$theme-dark-blue: #000080;
$theme-dark-gray: #1D1D1B;
$theme-light-gray: #B7B7B7;
$theme-light-bg: #EEF9F5;
$theme-cream-bg: #F6F4EE;
$white: #fff;
$border-color: #B7B7B7;

// Modern form styling
.inputOutline {
  margin-bottom: 20px !important;

  label {
    font-size: 15px !important;
    font-weight: 600 !important;
    background: $white;
    padding: 0 8px 0 0 !important;
    color: $theme-dark-blue !important;
  }

  input, select {
    font-size: 15px !important;
    padding: 14px 16px !important;
    border-radius: 30px !important;
    transition: all 0.3s ease !important;
    border-color: $border-color !important;
    background-color: $theme-cream-bg !important;
    color: $theme-dark-gray !important;

    &:focus {
      border-color: $theme-primary-color !important;
      box-shadow: 0 0 0 2px rgba(55, 167, 223, 0.2) !important;
      background-color: $white !important;
    }

    &:hover {
      border-color: $theme-secondary-color !important;
    }
  }

  // Placeholder styling
  input::placeholder {
    color: $theme-light-gray !important;
    opacity: 1 !important;
  }
}

// Error message styling
.errorMessage {
  color: #d32f2f;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 16px;
  font-weight: 500;
}

// Custom button styling
.MuiButton-contained {
  text-transform: none !important;
  font-size: 16px !important;
  letter-spacing: 0.5px !important;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(242, 188, 0, 0.4) !important;
  }

  &:active {
    transform: translateY(0);
  }
}

// Theme button styling
.theme-btn {
  position: relative;
  z-index: 1;
  overflow: hidden;
  background: linear-gradient(135deg, $theme-secondary-color 0%, $theme-secondary-color-dark 100%) !important;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
    z-index: -1;
  }

  &:hover::after {
    left: 100%;
  }

  &:hover {
    background: linear-gradient(135deg, $theme-secondary-color-dark 0%, $theme-secondary-color 100%) !important;
  }
}

// Responsive adjustments
@media (max-width: 600px) {
  .MuiContainer-root {
    padding: 0 16px !important;
  }

  .MuiPaper-root {
    border-radius: 12px !important;
  }
}

// Animation for form elements
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(15px); }
  to { opacity: 1; transform: translateY(0); }
}

.MuiGrid-item {
  animation: fadeIn 0.6s ease-out forwards;
}

// Staggered animation for form fields
@for $i from 1 through 12 {
  .MuiGrid-item:nth-child(#{$i}) {
    animation-delay: #{$i * 0.07}s;
  }
}

// Custom styling for select dropdowns
.MuiSelect-select {
  display: flex !important;
  align-items: center !important;
}

// Focus styles for form elements
.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: $theme-primary-color !important;
  border-width: 2px !important;
}

// Form field hover effect
.MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: $theme-primary-color-dark !important;
}

// Decorative elements
.decorative-circle {
  position: absolute;
  border-radius: 50%;
  z-index: 0;
  animation: pulse 4s infinite ease-in-out;

  &:nth-child(odd) {
    background: rgba($theme-secondary-color, 0.15);
    border: 1px solid rgba($theme-secondary-color, 0.3);
  }

  &:nth-child(even) {
    background: rgba($theme-primary-color, 0.15);
    border: 1px solid rgba($theme-primary-color, 0.3);
  }
}

// Decorative square
.decorative-square {
  position: absolute;
  z-index: 0;
  animation: rotate 20s infinite linear;
  border: 2px solid rgba($theme-accent-color, 0.2);
  background: rgba($theme-accent-color, 0.05);
}

// Background pattern
.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23F2BC00' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.3;
  pointer-events: none;
}

// Additional animation
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Niveau Select Styling
// Niveau Select Styling
/* Appliquer les mêmes styles que inputOutline à niveauSelect */
.niveauSelect {
  margin-bottom: 10px !important;

  .MuiOutlinedInput-root {
    font-size: 6px !important;
    padding: 8px 10px !important; /* Même padding que inputOutline */
    border-radius: 15px !important; /* Même bordure arrondie */
    transition: all 0.3s ease !important;
    border-color: $border-color !important; /* Même couleur de bordure */
    background-color: $theme-cream-bg !important; /* Même fond */
    color: $theme-dark-gray !important;

    /* Ajuster le padding pour la flèche déroulante */
    .MuiSelect-select {
      padding-right: 32px !important; /* Espace pour la flèche */
    }

    /* Survol */
    &:hover {
      border-color: $theme-secondary-color !important; /* Même effet de survol */
    }

    /* Focus */
    &:focus {
      border-color: $theme-primary-color !important; /* Même effet de focus */
      box-shadow: 0 0 0 2px rgba(55, 167, 223, 0.2) !important;
      background-color: $white !important;
    }

    /* Ajuster la bordure */
    .MuiOutlinedInput-notchedOutline {
      border-color: $border-color !important;
    }

    &:hover .MuiOutlinedInput-notchedOutline {
      border-color: $theme-primary-color !important;
    }

    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      border-color: $theme-primary-color !important;
      border-width: 2px !important;
    }
  }

  /* Même style pour le label */
  .MuiInputLabel-root {
    font-size: 12px !important;
    font-weight: 700 !important;
    background: $white;
    padding: 0 8px 0 0 !important;
    color: $theme-dark-blue !important;
  }
}