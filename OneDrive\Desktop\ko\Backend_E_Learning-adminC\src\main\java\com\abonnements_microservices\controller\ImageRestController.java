package com.abonnements_microservices.controller;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.abonnements_microservices.model.Image;
import com.abonnements_microservices.repo.ImageRepository;
import com.abonnements_microservices.services.ImageService;

import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;

@RestController
@RequestMapping("/api/image")
@CrossOrigin(origins = "*")
public class ImageRestController {

    @Autowired
    private ImageService imageService;
    
    @Autowired
    private ImageRepository imageRepository;

    @PostMapping("/upload")
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT','ETUDIANT')")
    public Image uploadImage(@RequestParam("image") MultipartFile file) throws IOException {
        return imageService.uplaodImage(file);
    }

    @GetMapping("/get/info/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT','ETUDIANT')")
    public Image getImageDetails(@PathVariable("id") Long id) throws IOException {
        return imageService.getImageDetails(id);
    }

    @GetMapping("/load/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT','ETUDIANT')")
    public ResponseEntity<byte[]> loadImage(@PathVariable(required = false) Long id) {
        if (id == null) {
            // Retourner une image par défaut si l'ID est null
            InputStream in = getClass().getResourceAsStream("/static/default-image.jpg");
            try {
                byte[] defaultImage = IOUtils.toByteArray(in);
                return ResponseEntity.ok()
                    .contentType(MediaType.IMAGE_JPEG)
                    .body(defaultImage);
            } catch (IOException e) {
                return ResponseEntity.internalServerError().build();
            }
        }

        return imageRepository.findById(id)
            .map(image -> ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(image.getType()))
                .body(image.getImage()))
            .orElseGet(() -> {
                // Image non trouvée - retourner une image par défaut
                InputStream in = getClass().getResourceAsStream("/static/default-image.jpg");
                try {
                    byte[] defaultImage = IOUtils.toByteArray(in);
                    return ResponseEntity.ok()
                        .contentType(MediaType.IMAGE_JPEG)
                        .body(defaultImage);
                } catch (IOException e) {
                    return ResponseEntity.notFound().build();
                }
            });
    }

    @DeleteMapping("/delete/{id}")
    public void deleteImage(@PathVariable("id") Long id) {
        imageService.deleteImage(id);
    }

    @PutMapping("/update")
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT','ETUDIANT')")
    public Image UpdateImage(@RequestParam("image") MultipartFile file) throws IOException {
        return imageService.uplaodImage(file);
    }
}