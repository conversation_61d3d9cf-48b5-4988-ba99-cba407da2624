package com.abonnements_microservices.services;

import java.io.IOException;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import com.abonnements_microservices.model.Image;
import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.repo.ImageRepository;
import com.abonnements_microservices.repo.MatiereRepository;

@Service
public class ImageServiceImpl implements ImageService {

    @Autowired
    private ImageRepository imageRepository;
    @Autowired
    private MatiereRepository matiereRepository;

    @Override
    public Image uplaodImage(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("Le fichier ne peut pas être vide");
        }

        Image image = new Image();
        image.setName(file.getOriginalFilename());
        image.setType(file.getContentType());
        image.setImage(file.getBytes());
        return imageRepository.save(image);
    }

    @Override
    public Image getImageDetails(Long id) throws IOException {
        Optional<Image> dbImage = imageRepository.findById(id);
        if (dbImage.isPresent()) {
            Image image = dbImage.get();
            return image;
        } else {
            throw new RuntimeException("Image non trouvée avec ID : " + id);
        }
    }

    @Override
    public ResponseEntity<byte[]> getImage(Long id) throws IOException {
        Optional<Image> dbImage = imageRepository.findById(id);
        if (dbImage.isPresent()) {
            return ResponseEntity.ok()
                    .contentType(MediaType.valueOf(dbImage.get().getType()))
                    .body(dbImage.get().getImage());
        } else {
            throw new RuntimeException("Image non trouvée avec ID : " + id);
        }
    }

    @Override
    public void deleteImage(Long id) {
        imageRepository.deleteById(id);
    }

    @Override
    public Image uplaodImageMatiere(MultipartFile file, Long idMat) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("Le fichier ne peut pas être vide");
        }

        Matiere m = matiereRepository.findById(idMat)
                .orElseThrow(() -> new RuntimeException("Matière non trouvée avec ID : " + idMat));

        Image img = new Image();
        img.setName(file.getOriginalFilename());
        img.setType(file.getContentType());
        img.setImage(file.getBytes());
        img.setMatiere(m);

        img = imageRepository.save(img);
        m.setImage(img);
        matiereRepository.save(m);

        return img;
    }
}
