package com.abonnements_microservices.services;

import java.io.IOException;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.abonnements_microservices.model.Abonnement;
import com.abonnements_microservices.model.ImageAbonnement;
import com.abonnements_microservices.repo.AbonnementRepository;
import com.abonnements_microservices.repo.ImageAbonnementRepository;

@Service
public class ImageAbonnementImpl implements ImageAbonnementService {

    @Autowired
    private ImageAbonnementRepository imageAbonnementRepository;
    @Autowired
    private AbonnementRepository abonnementRepository;

    @Override
    public ImageAbonnement uplaodImage(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("Le fichier ne peut pas être vide");
        }

        ImageAbonnement imageAbonnement = new ImageAbonnement();
        imageAbonnement.setName(file.getOriginalFilename());
        imageAbonnement.setType(file.getContentType());
        imageAbonnement.setImage(file.getBytes());
        return imageAbonnementRepository.save(imageAbonnement);
    }

    @Override
    public ImageAbonnement getImageDetails(Long id) throws IOException {
        Optional<ImageAbonnement> dbImage = imageAbonnementRepository.findById(id);
        if (dbImage.isPresent()) {
            return dbImage.get();
        } else {
            throw new RuntimeException("Image non trouvée avec ID : " + id);
        }
    }

    @Override
    public ResponseEntity<byte[]> getImage(Long id) throws IOException {
        Optional<ImageAbonnement> dbImage = imageAbonnementRepository.findById(id);
        if (dbImage.isPresent()) {
            return ResponseEntity.ok()
                    .contentType(MediaType.valueOf(dbImage.get().getType()))
                    .body(dbImage.get().getImage());
        } else {
            throw new RuntimeException("Image non trouvée avec ID : " + id);
        }
    }

    @Override
    public void deleteImage(Long id) {
        imageAbonnementRepository.deleteById(id);
    }

    @Override
    public ImageAbonnement uplaodImageAbonnement(MultipartFile file, Long idAbon) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("Le fichier ne peut pas être vide");
        }

        Abonnement abonnement = abonnementRepository.findById(idAbon)
                .orElseThrow(() -> new RuntimeException("Abonnement non trouvé avec ID : " + idAbon));

        ImageAbonnement imageAbonnement = new ImageAbonnement();
        imageAbonnement.setName(file.getOriginalFilename());
        imageAbonnement.setType(file.getContentType());
        imageAbonnement.setImage(file.getBytes());
        imageAbonnement.setAbonnement(abonnement);

        imageAbonnement = imageAbonnementRepository.save(imageAbonnement);
        abonnement.setImage(imageAbonnement);
        abonnementRepository.save(abonnement);

        return imageAbonnement;
    }
}
