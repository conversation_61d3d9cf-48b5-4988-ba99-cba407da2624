import React from "react";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

const ModifierAbonnement = ({ abonnement }) => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    nom: "",
    détails: "",
    dateDebut: "",
    dateFin: "",
    prix: "",
    photo: null,
  });

  useEffect(() => {
    // Remplir le formulaire avec les données existantes de l'abonnement
    if (abonnement) {
      setFormData({
        nom: abonnement.nom || "",
        détails: abonnement.détails || "",
        dateDebut: abonnement.dateDebut || "",
        dateFin: abonnement.dateFin || "",
        prix: abonnement.prix || "",
        photo: abonnement.photo || null,
      });
    }
  }, [abonnement]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleFileChange = (e) => {
    setFormData({ ...formData, photo: e.target.files[0] });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    // Ajouter la logique pour envoyer la modification au serveur ou effectuer d'autres actions
  };

  return (
    <div className="content-body">
      <div className="container-fluid">
        <div className="row page-titles mx-0">
          <div className="col-sm-6 p-md-0">
            <h4>Modifier Abonnement</h4>
          </div>
        </div>

        <div className="row">
          <div className="col-lg-12">
            <div className="card">
              <div className="card-header">
                <h4 className="card-title">
                  Modifier les détails d'abonnement
                </h4>
              </div>
              <div className="card-body">
                <form onSubmit={handleSubmit}>
                  <div className="form-group">
                    <label>Nom d'abonnement</label>
                    <input
                      type="text"
                      name="nom"
                      className="form-control"
                      value={formData.nom}
                      required
                      onChange={handleChange}
                    />
                  </div>
                  <div className="form-group">
                    <label>Détails d'abonnement</label>
                    <input
                      type="text"
                      name="détails"
                      className="form-control"
                      value={formData.détails}
                      required
                      onChange={handleChange}
                    />
                  </div>
                  <div className="form-group">
                    <label>Date de début</label>
                    <input
                      type="date"
                      name="dateDebut"
                      className="form-control"
                      value={formData.dateDebut}
                      required
                      onChange={handleChange}
                    />
                  </div>
                  <div className="form-group">
                    <label>Date de fin</label>
                    <input
                      type="date"
                      name="dateFin"
                      className="form-control"
                      value={formData.dateFin}
                      required
                      onChange={handleChange}
                    />
                  </div>
                  <div className="form-group">
                    <label>Prix d'abonnement</label>
                    <input
                      type="number"
                      name="prix"
                      className="form-control"
                      value={formData.prix}
                      required
                      onChange={handleChange}
                    />
                  </div>
                  <div className="form-group">
                    <label>Photo</label>
                    <input
                      type="file"
                      name="photo"
                      className="form-control"
                      onChange={handleFileChange}
                    />
                  </div>
                  <button type="submit" className="btn btn-primary" onClick={() => navigate("/Affichage")}>
                    Modifier
                  </button>
                  <button type="reset" className="btn btn-danger light" >
                    Annuler
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModifierAbonnement;
