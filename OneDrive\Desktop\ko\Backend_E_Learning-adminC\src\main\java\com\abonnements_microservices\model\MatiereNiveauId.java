package com.abonnements_microservices.model;

import jakarta.persistence.Embeddable;
import lombok.*;

import java.io.Serializable;
import java.util.Objects;

@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MatiereNiveauId implements Serializable {

    private Long matiereId;
    private Long niveauId;

    // equals et hashCode sont nécessaires pour les clés composites
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof MatiereNiveauId)) return false;
        MatiereNiveauId that = (MatiereNiveauId) o;
        return Objects.equals(matiereId, that.matiereId) &&
               Objects.equals(niveauId, that.niveauId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(matiereId, niveauId);
    }
}
