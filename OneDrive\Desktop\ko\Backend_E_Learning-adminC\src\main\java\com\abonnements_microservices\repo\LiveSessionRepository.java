package com.abonnements_microservices.repo;

import com.abonnements_microservices.model.LiveSession;
import com.abonnements_microservices.model.LiveSessionStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface LiveSessionRepository extends JpaRepository<LiveSession, Long> {
    List<LiveSession> findByEnseignantId(Long enseignantId);
    List<LiveSession> findByMatiereIdMatiere(Long matiereId);
    List<LiveSession> findByStatus(LiveSessionStatus status);
    List<LiveSession> findByScheduledStartTimeBetween(LocalDateTime start, LocalDateTime end);
    List<LiveSession> findByEnseignantIdAndStatus(Long enseignantId, LiveSessionStatus status);
}
