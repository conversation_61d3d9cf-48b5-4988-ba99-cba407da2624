import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosService";
import Select from 'react-select';

// Fonction pour générer un mot de passe aléatoire
const generatePassword = (length = 10) => {
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+";
  let password = "";
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }
  return password;
};


const AjouterEnseignant = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: "",
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "", // Ajout du champ pour le numéro de téléphone
    password: generatePassword(12), // Génère un mot de passe aléatoire de 12 caractères
    niveauIds: [],
    matiereIds: [],
  });

  // État pour stocker le mot de passe généré
  const [generatedPassword, setGeneratedPassword] = useState("");

  const [niveaux, setNiveaux] = useState([]);
  const [matieres, setMatieres] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [niveauxResponse, matieresResponse] = await Promise.all([
          axiosInstance.get("/api/niveaux/all"),
          axiosInstance.get("/api/matieres"),
        ]);
        setNiveaux(niveauxResponse.data);
        setMatieres(matieresResponse.data);

        // Stocker le mot de passe généré
        setGeneratedPassword(formData.password);
      } catch (err) {
        setError("Erreur lors du chargement des données");
        console.error(err);
      }
    };
    fetchData();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // La fonction handleMultiSelect a été remplacée par handleSelectChange
  const handleSelectChange = (selectedOptions, field) => {
    const selectedValues = selectedOptions.map(option => option.value);
    setFormData({ ...formData, [field]: selectedValues });
};


  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation des champs obligatoires
    if (!formData.username) {
      setError("Le nom d'utilisateur est obligatoire");
      return;
    }

    if (!formData.email) {
      setError("L'email est obligatoire pour envoyer les identifiants de connexion");
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Afficher les données envoyées au backend pour le débogage
      console.log("Données envoyées au backend:", formData);

      await axiosInstance.post("/api/enseignants", formData);
      setSuccess(true);
      // Générer un nouveau mot de passe pour le prochain enseignant
      const newPassword = generatePassword(12);

      setFormData({
        username: "",
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "", // Réinitialisation du numéro de téléphone
        password: newPassword,
        niveauIds: [],
        matiereIds: [],
      });

      // Stocker le nouveau mot de passe généré
      setGeneratedPassword(newPassword);
      setTimeout(() => {
        navigate("/enseignants");
      }, 2000);
    } catch (err) {
      setError(
        err.response?.data || "Erreur lors de la création de l'enseignant"
      );
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0">
        <div className="col-sm-6 p-md-0">
          <div className="welcome-text">
            <h4>Ajouter un enseignant</h4>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-xl-12 col-xxl-12">
          <div className="card">
            <div className="card-body">
              {error && (
                <div className="alert alert-danger" role="alert">
                  {error}
                </div>
              )}
              {success && (
                <div className="alert alert-success" role="alert">
                  <p>Enseignant créé avec succès!</p>
                  <p>Un email contenant les identifiants de connexion a été envoyé à l'adresse : <strong>{formData.email}</strong></p>
                </div>
              )}
              <form onSubmit={handleSubmit}>
                <div className="row">
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">
                        Login de l'enseignant*
                      </label>
                      <input
                        type="text"
                        name="username"
                        className="form-control"
                        placeholder="Entrez le login"
                        value={formData.username}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">
                        Mot de passe
                      </label>
                      <div className="alert alert-info">
                        <i className="la la-info-circle me-2"></i>
                        Le mot de passe sera généré automatiquement et envoyé par email à l'enseignant.
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">Prénom*</label>
                      <input
                        type="text"
                        name="firstName"
                        className="form-control"
                        placeholder="Prénom de l'enseignant"
                        value={formData.firstName}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">Nom*</label>
                      <input
                        type="text"
                        name="lastName"
                        className="form-control"
                        placeholder="Nom de l'enseignant"
                        value={formData.lastName}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">Email*</label>
                      <input
                        type="email"
                        name="email"
                        className="form-control"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={handleChange}
                        required
                      />
                    </div>
                  </div>
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">Numéro de téléphone</label>
                      <input
                        type="tel"
                        name="phoneNumber"
                        className="form-control"
                        placeholder="+216 XX XXX XXX"
                        value={formData.phoneNumber}
                        onChange={handleChange}
                      />
                      <small className="text-muted">
                        Ce numéro sera utilisé pour envoyer un SMS de bienvenue à l'enseignant.
                        Format recommandé: +216XXXXXXXX
                      </small>
                    </div>
                  </div>
                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">
                        Niveaux d'enseignement*
                      </label>
                      <Select
                        isMulti
                        options={niveaux.map(niveau => ({ value: niveau.id, label: niveau.nom }))}
                        onChange={(selectedOptions) => handleSelectChange(selectedOptions, 'niveauIds')}
                        placeholder="Sélectionner plusieurs niveaux"
                        styles={{
                            control: (provided) => ({
                                ...provided,
                                width: '100%',
                                borderColor: '#ccc',
                                borderRadius: '4px',
                                padding: '5px',
                            }),
                            menu: (provided) => ({
                                ...provided,
                                borderRadius: '4px',
                                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                            }),
                        }}
                    />

                    </div>
                  </div>

                  <div className="col-lg-6 mb-2">
                    <div className="form-group">
                      <label className="text-label">Matières enseignées*</label>
                    <Select
                    isMulti
                    options={matieres.map(matiere => ({ value: matiere.idMatiere, label: matiere.nomMatiere }))}
                    onChange={(selectedOptions) => handleSelectChange(selectedOptions, 'matiereIds')}
                    placeholder="Sélectionner plusieurs matières"
                    styles={{
                        control: (provided) => ({
                            ...provided,
                            width: '210%',
                            borderColor: '#ccc',
                            borderRadius: '4px',
                            padding: '5px',
                        }),
                        menu: (provided) => ({
                            ...provided,
                            borderRadius: '4px',
                            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        }),
                    }}
                />

                    </div>
                  </div>

                  <div className="col-lg-12 mt-3">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={loading}
                      style={{
                        backgroundColor: "#37A7DF",
                        borderColor: "#37A7DF",
                      }}
                    >
                      {loading ? "Création en cours..." : "Créer l'enseignant"}
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AjouterEnseignant;
