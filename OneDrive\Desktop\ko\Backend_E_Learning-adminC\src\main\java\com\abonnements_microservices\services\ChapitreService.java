package com.abonnements_microservices.services;

import com.abonnements_microservices.dto.ChapitreDTO;
import com.abonnements_microservices.model.Chapitre;
import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.model.MatiereNiveau;
import com.abonnements_microservices.model.MatiereNiveauId;
import com.abonnements_microservices.model.Niveau;
import com.abonnements_microservices.repo.ChapitreRepository;
import com.abonnements_microservices.repo.MatiereNiveauRepository;
import com.abonnements_microservices.repo.MatiereRepository;
import com.abonnements_microservices.repo.NiveauRepository;

import jakarta.persistence.EntityNotFoundException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

@Service
public class ChapitreService {

    @Autowired
    private ChapitreRepository chapitreRepository;

    @Autowired
    private MatiereRepository matiereRepository;

    @Autowired
    private MatiereNiveauRepository matiereNiveauRepository;

    @Autowired
    private NiveauRepository niveauRepository;

    public Page<Chapitre> getAllChapitreParPage(int page, int size) {
        PageRequest pageRequest = PageRequest.of(page, size);
        return chapitreRepository.findAll(pageRequest);
    }

    public Page<Chapitre> getChapitreParPage(Long matiereId, int page, int size) {
        PageRequest pageRequest = PageRequest.of(page, size);
        return chapitreRepository.findAll(pageRequest);
    }

    public List<Chapitre> getChapitreByNomChapitre(String nomChapitre) {
        return chapitreRepository.findByNomChapitreContainingIgnoreCase(nomChapitre);
    }

   /* @Transactional
    public Chapitre createChapitre(Chapitre chapitre, Long matiereId, Long niveauId) {
        Matiere matiere = matiereRepository.findById(matiereId)
                .orElseThrow(() -> new RuntimeException("Matière non trouvée"));

        Niveau niveau = niveauRepository.findById(niveauId)
                .orElseThrow(() -> new RuntimeException("Niveau non trouvé"));

        MatiereNiveau matiereNiveau = matiereNiveauRepository
                .findByMatiereAndNiveau(matiere, niveau)
                .orElseGet(() -> {
                    MatiereNiveau newMn = MatiereNiveau.builder()
                            .matiere(matiere)
                            .niveau(niveau)
                            .build();
                    return matiereNiveauRepository.save(newMn);
                });

        chapitre.setMatiereNiveau(matiereNiveau);
        return chapitreRepository.save(chapitre);
    }*/
    @Transactional
    public Chapitre createChapitre(ChapitreDTO dto) {
        // Vérifie que la matière existe
        Matiere matiere = matiereRepository.findById(dto.getMatiereId())
                .orElseThrow(() -> new RuntimeException("Matière non trouvée avec l'id: " + dto.getMatiereId()));

        // Si le niveau n'est pas spécifié, utiliser le premier niveau associé à la matière
        Niveau niveau;
        if (dto.getNiveauId() == null) {
            // Récupérer le premier niveau associé à la matière
            List<MatiereNiveau> matiereNiveaux = matiereNiveauRepository.findByMatiereId(dto.getMatiereId());
            if (matiereNiveaux.isEmpty()) {
                // Si aucun niveau n'est associé à la matière, utiliser le premier niveau disponible
                niveau = niveauRepository.findAll().stream().findFirst()
                        .orElseThrow(() -> new RuntimeException("Aucun niveau disponible"));
            } else {
                niveau = matiereNiveaux.get(0).getNiveau();
            }
        } else {
            // Vérifie que le niveau existe
            niveau = niveauRepository.findById(dto.getNiveauId())
                    .orElseThrow(() -> new RuntimeException("Niveau non trouvé avec l'id: " + dto.getNiveauId()));
        }

        // Cherche ou crée l'association MatiereNiveau
        MatiereNiveau matiereNiveau = matiereNiveauRepository
                .findByMatiereAndNiveau(matiere, niveau)
                .orElseGet(() -> {
                    MatiereNiveau newMn = new MatiereNiveau();
                    newMn.setMatiere(matiere);
                    newMn.setNiveau(niveau);
                    return matiereNiveauRepository.save(newMn);
                });

        // Crée le chapitre
        Chapitre chapitre = new Chapitre();
        chapitre.setNomChapitre(dto.getNomChapitre());
        chapitre.setNomDeProf(dto.getNomDeProf());
        chapitre.setDuree(dto.getDuree() != null ? dto.getDuree() : 0L);
        chapitre.setNombreDeCours(dto.getNombreDeCours() != null ? dto.getNombreDeCours() : 0L);
        chapitre.setDescription(dto.getDescription());
        chapitre.setMatiereNiveau(matiereNiveau);

        // Enregistre et retourne le chapitre
        return chapitreRepository.save(chapitre);
    }


    public List<Chapitre> getAllChapitres() {
        return chapitreRepository.findAll();
    }

    public Optional<Chapitre> getChapitreById(Long id) {
        return chapitreRepository.findById(id);
    }

    public List<Chapitre> getChapitresByMatiereNiveau(Long matiereId, Long niveauId) {
        return chapitreRepository.findByMatiereNiveau(matiereId, niveauId);
    }

    /**
     * Get all chapitres for a specific matiere regardless of niveau
     * @param matiereId the ID of the matiere
     * @return List of chapitres for the matiere
     */
    public List<Chapitre> getChapitresByMatiere(Long matiereId) {
        // Check if matiere exists
        if (!matiereRepository.existsById(matiereId)) {
            throw new RuntimeException("Matière non trouvée avec l'id: " + matiereId);
        }

        // Get all MatiereNiveau associations for this matiere
        List<MatiereNiveau> matiereNiveaux = matiereNiveauRepository.findByMatiereId(matiereId);

        // If no MatiereNiveau associations found, return empty list
        if (matiereNiveaux.isEmpty()) {
            return java.util.Collections.emptyList();
        }

        // Create a list to store all chapitres
        List<Chapitre> allChapitres = new java.util.ArrayList<>();

        // For each MatiereNiveau, get the chapitres and add them to the list
        for (MatiereNiveau mn : matiereNiveaux) {
            List<Chapitre> chapitres = chapitreRepository.findByMatiereNiveau(mn.getMatiere().getId(), mn.getNiveau().getId());
            allChapitres.addAll(chapitres);
        }

        return allChapitres;
    }


    @Transactional
    public Chapitre updateChapitre(Long chapitreId, Chapitre updatedChapitre) {
        // Récupérer l'entité Chapitre existante
        Chapitre existingChapitre = chapitreRepository.findById(chapitreId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Chapitre non trouvé"));

        // Mise à jour des champs simples
        existingChapitre.setNomChapitre(updatedChapitre.getNomChapitre());
        existingChapitre.setNomDeProf(updatedChapitre.getNomDeProf());
        existingChapitre.setDuree(updatedChapitre.getDuree());
        existingChapitre.setNombreDeCours(updatedChapitre.getNombreDeCours());
        existingChapitre.setDescription(updatedChapitre.getDescription());

        // Ne pas toucher aux Matiere et Niveau si tu ne veux pas les modifier
        // existingChapitre.setMatiere(updatedChapitre.getMatiere()); // Ne pas inclure cette ligne
        // existingChapitre.setNiveau(updatedChapitre.getNiveau()); // Ne pas inclure cette ligne

        // Sauvegarder et retourner l'objet mis à jour
        return chapitreRepository.save(existingChapitre);
    }
    @Transactional
    public void deleteChapitreByMatiereNiveau(Long matiereId, Long niveauId) {
        chapitreRepository.deleteByMatiereIdAndNiveauId(matiereId, niveauId);
    }
    public Chapitre addChapitre(Chapitre chapitre) {
        return chapitreRepository.save(chapitre);
    }
    @Transactional
    public Chapitre ajouterChapitre(ChapitreDTO dto) {
        // Vérification des IDs
        Matiere matiere = matiereRepository.findById(dto.getMatiereId())
                .orElseThrow(() -> new RuntimeException("Matière non trouvée"));

        Niveau niveau = niveauRepository.findById(dto.getNiveauId())
                .orElseThrow(() -> new RuntimeException("Niveau non trouvé"));

        // Gestion de la relation MatiereNiveau
        MatiereNiveau matiereNiveau = matiereNiveauRepository
                .findByMatiereAndNiveau(matiere, niveau)
                .orElseGet(() -> {
                    MatiereNiveau newMn = new MatiereNiveau();
                    newMn.setMatiere(matiere);
                    newMn.setNiveau(niveau);
                    return matiereNiveauRepository.save(newMn);
                });

        // Création du chapitre
        Chapitre chapitre = new Chapitre();
        chapitre.setNomChapitre(dto.getNomChapitre());
        chapitre.setNomDeProf(dto.getNomDeProf());
        chapitre.setDuree(dto.getDuree());
        chapitre.setNombreDeCours(dto.getNombreDeCours());
        chapitre.setDescription(dto.getDescription());
        chapitre.setMatiereNiveau(matiereNiveau);

        return chapitreRepository.save(chapitre);
    }
}
