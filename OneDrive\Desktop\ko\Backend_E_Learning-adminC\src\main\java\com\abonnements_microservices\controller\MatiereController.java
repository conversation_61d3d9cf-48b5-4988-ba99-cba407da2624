package com.abonnements_microservices.controller;

import com.abonnements_microservices.dto.AbonnementDTO;
import com.abonnements_microservices.dto.ImageDTO;
import com.abonnements_microservices.dto.MatiereDTO;
import com.abonnements_microservices.dto.NiveauDTO;
import com.abonnements_microservices.model.Abonnement;
import com.abonnements_microservices.model.ImageAbonnement;
import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.services.MatiereService;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.persistence.EntityNotFoundException;

import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/matieres")
@CrossOrigin(origins = {"http://localhost:3036", "http://localhost:3000"})
public class MatiereController {
    @Autowired
    private MatiereService matiereService;

    @GetMapping
    public ResponseEntity<List<Matiere>> getAllMatieres() {
        return ResponseEntity.ok(matiereService.getAllMatieres());
    }

    @GetMapping("/{id}")
    public ResponseEntity<Matiere> getMatiereById(@PathVariable Long id) {
        Matiere matiere = matiereService.getMatiereById(id); // Utilisation de la méthode getMatiereById
        if (matiere != null) {
            return ResponseEntity.ok(matiere);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    @PutMapping(value = "/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> updateMatiere(
            @PathVariable Long id,
            @RequestParam("nomMatiere") String nom,
            @RequestParam("description") String description,
            @RequestParam("duree") Integer duree,
            @RequestParam(value = "image", required = false) MultipartFile imageFile,
            @RequestParam(value = "abonnementIds", required = false) Set<Long> abonnementIds,
            @RequestParam(value = "niveauIds", required = false) Set<Long> niveauIds
    ) {
        try {
            MatiereDTO dto = new MatiereDTO();
            dto.setNomMatiere(nom);
            dto.setDescription(description);
            dto.setDuree(duree);

            // Image
            if (imageFile != null && !imageFile.isEmpty()) {
                ImageDTO imageDTO = new ImageDTO();
                imageDTO.setName(imageFile.getOriginalFilename());
                imageDTO.setType(imageFile.getContentType());
                imageDTO.setImage(imageFile.getBytes());
                dto.setImage(imageDTO);
            }

            // Abonnements
            if (abonnementIds != null) {
            	// Abonnements
            	if (abonnementIds != null) {
            	    Set<AbonnementDTO> abDtos = abonnementIds.stream()
            	            .map(idAb -> {
            	                AbonnementDTO a = new AbonnementDTO();
            	                a.setId(idAb);
            	                return a;
            	            })
            	            .collect(Collectors.toSet()); // ✅ ici
            	    dto.setAbonnementIds(abDtos);
            	}

            	// Niveaux
            	if (niveauIds != null) {
            	    Set<NiveauDTO> niveauDtos = niveauIds.stream()
            	            .map(idNv -> {
            	                NiveauDTO n = new NiveauDTO();
            	                n.setId(idNv);
            	                return n;
            	            })
            	            .collect(Collectors.toSet()); // ✅ ici aussi
            	    dto.setNiveauIds(niveauDtos);
            	}}

            Matiere updated = matiereService.updateMatiere(id, dto);
            return ResponseEntity.ok(updated);

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Erreur lors de la mise à jour : " + e.getMessage());
        }
    }



    /*  @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateMatiereSansImage(
            @PathVariable Long id,
            @RequestBody Matiere matiereDetails) {
        try {
            // Appeler la méthode pour mettre à jour la matière sans images
            Matiere updatedMatiere = matiereService.updateMatiereSansImage(id, matiereDetails);
            return ResponseEntity.ok(updatedMatiere);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }
    @PutMapping(value = "/update/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> updateAbonnement(
            @PathVariable Long id,
            @RequestParam("nomMatiere") String nom,
            @RequestParam("description") String description,
            @RequestParam("duree") Long duree,
            @RequestParam(value = "image", required = false) MultipartFile imageFile,
            @RequestParam(value = "abonnementIds", required = false) List<Long> abonnementIds
    ) {
        // récupère l'matiere existant
        Optional<matiere> abonnementOpt = abonnementRepository.findById(id);
        if (abonnementOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        Abonnement abonnement = abonnementOpt.get();
        abonnement.setNom(nom);
        abonnement.setDescription(description);
        abonnement.setPrix(prix);

        // update image si présente
        if (imageFile != null && !imageFile.isEmpty()) {
            try {
                ImageAbonnement image = new ImageAbonnement();
                image.setName(imageFile.getOriginalFilename());
                image.setType(imageFile.getContentType());
                image.setImage(imageFile.getBytes()); // Cette méthode peut lancer une IOException
                abonnement.setImageAbonnement(image);
            } catch (IOException e) {
                e.printStackTrace(); // Log l'erreur, ou utiliser un mécanisme de gestion des erreurs
                // Traiter l'erreur comme il faut
            }
        }

        // update matieres
        if (matiereIds != null) {
            Set<Matiere> matieres = new HashSet<>(matiereRepository.findAllById(matiereIds));
            abonnement.setMatieres(matieres);
        }

        abonnementRepository.save(abonnement);
        return ResponseEntity.ok(abonnement);
    }

*/

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteMatiere(@PathVariable Long id) {
        try {
            matiereService.deleteMatiere(id);
            return ResponseEntity.ok("Matière supprimée avec succès !");
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Erreur lors de la suppression de la matière: " + e.getMessage());
        }
    }

    @PostMapping(value = "/add", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasRole('ADMIN')")

    public ResponseEntity<Matiere> createMatiere(
    		  @RequestParam String nomMatiere,
    		    @RequestParam String description,
    		    @RequestParam int duree,
    		    @RequestParam List<Long> niveauIds,
    		    @RequestParam List<Long> abonnementIds,
            @RequestParam(value = "image", required = false) MultipartFile imageFile) {

        Matiere matiere = new Matiere();
        matiere.setNomMatiere(nomMatiere);
        matiere.setDescription(description);
        matiere.setDuree(duree);

        Matiere savedMatiere = matiereService.createMatiereWithImageAbonnementsAndNiveaux(
                matiere, abonnementIds, niveauIds, imageFile);
        return ResponseEntity.ok(savedMatiere);
    }


    @GetMapping("/search")
    public List<Matiere> getMatieresByNom(@RequestParam String nom) {
        return matiereService.getMatieresByNom(nom);
    }

    @GetMapping("/page")
    public ResponseEntity<Page<MatiereDTO>> getAllMatieres(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "8") int size) {
        
        Page<Matiere> matieresPage = matiereService.getAllMatiereParPage(page, size);
        
        Page<MatiereDTO> dtoPage = matieresPage.map(matiere -> {
            // Initialisation explicite des relations
            Hibernate.initialize(matiere.getAbonnements());
            Hibernate.initialize(matiere.getMatiereNiveaux());
            if (matiere.getImage() != null) {
                Hibernate.initialize(matiere.getImage());
            }
            
            return new MatiereDTO(matiere);
        });
        
        return ResponseEntity.ok(dtoPage);
    }
}

