package com.abonnements_microservices.services;
import com.abonnements_microservices.dto.AbonnementDTO;
import com.abonnements_microservices.dto.MatiereDTO;
import com.abonnements_microservices.dto.NiveauDTO;
import com.abonnements_microservices.model.Abonnement;
import com.abonnements_microservices.model.Chapitre;
import com.abonnements_microservices.model.Enseignant;
import com.abonnements_microservices.model.Image;
import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.model.MatiereNiveau;
import com.abonnements_microservices.model.MatiereNiveauId;
import com.abonnements_microservices.model.Niveau;
import com.abonnements_microservices.repo.AbonnementRepository;
import com.abonnements_microservices.repo.ImageRepository;
import com.abonnements_microservices.repo.MatiereRepository;
import com.abonnements_microservices.repo.NiveauRepository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.PersistenceContext;

import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.transaction.Transactional;


@Service
public class MatiereService {
    @Autowired
    private MatiereRepository matiereRepository;
    
    @Autowired
    private AbonnementRepository abonnementRepository;
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Autowired
    private ImageService imageService;

    @Autowired
    private NiveauRepository niveauRepository;
    
    @Autowired
    private ImageRepository imageRepository;

    public List<Matiere> getAllMatieres() {
        return matiereRepository.findAllWithAbonnementsAndNiveauxAndChapitres();
    }

    public List<Matiere> getMatieresByIds(List<Long> ids) {
        List<Matiere> matieres = matiereRepository.findAllById(ids);
        matieres.forEach(m -> Hibernate.initialize(m.getAbonnements()));
        return matieres;
    }
    public Matiere getMatiereById(Long id) {
        return matiereRepository.findById(id)
                .map(matiere -> {
                    Hibernate.initialize(matiere.getAbonnements());
                    return matiere;
                })
                .orElse(null); // Retourne null si la matière n'est pas trouvée
    }


    public Matiere createMatiereWithImage(Matiere matiere, Long abonnementId, MultipartFile imageFile) {
        Abonnement abonnement = abonnementRepository.findById(abonnementId)
                .orElseThrow(() -> new RuntimeException("Abonnement non trouvé"));

        if (imageFile != null && !imageFile.isEmpty()) {
            try {
                Image image = imageService.uplaodImage(imageFile);
                matiere.setImage(image);
            } catch (IOException e) {
                throw new RuntimeException("Erreur lors du traitement de l'image", e);
            }
        }

        matiere.addAbonnement(abonnement);
        return matiereRepository.save(matiere);
    }

    public Matiere createMatiereWithImageAbonnementsAndNiveaux(
            Matiere matiere,
            List<Long> abonnementIds,
            List<Long> niveauIds,
            MultipartFile imageFile
    ) {
        // 🔹 Ajout de l'image
        if (imageFile != null && !imageFile.isEmpty()) {
            try {
                Image image = imageService.uplaodImage(imageFile);
                matiere.setImage(image);
            } catch (IOException e) {
                throw new RuntimeException("Erreur lors du traitement de l'image", e);
            }
        }

        // 🔹 Ajout des abonnements
        for (Long id : abonnementIds) {
            Abonnement abonnement = abonnementRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("Abonnement avec ID " + id + " non trouvé"));
            matiere.addAbonnement(abonnement);
        }

        // 🔹 Ajout des niveaux via MatiereNiveau
        Set<MatiereNiveau> matiereNiveaux = new HashSet<>();
        for (Long niveauId : niveauIds) {
            Niveau niveau = niveauRepository.findById(niveauId)
                    .orElseThrow(() -> new RuntimeException("Niveau avec ID " + niveauId + " non trouvé"));

            MatiereNiveau matiereNiveau = new MatiereNiveau();
            matiereNiveau.setMatiere(matiere);
            matiereNiveau.setNiveau(niveau);

            // Initialiser l'ID composite
            MatiereNiveauId id = new MatiereNiveauId();
            id.setMatiereId(matiere.getId()); // ou on le mettra à jour après le save
            id.setNiveauId(niveau.getId());
            matiereNiveau.setId(id);

            matiereNiveaux.add(matiereNiveau);
            niveau.getMatiereNiveaux().add(matiereNiveau); // bidirectionnel
        }

        matiere.setMatiereNiveaux(matiereNiveaux);

        // 🔹 Sauvegarde finale
        Matiere savedMatiere = matiereRepository.save(matiere);

        // 🔁 Mise à jour des IDs dans MatiereNiveau après génération de l'ID de la matière
        for (MatiereNiveau mn : savedMatiere.getMatiereNiveaux()) {
            if (mn.getId().getMatiereId() == null) {
                mn.getId().setMatiereId(savedMatiere.getId());
            }
        }

        return matiereRepository.save(savedMatiere);
    }


    @Transactional
    public Matiere updateMatiere(Long id, MatiereDTO dto) {
        Matiere matiere = matiereRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Matière non trouvée"));

        matiere.setNomMatiere(dto.getNomMatiere());
        matiere.setDescription(dto.getDescription());
        matiere.setDuree(dto.getDuree());

        // Gérer image si présente
        if (dto.getImage() != null) {
            Image img = new Image();
            img.setName(dto.getImage().getName());
            img.setType(dto.getImage().getType());
            img.setImage(dto.getImage().getImage());
            matiere.setImage(img);
        }

        // ⚠️ Abonnements : pas d'erreur ici normalement
        if (dto.getAbonnementIds() != null) {
            Set<Abonnement> abonnements = dto.getAbonnementIds().stream()
                .map(ab -> abonnementRepository.findById(ab.getId())
                    .orElseThrow(() -> new RuntimeException("Abonnement non trouvé")))
                .collect(Collectors.toSet());
            matiere.setAbonnements(abonnements);
        }

        // ⚠️ Niveaux : toujours détacher puis recréer proprement les relations
        if (dto.getNiveauIds() != null) {
            matiere.getMatiereNiveaux().clear(); // 💡 important
            for (NiveauDTO niveauDTO : dto.getNiveauIds()) {
                Niveau niveau = niveauRepository.findById(niveauDTO.getId())
                    .orElseThrow(() -> new RuntimeException("Niveau non trouvé"));

                MatiereNiveau mn = new MatiereNiveau();
                mn.setMatiere(matiere); // ou mn.setId(new CompositeKey(matiereId, niveauId))
                mn.setNiveau(niveau);

                matiere.getMatiereNiveaux().add(mn);
            }
        }

        return matiereRepository.save(matiere); // Hibernate gère le merge correctement ici
    }


  /*  public Matiere updateMatiere(Long id, MatiereDTO dto) {
        return matiereRepository.findById(id)
            .map(matiere -> {
                matiere.setNomMatiere(dto.getNomMatiere());
                matiere.setDescription(dto.getDescription());
                matiere.setDuree(dto.getDuree());

                // Gestion de l'image, si présente
                if (dto.getImageAbonnement() != null) {
                	abonnement.setImageAbonnement(dto.getImageAbonnement().toEntity());
                }

                // Gestion des Abonnement
                if (dto.getAbonnementIds() != null && !dto.getAbonnementIds().isEmpty()) {
                    Set<Abonnement> Abonnements = dto.getAbonnementIds().stream()
                        .map(idAbonnement -> abonnenemtRepository.findById(idAbonnement)
                            .orElseThrow(() -> new RuntimeException("Abonnement non trouvée: " + idAbonnement)))
                        .collect(Collectors.toSet());

                    // On vide les anciennes matières et on ajoute les nouvelles
                    matiere.getAbonnements().clear();
                    matieres.forEach(abonnement::addAbonnement);
                }

                return abonnementRepository.save(matiere);
            })
            .orElseThrow(() -> new RuntimeException("matiere non trouvé"));
    }*/


    @Transactional
    public void deleteMatiere(Long id) {
        Matiere matiere = entityManager.find(Matiere.class, id);
        if (matiere == null) {
            throw new EntityNotFoundException("Matière non trouvée avec l'ID: " + id);
        }

        // 🔄 1. Dissocier abonnements
        if (matiere.getAbonnements() != null) {
            for (Abonnement ab : new HashSet<>(matiere.getAbonnements())) {
                ab.getMatieres().remove(matiere);
                matiere.getAbonnements().remove(ab);
                entityManager.merge(ab);
            }
        }

        // 🔄 2. Dissocier niveaux
        if (matiere.getMatiereNiveaux() != null) {
            for (MatiereNiveau mn : new HashSet<>(matiere.getMatiereNiveaux())) {
                Niveau niveau = mn.getNiveau();
                niveau.getMatiereNiveaux().remove(mn);
                entityManager.merge(niveau);
            }
            matiere.getMatiereNiveaux().clear();
        }

        // 🧑‍🏫 3. Dissocier enseignants
        if (matiere.getEnseignants() != null) {
            for (Enseignant e : new HashSet<>(matiere.getEnseignants())) {
                e.getMatieres().remove(matiere);
                matiere.getEnseignants().remove(e);
                entityManager.merge(e);
            }
        }

        // 🖼️ 4. Supprimer image manuellement
        if (matiere.getImage() != null) {
            Image image = entityManager.find(Image.class, matiere.getImage().getIdImage());
            if (image != null) {
                matiere.setImage(null);
                entityManager.remove(image);
            }
        }

        // ✅ 5. Supprimer la matière elle-même
        entityManager.remove(matiere);
        entityManager.flush();
    }


    public Matiere addMatiere(Matiere matier) {
        return matiereRepository.save(matier);
    }

    public long countMatieres() {
        return matiereRepository.count();
    }

    public Page<Matiere> getAllMatiereParPage(int page, int size) {
        return matiereRepository.findAll(PageRequest.of(page, size));
    }

    public List<Matiere> getMatieresByNom(String nom) {
        return matiereRepository.findByNomMatiereContainingIgnoreCase(nom);
    }
}

