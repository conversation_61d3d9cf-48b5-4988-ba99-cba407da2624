package com.abonnements_microservices.dto;

import com.abonnements_microservices.model.Niveau;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NiveauDTO {
    private Long id;

    @NotBlank(message = "Le nom du niveau est obligatoire")
    private String nom;

    private List<Long> matiereIds;

    private List<Long> enseignantIds;

    public NiveauDTO() {}

    public NiveauDTO(String nom) {
        this.nom = nom;
    }

    public NiveauDTO(Niveau niveau) {
        this.id = niveau.getId();
        this.nom = niveau.getNom();

        if (niveau.getMatiereNiveaux() != null) {
            this.matiereIds = niveau.getMatiereNiveaux().stream()
                    .map(mn -> mn.getMatiere().getId())
                    .collect(Collectors.toList());
        }

        if (niveau.getEnseignants() != null) {
            this.enseignantIds = niveau.getEnseignants().stream()
                    .map(ens -> ens.getId()) // assure-toi que Enseignant a un getId()
                    .collect(Collectors.toList());
        }
    }
}
