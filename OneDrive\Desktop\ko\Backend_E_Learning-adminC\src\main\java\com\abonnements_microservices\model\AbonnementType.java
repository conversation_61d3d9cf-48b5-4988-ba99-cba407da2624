package com.abonnements_microservices.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class AbonnementType {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;

    private String nom;
    private String description;
    
    // Access flags for different features
    private boolean hasCourses = true;        // All types have access to courses
    private boolean hasRecordings = false;    // Type 2 and 3 have access to recordings
    private boolean hasLiveSessions = false;  // Only Type 3 has access to live sessions
    
    // One-to-many relationship with Abonnement
    @OneToMany(mappedBy = "type", cascade = CascadeType.ALL, orphanRemoval = false)
    @JsonIgnoreProperties("type")
    private Set<Abonnement> abonnements = new HashSet<>();
}
