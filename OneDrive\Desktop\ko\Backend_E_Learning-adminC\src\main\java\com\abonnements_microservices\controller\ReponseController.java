package com.abonnements_microservices.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.abonnements_microservices.model.Reponse;
import com.abonnements_microservices.services.ReponseService;

import jakarta.persistence.EntityNotFoundException;

@RestController
@RequestMapping("/api/forum/reponses")
@CrossOrigin(origins = {"http://localhost:3036", "http://localhost:3000"})
public class ReponseController {

    @Autowired
    private ReponseService reponseService;

    @GetMapping("/question/{questionId}")
    public ResponseEntity<List<Reponse>> getReponsesByQuestionId(@PathVariable Long questionId) {
        List<Reponse> reponses = reponseService.getReponsesByQuestionId(questionId);
        return ResponseEntity.ok(reponses);
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<Map<String, Object>> getReponsesByUser(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<Reponse> reponsesPage = reponseService.getReponsesByUser(userId, pageable);

        Map<String, Object> response = new HashMap<>();
        response.put("reponses", reponsesPage.getContent());
        response.put("currentPage", reponsesPage.getNumber());
        response.put("totalItems", reponsesPage.getTotalElements());
        response.put("totalPages", reponsesPage.getTotalPages());

        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Reponse> getReponseById(@PathVariable Long id) {
        try {
            Reponse reponse = reponseService.getReponseById(id);
            return ResponseEntity.ok(reponse);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT', 'ETUDIANT')")
    public ResponseEntity<Reponse> createReponse(
            @RequestBody Reponse reponse,
            @RequestParam Long questionId,
            @RequestParam String userId) { // Maintenant c'est un nom d'utilisateur, pas un ID

        try {
            // Utiliser le nom d'utilisateur pour créer la réponse
            Reponse createdReponse = reponseService.createReponse(reponse, questionId, userId);
            return new ResponseEntity<>(createdReponse, HttpStatus.CREATED);
        } catch (EntityNotFoundException e) {
            // Ajouter un log pour le débogage
            System.out.println("Erreur lors de la création de la réponse: " + e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            // Ajouter un log pour le débogage
            System.out.println("Erreur inattendue lors de la création de la réponse: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT', 'ETUDIANT')")
    public ResponseEntity<Reponse> updateReponse(
            @PathVariable Long id,
            @RequestBody Reponse reponseDetails) {

        try {
            Reponse updatedReponse = reponseService.updateReponse(id, reponseDetails);
            return ResponseEntity.ok(updatedReponse);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'ENSEIGNANT', 'ETUDIANT')")
    public ResponseEntity<Void> deleteReponse(@PathVariable Long id) {
        try {
            reponseService.deleteReponse(id);
            return ResponseEntity.noContent().build();
        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }
}
