package com.abonnements_microservices.repo;

import com.abonnements_microservices.model.Niveau;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface NiveauRepository extends JpaRepository<Niveau, Long> {
	@Query("SELECT DISTINCT n FROM Niveau n " +
		       "LEFT JOIN FETCH n.matiereNiveaux " +
		       "LEFT JOIN FETCH n.enseignants")
		List<Niveau> findAllWithRelations();
}
