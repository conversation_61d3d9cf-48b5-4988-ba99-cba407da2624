package com.abonnements_microservices.repo;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.abonnements_microservices.model.Reponse;
import com.abonnements_microservices.model.User;

@Repository
public interface ReponseRepository extends JpaRepository<Reponse, Long> {
    
    List<Reponse> findByQuestionIdOrderByDateCreationAsc(Long questionId);
    
    Page<Reponse> findByAuteurOrderByDateCreationDesc(User auteur, Pageable pageable);
    
    @Query("SELECT r FROM Reponse r WHERE r.question.id = :questionId AND r.acceptee = true")
    Reponse findAcceptedReponseByQuestionId(@Param("questionId") Long questionId);
}
