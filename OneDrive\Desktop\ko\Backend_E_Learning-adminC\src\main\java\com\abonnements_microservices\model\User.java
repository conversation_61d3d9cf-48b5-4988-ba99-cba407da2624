package com.abonnements_microservices.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "users")
@Inheritance(strategy = InheritanceType.JOINED)
@DiscriminatorColumn(name = "role", discriminatorType = DiscriminatorType.STRING)
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true)
    private String username;
    
    private String firstName;
    private String lastName;
    
    @Column(unique = true)
    private String email;
    
    @Column(insertable = false, updatable = false)
    private String role;  // ETUDIANT or ENSEIGNANT - Now managed by @DiscriminatorColumn
    
    // We don't store the password in our database since it's managed by Key<PERSON>loak
    @Transient
    private String password;
}