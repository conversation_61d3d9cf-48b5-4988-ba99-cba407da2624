package com.abonnements_microservices.services;

import com.abonnements_microservices.model.TextToSpeechHistory;
import com.abonnements_microservices.repository.TextToSpeechHistoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TextToSpeechService {

    @Autowired
    private TextToSpeechHistoryRepository textToSpeechHistoryRepository;

    /**
     * Save a text-to-speech conversion to history
     * 
     * @param text The text that was converted
     * @param voice The voice that was used
     * @param userId The ID of the user who made the conversion
     * @return The saved history entry
     */
    public TextToSpeechHistory saveToHistory(String text, String voice, Long userId) {
        TextToSpeechHistory history = new TextToSpeechHistory();
        history.setText(text);
        history.setVoice(voice);
        history.setUserId(userId);
        
        return textToSpeechHistoryRepository.save(history);
    }

    /**
     * Get the text-to-speech history for a user
     * 
     * @param userId The ID of the user
     * @return A list of history entries
     */
    public List<TextToSpeechHistory> getHistoryForUser(Long userId) {
        return textToSpeechHistoryRepository.findByUserIdOrderByCreatedAtDesc(userId);
    }
}
