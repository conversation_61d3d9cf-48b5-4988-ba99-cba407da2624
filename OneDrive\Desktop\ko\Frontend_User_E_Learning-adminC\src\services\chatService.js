import axiosInstance from './axiosService';

/**
 * Service for interacting with the chat API
 */
const chatService = {
  /**
   * Send a message to the chat model
   * 
   * @param {string} message - The message to send
   * @param {number} userId - The ID of the user
   * @returns {Promise} - A promise that resolves to the response from the API
   */
  sendMessage: async (message, userId) => {
    try {
      const response = await axiosInstance.post('/api/chat/send', {
        message,
        userId
      });
      return response.data;
    } catch (error) {
      console.error('Error sending message to chat model:', error);
      throw error;
    }
  },

  /**
   * Get the chat history for a user
   * 
   * @param {number} userId - The ID of the user
   * @returns {Promise} - A promise that resolves to the chat history
   */
  getHistory: async (userId) => {
    try {
      const response = await axiosInstance.get(`/api/chat/history/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting chat history:', error);
      throw error;
    }
  }
};

export default chatService;
