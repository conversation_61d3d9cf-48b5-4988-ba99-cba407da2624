package com.abonnements_microservices.dto;

import com.abonnements_microservices.model.Abonnement;
import com.abonnements_microservices.model.AbonnementType;
import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.model.ImageAbonnement;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AbonnementDTO {
    private Long id;

    @NotBlank(message = "Le nom est obligatoire")
    private String nom;

    private String description;

    @Positive(message = "Le prix doit être positif")
    private double prix;

    @Positive(message = "La durée doit être positive")
    private Integer duree; // Durée de l'abonnement en mois

    private ImageAbonnementDTO imageAbonnement;

    private Set<Long> matiereIds;

    private Long typeId;

    private AbonnementTypeDTO type;

    // Constructeur à partir de l'entité
    public AbonnementDTO(Abonnement abonnement) {
        this.id = abonnement.getId();
        this.nom = abonnement.getNom();
        this.description = abonnement.getDescription();
        this.prix = abonnement.getPrix();
        this.duree = abonnement.getDuree();

        if (abonnement.getImageAbonnement() != null) {
            this.imageAbonnement = new ImageAbonnementDTO(abonnement.getImageAbonnement());
        }

        if (abonnement.getMatieres() != null) {
            this.matiereIds = abonnement.getMatieres().stream()
                    .map(Matiere::getIdMatiere)
                    .collect(Collectors.toSet());
        }

        if (abonnement.getType() != null) {
            this.typeId = abonnement.getType().getId();
            this.type = AbonnementTypeDTO.fromEntity(abonnement.getType());
        }
    }

    // Conversion DTO → Entity (ne gère pas l'image ni les matières ici)
    public Abonnement toEntity() {
        Abonnement abonnement = new Abonnement();
        abonnement.setId(this.id);
        abonnement.setNom(this.nom);
        abonnement.setDescription(this.description);
        abonnement.setPrix(this.prix);
        abonnement.setDuree(this.duree);
        // imageAbonnement et matières sont généralement gérés ailleurs

        // Le type d'abonnement sera géré par le service
        return abonnement;
    }
}
