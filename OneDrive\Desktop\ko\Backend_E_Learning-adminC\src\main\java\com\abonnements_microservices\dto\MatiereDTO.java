package com.abonnements_microservices.dto;

import com.abonnements_microservices.model.Matiere;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;
import java.util.stream.Collectors;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public class MatiereDTO {
    private Long idMatiere;
    private String nomMatiere;
    private String description;
    private Integer duree;
    private ImageDTO image;
    private Set<AbonnementDTO> abonnementIds;
    private Set<NiveauDTO> niveauIds;

    public MatiereDTO(Matiere matiere) {
        this.idMatiere = matiere.getIdMatiere();
        this.nomMatiere = matiere.getNomMatiere();
        this.description = matiere.getDescription();
        this.duree = matiere.getDuree();

        // Image
        this.image = matiere.getImage() != null ? new ImageDTO(matiere.getImage()) : null;

        // Abonnements
        this.abonnementIds = matiere.getAbonnements().stream()
                .map(AbonnementDTO::new)
                .collect(Collectors.toSet());

        // Niveaux (via MatiereNiveau)
        this.niveauIds = matiere.getMatiereNiveaux().stream()
                .map(mn -> new NiveauDTO(mn.getNiveau()))
                .collect(Collectors.toSet());
    }

    @Override
    public String toString() {
        return "MatiereDTO{" +
                "idMatiere=" + idMatiere +
                ", nomMatiere='" + nomMatiere + '\'' +
                ", description='" + description + '\'' +
                ", duree=" + duree +
                ", image=" + image +
                ", abonnementIds=" + abonnementIds +
                ", niveauIds=" + niveauIds +
                '}';
    }
}
