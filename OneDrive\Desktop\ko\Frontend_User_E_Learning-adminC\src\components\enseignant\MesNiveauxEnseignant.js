import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button, Form } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosService";
import keycloak from "../../keycloak";

const MesNiveauxEnseignant = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [niveaux, setNiveaux] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [imageUrls, setImageUrls] = useState({});
  const [userId, setUserId] = useState(null);
  const [successMessage, setSuccessMessage] = useState("");

  const loadImage = async (imageId) => {
    try {
      const response = await axiosInstance.get(`/api/image/load/${imageId}`, {
        responseType: 'blob',
        headers: {
          Accept: 'image/*'
        }
      });
      const blobUrl = URL.createObjectURL(new Blob([response.data]));
      setImageUrls(prev => ({...prev, [imageId]: blobUrl}));
    } catch (error) {
      console.error('Error loading image:', error);
      setImageUrls(prev => ({...prev, [imageId]: prev[imageId] || 'placeholder-image.jpg'}));
    }
  };

  useEffect(() => {
    // Cleanup function to revoke blob URLs
    return () => {
      Object.values(imageUrls).forEach(url => URL.revokeObjectURL(url));
    };
  }, [imageUrls]);

  // Get the user ID from Keycloak
  useEffect(() => {
    if (keycloak.authenticated) {
      const fetchEnseignantId = async () => {
        try {
          // Get all enseignants and filter by the username from the token
          const tokenParsed = keycloak.tokenParsed;
          const username = tokenParsed.preferred_username;
          
          // First try to get all enseignants and filter by username
          const response = await axiosInstance.get('/api/enseignants');
          
          if (response.data) {
            // Find the enseignant whose username or email matches the current user
            const enseignant = response.data.find(
              e => e.username === username || 
                   e.email === keycloak.tokenParsed.email
            );
            
            if (enseignant) {
              setUserId(enseignant.id || enseignant.idEnseignant);
              console.log('Found enseignant ID:', enseignant.id || enseignant.idEnseignant);
            } else {
              // If no match found, try a simpler approach - just get the first enseignant
              // This is just for demo/testing purposes
              if (response.data.length > 0) {
                const firstEnseignant = response.data[0];
                setUserId(firstEnseignant.id || firstEnseignant.idEnseignant);
                console.log('Using first enseignant ID for testing:', firstEnseignant.id || firstEnseignant.idEnseignant);
              } else {
                setError('Aucun enseignant trouvé dans le système');
              }
            }
          }
        } catch (error) {
          console.error('Error fetching enseignants:', error);
          setError('Impossible de récupérer la liste des enseignants');
        }
      };
      
      fetchEnseignantId();
    }
  }, []);

  const niveauxParPage = 8;
  
  // Fetch the niveaux of the enseignant
  useEffect(() => {
    const fetchData = async () => {
      if (!userId) return; // Don't fetch if no userId is available
      
      try {
        setLoading(true);
        setError(null);
        
        // Fetch niveaux by enseignant ID
        const niveauxRes = await axiosInstance.get(`/api/enseignants/${userId}/niveau`);
        
        if (niveauxRes.data) {
          // If we have content property (paginated response)
          const allNiveaux = niveauxRes.data.content || niveauxRes.data;
          
          setNiveaux(allNiveaux);
          setTotalPages(Math.ceil(allNiveaux.length / niveauxParPage) || 1);
          console.log('Fetched niveaux:', allNiveaux);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        if (err.response?.status === 401) {
          setError('Session expirée. Veuillez vous reconnecter.');
        } else {
          setError(err.response?.data?.message || 'Erreur lors du chargement des données');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [userId, currentPage]);

  // Calculate the indexes for pagination display
  const indexOfLastNiveau = currentPage * niveauxParPage;
  const indexOfFirstNiveau = indexOfLastNiveau - niveauxParPage;
  const currentNiveaux = niveaux.slice(indexOfFirstNiveau, indexOfLastNiveau);

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Filtrage des niveaux selon le terme de recherche
  const niveauxFiltres = niveaux.filter((niveau) => {
    return (
      niveau.nom &&
      typeof niveau.nom === "string" &&
      niveau.nom.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  return (
      <div className="container-fluid">
        <div className="row page-titles mx-0 d-flex align-items-center justify-content-between">
          {/* Titre */}
          <div className="col-auto">
            <h4 style={{ color: "#37A7DF" }}>Mes Niveaux</h4>
          </div>

          {/* Champ de recherche */}
          <div className="col-md-4">
            <Form.Control
              type="text"
              placeholder="Rechercher un niveau..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {error && <div className="alert alert-danger">{error}</div>}
        {successMessage && <div className="alert alert-success">{successMessage}</div>}
        {loading && <div className="text-center">Chargement...</div>}

        <div className="row">
          {niveauxFiltres.length === 0 ? (
            <p>Aucun niveau trouvé.</p>
          ) : (
            niveauxFiltres.map((niveau) => {
              return (
                <div
                  key={niveau.idNiveau}
                  className="col-xl-3 col-lg-4 col-md-6 col-sm-6"
                >                  <div className="card">
                    <div className="card-body">
                      <h4>{niveau.nom}</h4>
                      <p>{niveau.description}</p>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>

        {/* Pagination : boutons pour naviguer entre les pages */}
        <div className="d-flex justify-content-center mt-3">
          <button
            className="btn btn-secondary mx-1"
            onClick={goToPrevPage}
            disabled={currentPage === 1}
          >
            Précédent
          </button>
          <span className="mx-2">
            Page {currentPage} sur {totalPages}
          </span>
          <button
            className="btn btn-secondary mx-1"
            onClick={goToNextPage}
            disabled={currentPage === totalPages}
          >
            Suivant
          </button>
        </div>
      </div>
  );
};

export default MesNiveauxEnseignant;
