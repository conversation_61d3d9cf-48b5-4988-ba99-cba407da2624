package com.abonnements_microservices.services;

import java.io.IOException;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;
import com.abonnements_microservices.model.Image;

public interface ImageService {
	Image uplaodImage(MultipartFile file) throws IOException;

	Image getImageDetails(Long id) throws IOException;

	ResponseEntity<byte[]> getImage(Long id) throws IOException;

	void deleteImage(Long id);

	Image uplaodImageMatiere(MultipartFile file, Long idMat) throws IOException;
	//List<Image> getImagesParMatiere(Long matId);

}