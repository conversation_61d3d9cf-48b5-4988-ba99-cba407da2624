package com.abonnements_microservices.services;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abonnements_microservices.model.Question;
import com.abonnements_microservices.model.Reponse;
import com.abonnements_microservices.model.User;
import com.abonnements_microservices.repo.QuestionRepository;
import com.abonnements_microservices.repo.ReponseRepository;
import com.abonnements_microservices.repo.UserRepository;

import jakarta.persistence.EntityNotFoundException;

@Service
public class ReponseService {

    @Autowired
    private ReponseRepository reponseRepository;

    @Autowired
    private QuestionRepository questionRepository;

    @Autowired
    private UserRepository userRepository;

    public List<Reponse> getReponsesByQuestionId(Long questionId) {
        return reponseRepository.findByQuestionIdOrderByDateCreationAsc(questionId);
    }

    public Page<Reponse> getReponsesByUser(Long userId, Pageable pageable) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new EntityNotFoundException("Utilisateur non trouvé avec l'ID: " + userId));
        return reponseRepository.findByAuteurOrderByDateCreationDesc(user, pageable);
    }

    @Transactional
    public Reponse createReponse(Reponse reponse, Long questionId, String username) {
        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new EntityNotFoundException("Question non trouvée avec l'ID: " + questionId));

        // Rechercher l'utilisateur par son nom d'utilisateur au lieu de son ID
        User auteur = userRepository.findByUsername(username)
                .orElseThrow(() -> new EntityNotFoundException("Utilisateur non trouvé avec le nom d'utilisateur: " + username));

        reponse.setQuestion(question);
        reponse.setAuteur(auteur);
        reponse.setDateCreation(new Date());
        reponse.setAcceptee(false);

        return reponseRepository.save(reponse);
    }

    @Transactional
    public Reponse updateReponse(Long id, Reponse reponseDetails) {
        Reponse reponse = reponseRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Réponse non trouvée avec l'ID: " + id));

        reponse.setContenu(reponseDetails.getContenu());

        return reponseRepository.save(reponse);
    }

    @Transactional
    public void deleteReponse(Long id) {
        Reponse reponse = reponseRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Réponse non trouvée avec l'ID: " + id));

        // Si cette réponse était acceptée, mettre à jour la question
        if (reponse.isAcceptee()) {
            Question question = reponse.getQuestion();
            question.setResolu(false);
            question.setReponseAccepteeId(null);
            questionRepository.save(question);
        }

        reponseRepository.delete(reponse);
    }

    public Reponse getReponseById(Long id) {
        return reponseRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Réponse non trouvée avec l'ID: " + id));
    }
}
