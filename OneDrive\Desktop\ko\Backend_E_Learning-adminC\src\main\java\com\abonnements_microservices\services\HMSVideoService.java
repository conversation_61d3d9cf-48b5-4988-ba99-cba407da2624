package com.abonnements_microservices.services;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Service for integrating with 100ms video conferencing platform.
 */
@Service
public class HMSVideoService {
    private static final Logger logger = LoggerFactory.getLogger(HMSVideoService.class);

    @Value("${hmsvideo.app.access.key}")
    private String appAccessKey;

    @Value("${hmsvideo.app.secret}")
    private String appSecret;

    @Value("${hmsvideo.management.token}")
    private String managementToken;

    @Value("${hmsvideo.token.expiration}")
    private int tokenExpirationSeconds;

    /**
     * Generate a unique room name for a session
     * @param prefix An optional prefix to add to the room name
     * @return A unique room name
     */
    public String generateRoomName(String prefix) {
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);
        return prefix != null ? prefix + "-" + uniqueId : "room-" + uniqueId;
    }

    /**
     * Generate an authentication token for 100ms
     * @param roomId The room ID
     * @param userId The user ID
     * @param username The username
     * @param isTeacher Whether the user is a teacher (will be made moderator)
     * @return A JWT token for 100ms authentication
     */
    public String generateAuthToken(String roomId, String userId, String username, boolean isTeacher) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(appSecret);
            
            Date now = new Date();
            Date expiry = new Date(now.getTime() + tokenExpirationSeconds * 1000);
            
            Map<String, Object> payload = new HashMap<>();
              // Standard JWT claims
            payload.put("access_key", appAccessKey);
            payload.put("room_id", roomId);
            payload.put("user_id", userId);
            payload.put("role", isTeacher ? "host" : "guest");
              // User metadata
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("name", username);
            metadata.put("isTeacher", isTeacher);
            
            // Add metadata to payload
            payload.put("user_name", username);
            payload.put("metadata", metadata); // Include metadata object in the payload
            payload.put("type", "app"); // Required by 100ms
            payload.put("version", 2);  // Required by 100ms
            payload.put("jti", UUID.randomUUID().toString()); // JWT ID
            
            String token = JWT.create()
                    .withPayload(payload)
                    .withIssuedAt(now)
                    .withExpiresAt(expiry)
                    .sign(algorithm);
            
            logger.debug("Generated 100ms auth token for room: {}, user: {}, role: {}", 
                    roomId, username, isTeacher ? "host" : "guest");
            
            return token;
        } catch (Exception e) {
            logger.error("Error generating 100ms auth token", e);
            throw new RuntimeException("Failed to generate 100ms authentication token", e);
        }
    }
    
    /**
     * Create a room in 100ms
     * 
     * @param roomName Name of the room
     * @param description Description of the room
     * @param templateId Optional template ID to use for the room (can be null)
     * @param region Optional region for the room (can be null, defaults to server region)
     * @return The room ID
     */
    public String createRoom(String roomName, String description, String templateId, String region) {
        try {
            logger.info("Creating 100ms room: {}", roomName);
            
            // Create a restTemplate
            RestTemplate restTemplate = new RestTemplate();
            
            // Set up the headers with the management token
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(managementToken);
            
            // Prepare the request body
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("name", roomName);
            requestBody.put("description", description);
            
            // Add template_id if provided
            if (templateId != null && !templateId.isEmpty()) {
                requestBody.put("template_id", templateId);
            }
            
            // Add region if provided
            if (region != null && !region.isEmpty()) {
                requestBody.put("region", region);
            }
            
            // Create the HTTP entity
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            // Make the API call to create the room
            ResponseEntity<Map> response = restTemplate.postForEntity(
                    "https://api.100ms.live/v2/rooms",
                    requestEntity,
                    Map.class
            );
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                String roomId = (String) response.getBody().get("id");
                logger.info("Created 100ms room with ID: {}", roomId);
                
                // Log additional information available in the response
                boolean enabled = (boolean) response.getBody().getOrDefault("enabled", true);
                String templateName = (String) response.getBody().get("template");
                String region_assigned = (String) response.getBody().get("region");
                
                logger.debug("Room details - Enabled: {}, Template: {}, Region: {}", 
                    enabled, templateName, region_assigned);
                    
                return roomId;
            } else {
                logger.error("Failed to create 100ms room. Response: {}", response);
                throw new RuntimeException("Failed to create 100ms room");
            }
        } catch (Exception e) {
            logger.error("Error creating 100ms room", e);
            throw new RuntimeException("Failed to create 100ms room", e);
        }
    }
    
    /**
     * Create a room in 100ms with default parameters
     * 
     * @param roomName Name of the room
     * @param description Description of the room
     * @return The room ID
     */
    public String createRoom(String roomName, String description) {
        return createRoom(roomName, description, null, null);
    }
    
    /**
     * Ensure a room exists before joining - either get an existing room or create a new one
     * @param roomName Name or identifier for the room
     * @param description Description of the room
     * @return A valid room ID from the 100ms API
     */
    public String ensureRoomExists(String roomName, String description) {
        try {
            logger.info("Checking if room exists: {}", roomName);
            
            // Create a restTemplate
            RestTemplate restTemplate = new RestTemplate();
            
            // Set up the headers with the management token
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(managementToken);
            
            // Normalize room name to prevent issues
            String normalizedRoomName = roomName.trim().replaceAll("\\s+", "-").toLowerCase();
            
            // Try to get room by name first
            ResponseEntity<Map> response = restTemplate.exchange(
                    "https://api.100ms.live/v2/rooms?name=" + normalizedRoomName,
                    org.springframework.http.HttpMethod.GET,
                    new HttpEntity<>(headers),
                    Map.class
            );
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Object[] rooms = (Object[]) response.getBody().get("data");
                if (rooms != null && rooms.length > 0) {
                    // Room exists, return its ID
                    String roomId = (String) ((Map)rooms[0]).get("id");
                    
                    // Validate that this is an actual room ID
                    if (roomId != null && !roomId.trim().isEmpty()) {
                        logger.info("Found existing room with ID: {}", roomId);
                        
                        // Check if the room is active
                        ResponseEntity<Map> roomResponse = restTemplate.exchange(
                                "https://api.100ms.live/v2/rooms/" + roomId,
                                org.springframework.http.HttpMethod.GET,
                                new HttpEntity<>(headers),
                                Map.class
                        );
                        
                        if (roomResponse.getStatusCode().is2xxSuccessful() && 
                            roomResponse.getBody() != null && 
                            !"disabled".equals(roomResponse.getBody().get("status"))) {
                            return roomId;
                        } else {
                            logger.warn("Found room exists but is not active, creating new room");
                        }
                    }
                }
            }
            
            // If we didn't find an active room, create a new one
            return createRoom(normalizedRoomName, description);
        } catch (Exception e) {
            logger.error("Error ensuring room exists: {}", e.getMessage(), e);
            // If checking fails, try to create it anyway
            String normalizedRoomName = roomName.trim().replaceAll("\\s+", "-").toLowerCase();
            return createRoom(normalizedRoomName, description);
        }
    }
    
    /**
     * Get session join information
     * @param roomId The room ID
     * @param userId The user ID
     * @param username The username
     * @param isTeacher Whether the user is a teacher
     * @return Map containing session join information
     */
    public Map<String, Object> getSessionJoinInfo(String roomId, String userId, String username, boolean isTeacher) {
        String authToken = generateAuthToken(roomId, userId, username, isTeacher);
        
        Map<String, Object> joinInfo = new HashMap<>();
        joinInfo.put("roomId", roomId);
        joinInfo.put("token", authToken);
        joinInfo.put("username", username);
        joinInfo.put("role", isTeacher ? "host" : "guest");
        joinInfo.put("appId", appAccessKey);
        
        return joinInfo;
    }
    
    /**
     * Get session join information for an existing room with known ID
     * @param knownRoomId The existing room ID
     * @param userId The user ID
     * @param username The username
     * @param isTeacher Whether the user is a teacher
     * @return Map containing session join information
     */
    public Map<String, Object> getSessionInfoForExistingRoom(String knownRoomId, String userId, String username, boolean isTeacher) {
        try {
            logger.info("Generating join information for existing room ID: {}", knownRoomId);
            
            // Validate that this is an actual room ID by checking with 100ms API
            RestTemplate restTemplate = new RestTemplate();
            
            // Set up the headers with the management token
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(managementToken);
            
            // Check if the room exists
            ResponseEntity<Map> roomResponse = restTemplate.exchange(
                    "https://api.100ms.live/v2/rooms/" + knownRoomId,
                    org.springframework.http.HttpMethod.GET,
                    new HttpEntity<>(headers),
                    Map.class
            );
            
            if (roomResponse.getStatusCode().is2xxSuccessful() && roomResponse.getBody() != null) {
                logger.info("Confirmed room exists: {}", knownRoomId);
                // Room exists, generate token and return join info
                return getSessionJoinInfo(knownRoomId, userId, username, isTeacher);
            } else {
                logger.error("Room with ID {} not found or not accessible", knownRoomId);
                throw new RuntimeException("Room not found or not accessible");
            }
        } catch (Exception e) {
            logger.error("Error getting session info for existing room: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to get session information for existing room", e);
        }
    }
    
    // Getters for configuration values
    public String getAppAccessKey() {
        return appAccessKey;
    }
    
    public int getTokenExpirationSeconds() {
        return tokenExpirationSeconds;
    }
}
