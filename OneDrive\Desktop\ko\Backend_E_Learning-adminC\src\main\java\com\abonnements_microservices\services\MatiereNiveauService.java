package com.abonnements_microservices.services;


import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.model.MatiereNiveau;
import com.abonnements_microservices.model.MatiereNiveauId;
import com.abonnements_microservices.model.Niveau;
import com.abonnements_microservices.repo.MatiereNiveauRepository;
import com.abonnements_microservices.repo.MatiereRepository;
import com.abonnements_microservices.repo.NiveauRepository;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class MatiereNiveauService {

    private final MatiereNiveauRepository matiereNiveauRepository;
    private final MatiereRepository matiereRepository;
    private final NiveauRepository niveauRepository;

    public MatiereNiveau create(Long matiereId, Long niveauId) {
        Matiere matiere = matiereRepository.findById(matiereId)
                .orElseThrow(() -> new EntityNotFoundException("Matière non trouvée"));
        Niveau niveau = niveauRepository.findById(niveauId)
                .orElseThrow(() -> new EntityNotFoundException("Niveau non trouvé"));

        MatiereNiveau mn = MatiereNiveau.builder()
                .matiere(matiere)
                .niveau(niveau)
                .build();

        return matiereNiveauRepository.save(mn);
    }

    public List<MatiereNiveau> getAll() {
        return matiereNiveauRepository.findAll();
    }

    public MatiereNiveau getById(MatiereNiveauId id) {
        return matiereNiveauRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("MatiereNiveau non trouvé"));
    }
}
