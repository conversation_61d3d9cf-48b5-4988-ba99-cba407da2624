package com.abonnements_microservices.services;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import sendinblue.ApiClient;
import sendinblue.ApiException;
import sendinblue.Configuration;
import sendinblue.auth.ApiKeyAuth;
import sibModel.CreateSmtpEmail;
import sibModel.SendSmtpEmail;
import sibModel.SendSmtpEmailSender;
import sibModel.SendSmtpEmailTo;
import sibApi.TransactionalEmailsApi;


import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class EmailService {

    @Value("${brevo.api.key}")
    private String apiKey;
    
    @Value("${brevo.sender.email:<EMAIL>}")
    private String senderEmail;
    
    @Value("${brevo.sender.name:E-Learning Platform}")
    private String senderName;
    
    private TransactionalEmailsApi apiInstance;
    
    @PostConstruct
    public void init() {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        ApiKeyAuth apiKeyAuth = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
        apiKeyAuth.setApiKey(apiKey);
        apiInstance = new TransactionalEmailsApi();
        
        System.out.println("Email service initialized with API key: " + apiKey.substring(0, 5) + "...");
    }    public void sendCredentialsEmail(String toEmail, String fullName, String username, String password) {
        // Debug info
        System.out.println("Preparing email with credentials:");
        System.out.println("To: " + toEmail);
        System.out.println("Name: " + fullName);
        System.out.println("Username value: '" + username + "'");
        System.out.println("Password value: '" + password + "'");
        
        SendSmtpEmail email = new SendSmtpEmail();

        // Create sender
        SendSmtpEmailSender sender = new SendSmtpEmailSender();
        sender.setName(senderName);
        sender.setEmail(senderEmail);
        email.setSender(sender);
        
        // Create recipient list
        SendSmtpEmailTo recipient = new SendSmtpEmailTo();
        recipient.setEmail(toEmail);
        recipient.setName(fullName);
        email.setTo(Arrays.asList(recipient));
        
        // Build HTML content with credentials
        String htmlContent = "<html><body>" +
            "<h1>Bienvenue " + fullName + " !</h1>" +
            "<p>Voici vos identifiants pour vous connecter à la plateforme :</p>" +
            "<p><strong>Nom d'utilisateur :</strong> " + username + "</p>" +
            "<p><strong>Mot de passe :</strong> " + password + "</p>" +
            "<p>Merci de changer votre mot de passe après la première connexion.</p>" +
            "</body></html>";
        
        System.out.println("Email HTML content: " + htmlContent);
            
        // Set email content
        email.setSubject("Bienvenue sur la plateforme E-learning");
        email.setHtmlContent(htmlContent);        try {
            // Check for null/empty credentials before sending
            if (username == null || username.isEmpty()) {
                System.err.println("WARNING: Username is null or empty!");
            }
            if (password == null || password.isEmpty()) {
                System.err.println("WARNING: Password is null or empty!");
            }
            
            CreateSmtpEmail result = apiInstance.sendTransacEmail(email);
            System.out.println("Email envoyé avec succès ! Message ID: " + result.getMessageId());
        } catch (ApiException e) {
            System.err.println("Erreur lors de l'envoi de l'email : " + e.getMessage());
            System.err.println("Code d'état: " + e.getCode());
            System.err.println("Response body: " + e.getResponseBody());
        } catch (Exception e) {
            System.err.println("Erreur lors de l'envoi de l'email : " + e.getMessage());
            e.printStackTrace();
        }
    }    /**
     * Generic method to send an email with subject and body text
     * @param toEmail Recipient email address
     * @param subject Email subject
     * @param bodyText Email body text
     */
    public void sendEmail(String toEmail, String subject, String bodyText) {
        try {
            SendSmtpEmail email = new SendSmtpEmail();
            
            // Set sender information
            SendSmtpEmailSender sender = new SendSmtpEmailSender();
            sender.setEmail(senderEmail);
            sender.setName(senderName);
            email.setSender(sender);
            
            // Set recipient
            List<SendSmtpEmailTo> toList = new ArrayList<>();
            SendSmtpEmailTo to = new SendSmtpEmailTo();
            to.setEmail(toEmail);
            toList.add(to);
            email.setTo(toList);
            
            // Set email content
            email.setSubject(subject);
            email.setHtmlContent("<html><body><p>" + bodyText.replace("\n", "<br>") + "</p></body></html>");
            
            // Send the email
            CreateSmtpEmail response = apiInstance.sendTransacEmail(email);
            System.out.println("Email sent successfully to " + toEmail + ". Message ID: " + response.getMessageId());
        } catch (ApiException e) {
            System.err.println("Error sending email: " + e.getMessage());
            System.err.println("Response body: " + e.getResponseBody());
            e.printStackTrace();
            throw new RuntimeException("Failed to send email: " + e.getMessage());
        }
    }
}
