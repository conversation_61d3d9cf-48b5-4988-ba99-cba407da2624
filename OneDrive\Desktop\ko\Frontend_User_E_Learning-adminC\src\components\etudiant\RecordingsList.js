import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Row, Col, Form, Spinner, Alert, Pagination } from 'react-bootstrap';
import axiosInstance from '../../services/axiosService';
import keycloak from '../../keycloak';
import accessControlService from '../../services/accessControlService';

const RecordingsList = () => {
  const navigate = useNavigate();
  const [recordings, setRecordings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [matieres, setMatieres] = useState([]);
  const [niveaux, setNiveaux] = useState([]);
  const [userId, setUserId] = useState(null);
  const [userNiveauId, setUserNiveauId] = useState(null);
  const [selectedMatiere, setSelectedMatiere] = useState('all');
  const [selectedDateRange, setSelectedDateRange] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const recordingsPerPage = 8;
  const [hasAccess, setHasAccess] = useState(false);

  // Fetch user info and check access permissions
  useEffect(() => {
    const fetchUserInfo = async () => {
      if (keycloak.authenticated) {
        try {
          // Try to get the current user's info
          const response = await axiosInstance.get('/api/etudiants');
          const username = keycloak.tokenParsed?.preferred_username;
          const email = keycloak.tokenParsed?.email;

          const etudiant = response.data.find(
            (e) => e.username === username || e.email === email
          );

          if (etudiant) {
            setUserId(etudiant.id || etudiant.idEtudiant);
            if (etudiant.niveau && etudiant.niveau.id) {
              const niveauId = etudiant.niveau.id;
              setUserNiveauId(niveauId);
              console.log(`Student niveau detected: ${niveauId}. Will filter recordings by this niveau.`);
            }

            // Check if user has access to recordings
            const hasRecordingsAccess = await accessControlService.hasAccessToRecordings();
            setHasAccess(hasRecordingsAccess);

            if (!hasRecordingsAccess) {
              setError("Votre abonnement ne vous donne pas accès aux enregistrements. Veuillez mettre à niveau votre abonnement pour accéder à cette fonctionnalité.");
            }
          }
        } catch (err) {
          console.error('Error fetching user info:', err);
        }
      }
    };

    fetchUserInfo();
  }, []);

  // Fetch matieres
  useEffect(() => {
    const fetchMatieres = async () => {
      if (!userId) return;

      try {
        const response = await axiosInstance.get(`/api/etudiants/${userId}/abonnements-matieres`);
        const matieresData = response.data.content || response.data;
        setMatieres(matieresData);
      } catch (err) {
        console.error('Error fetching matieres:', err);
      }
    };

    fetchMatieres();
  }, [userId]);

  // Fetch niveaux
  useEffect(() => {
    const fetchNiveaux = async () => {
      try {
        const response = await axiosInstance.get('/api/niveaux/all');
        setNiveaux(response.data);
      } catch (err) {
        console.error('Error fetching niveaux:', err);
      }
    };

    fetchNiveaux();
  }, []);

  // Helper function to get date range
  const getDateRange = (range) => {
    const today = new Date();
    let startDate = new Date();

    switch(range) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        startDate.setDate(today.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(today.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(today.getFullYear() - 1);
        break;
      default:
        return null; // No date filtering
    }

    return {
      start: startDate,
      end: today
    };
  };

  // Fetch recordings
  useEffect(() => {
    const fetchRecordings = async () => {
      setLoading(true);

      // Only clear error if it's not an access error
      if (!error || !error.includes("Votre abonnement ne vous donne pas accès")) {
        setError(null);
      }

      // Check if user has access to recordings
      if (!hasAccess) {
        setLoading(false);
        return; // Don't fetch recordings if user doesn't have access
      }

      // Verify access permissions again (in case they changed)
      try {
        const hasRecordingsAccess = await accessControlService.hasAccessToRecordings();
        setHasAccess(hasRecordingsAccess);

        if (!hasRecordingsAccess) {
          setError("Votre abonnement ne vous donne pas accès aux enregistrements. Veuillez mettre à niveau votre abonnement pour accéder à cette fonctionnalité.");
          setLoading(false);
          return;
        }
      } catch (err) {
        console.error('Error checking recordings access:', err);
      }

      try {
        let allRecordings = [];

        // If the student has a niveau, fetch recordings for that niveau
        if (userNiveauId) {
          if (selectedMatiere !== 'all') {
            // Fetch recordings for specific matiere and student's niveau
            const url = `/api/recordings/matiere/${selectedMatiere}/niveau/${userNiveauId}`;
            const response = await axiosInstance.get(url);
            allRecordings = response.data || [];
          } else {
            // Fetch recordings for all student's matieres but only for their niveau
            const matieresPromises = matieres.map(matiere =>
              axiosInstance.get(`/api/recordings/matiere/${matiere.idMatiere}/niveau/${userNiveauId}`)
            );

            try {
              const responses = await Promise.all(matieresPromises);
              allRecordings = responses.flatMap(response => response.data || []);
            } catch (err) {
              console.error('Error fetching recordings for student niveau:', err);

              // Fallback: try to get all recordings for the student's niveau
              try {
                const response = await axiosInstance.get(`/api/recordings/niveau/${userNiveauId}`);
                allRecordings = response.data || [];
              } catch (fallbackErr) {
                console.error('Fallback error:', fallbackErr);
                setError('Erreur lors du chargement des enregistrements. Veuillez réessayer plus tard.');
              }
            }
          }
        } else {
          // If student doesn't have a niveau, fetch based on selected matiere
          if (selectedMatiere !== 'all') {
            const url = `/api/recordings/matiere/${selectedMatiere}`;
            const response = await axiosInstance.get(url);
            allRecordings = response.data || [];
          } else {
            // Fetch all recordings for student's matieres
            const matieresPromises = matieres.map(matiere =>
              axiosInstance.get(`/api/recordings/matiere/${matiere.idMatiere}`)
            );

            try {
              const responses = await Promise.all(matieresPromises);
              allRecordings = responses.flatMap(response => response.data || []);
            } catch (err) {
              console.error('Error fetching all recordings:', err);
              setError('Erreur lors du chargement des enregistrements. Veuillez réessayer plus tard.');
            }
          }
        }

        // Apply date filtering if needed
        if (selectedDateRange !== 'all') {
          const dateRange = getDateRange(selectedDateRange);
          if (dateRange) {
            allRecordings = allRecordings.filter(recording => {
              if (!recording.recordingDate) return false;

              const recordingDate = new Date(recording.recordingDate);
              return recordingDate >= dateRange.start && recordingDate <= dateRange.end;
            });
          }
        }

        setRecordings(allRecordings);
      } catch (err) {
        console.error('Error fetching recordings:', err);
        setError('Erreur lors du chargement des enregistrements. Veuillez réessayer plus tard.');
      } finally {
        setLoading(false);
      }
    };

    if (userId || matieres.length > 0) {
      fetchRecordings();
    }
  }, [userId, selectedMatiere, selectedDateRange, userNiveauId, matieres]);

  // Filter recordings based on search term
  const filteredRecordings = recordings.filter(recording =>
    recording.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (recording.description && recording.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (recording.matiere && recording.matiere.nomMatiere.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (recording.niveau && recording.niveau.nom.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Pagination
  const totalPages = Math.ceil(filteredRecordings.length / recordingsPerPage);
  const currentRecordings = filteredRecordings.slice(
    (currentPage - 1) * recordingsPerPage,
    currentPage * recordingsPerPage
  );

  // Handle video playback
  const handlePlayVideo = (recording) => {
    // Extract video ID from URL (assuming Vimeo URLs)
    const vimeoMatch = recording.videoUrl.match(/(?:vimeo\.com\/|\/videos\/|video\/)?(\d+)/);
    if (vimeoMatch && vimeoMatch[1]) {
      window.open(`https://player.vimeo.com/video/${vimeoMatch[1]}`, '_blank');
    } else {
      window.open(recording.videoUrl, '_blank');
    }
  };

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0 d-flex align-items-center justify-content-between">
        <div className="col-auto">
          <h4 style={{ color: "#37A7DF" }}>Enregistrements des séances live</h4>
        </div>
        <div className="col-md-4">
          <Form.Control
            type="text"
            placeholder="Rechercher un enregistrement..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            disabled={!hasAccess}
          />
        </div>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}

      {!hasAccess ? (
        <Alert variant="warning">
          <h5>Accès limité</h5>
          <p>Votre abonnement actuel ne vous donne pas accès aux enregistrements des séances live.</p>
          <p>Pour accéder à cette fonctionnalité, veuillez mettre à niveau votre abonnement vers un forfait qui inclut l'accès aux enregistrements.</p>
          <button
            className="btn btn-primary mt-2"
            onClick={() => navigate('/Mes-abonnement')}
          >
            Voir mes abonnements
          </button>
        </Alert>
      ) : (
        <>
          <div className="row mb-4">
            <div className="col-md-6">
              <Form.Group>
                <Form.Label>Filtrer par matière</Form.Label>
                <Form.Control
                  as="select"
                  value={selectedMatiere}
                  onChange={(e) => setSelectedMatiere(e.target.value)}
                >
                  <option value="all">Toutes les matières</option>
                  {matieres.map((matiere) => (
                    <option key={matiere.idMatiere} value={matiere.idMatiere}>
                      {matiere.nomMatiere}
                    </option>
                  ))}
                </Form.Control>
              </Form.Group>
            </div>
            <div className="col-md-6">
              <Form.Group>
                <Form.Label>
                  Filtrer par date
                </Form.Label>
                <Form.Control
                  as="select"
                  value={selectedDateRange}
                  onChange={(e) => setSelectedDateRange(e.target.value)}
                >
                  <option value="all">Toutes les dates</option>
                  <option value="today">Aujourd'hui</option>
                  <option value="week">Cette semaine</option>
                  <option value="month">Ce mois-ci</option>
                  <option value="year">Cette année</option>
                </Form.Control>
                <small className="text-muted">
                  Filtrez les enregistrements par date pour trouver les sessions récentes.
                </small>
              </Form.Group>
            </div>
          </div>
        </>
      )}

      {hasAccess && loading ? (
        <div className="text-center py-5">
          <Spinner animation="border" variant="primary" />
          <p className="mt-2">Chargement des enregistrements...</p>
        </div>
      ) : hasAccess && (
        <>
          {currentRecordings.length === 0 ? (
            <Alert variant="info">
              {selectedDateRange !== 'all' ? (
                <>
                  Aucun enregistrement trouvé pour la période sélectionnée.
                  <br />
                  Vous pouvez sélectionner "Toutes les dates" dans le filtre pour voir tous les enregistrements disponibles.
                </>
              ) : (
                <>
                  Aucun enregistrement trouvé avec les critères de recherche actuels.
                  <br />
                  Essayez de modifier vos filtres ou revenez plus tard pour voir de nouveaux enregistrements.
                </>
              )}
            </Alert>
          ) : (
            <Row>
              {currentRecordings.map((recording) => (
                <Col key={recording.id} lg={3} md={4} sm={6} className="mb-4">
                  <Card className="h-100 shadow-sm">
                    <div
                      className="video-thumbnail"
                      style={{
                        height: '180px',
                        background: '#000',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer'
                      }}
                      onClick={() => handlePlayVideo(recording)}
                    >
                      <i
                        className="fa fa-play-circle"
                        style={{
                          fontSize: '3rem',
                          color: '#fff'
                        }}
                      ></i>
                    </div>
                    <Card.Body>
                      <Card.Title>{recording.title}</Card.Title>
                      <Card.Text className="text-muted small">
                        {recording.recordingDate ? new Date(recording.recordingDate).toLocaleDateString() : 'Date non disponible'}
                      </Card.Text>
                      <Card.Text>
                        {recording.description || 'Aucune description disponible'}
                      </Card.Text>
                      <div className="mt-2">
                        <span className="badge bg-primary me-2">
                          {recording.matiere?.nomMatiere || 'Matière non spécifiée'}
                        </span>
                        <span className="badge bg-secondary">
                          {recording.niveau?.nom || 'Niveau non spécifié'}
                        </span>
                      </div>
                    </Card.Body>
                    <Card.Footer className="bg-white border-0">
                      <button
                        className="btn btn-primary w-100"
                        style={{
                          backgroundColor: "#37A7DF",
                          borderColor: "#37A7DF",
                        }}
                        onClick={() => handlePlayVideo(recording)}
                      >
                        Regarder la vidéo
                      </button>
                    </Card.Footer>
                  </Card>
                </Col>
              ))}
            </Row>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="d-flex justify-content-center mt-4">
              <Pagination>
                <Pagination.First onClick={() => setCurrentPage(1)} disabled={currentPage === 1} />
                <Pagination.Prev onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1} />

                {[...Array(totalPages)].map((_, i) => (
                  <Pagination.Item
                    key={i + 1}
                    active={i + 1 === currentPage}
                    onClick={() => setCurrentPage(i + 1)}
                  >
                    {i + 1}
                  </Pagination.Item>
                ))}

                <Pagination.Next onClick={() => setCurrentPage(currentPage + 1)} disabled={currentPage === totalPages} />
                <Pagination.Last onClick={() => setCurrentPage(totalPages)} disabled={currentPage === totalPages} />
              </Pagination>
            </div>
          )}
        </>
      )}
    </div>
  );
};

// Export with access control check
export default RecordingsList;
