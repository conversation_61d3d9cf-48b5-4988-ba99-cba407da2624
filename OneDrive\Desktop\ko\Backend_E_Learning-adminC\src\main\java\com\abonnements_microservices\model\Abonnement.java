package com.abonnements_microservices.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.*;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)

@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Abonnement {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;

    private String nom;
    private String description;
    private double prix;
    private Integer duree; // Durée de l'abonnement en mois

    @ManyToMany(mappedBy = "abonnements", fetch = FetchType.LAZY)  // Notez "abonnements"
    @JsonIgnoreProperties("abonnements")  // Pour éviter une boucle infinie lors de la sérialisation
    private Set<Matiere> matieres = new HashSet<>();

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "type_id")
    @JsonIgnoreProperties("abonnements")
    private AbonnementType type;

    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "image_id", referencedColumnName = "idImage")
    @JsonIgnoreProperties("abonnement")
    private ImageAbonnement imageAbonnement;

    @ManyToMany(mappedBy = "abonnements", fetch = FetchType.LAZY)
    @JsonIgnoreProperties("abonnements")
    private Set<Etudiant> etudiants = new HashSet<>();


    public void setImage(ImageAbonnement imageAbonnement) {
        if (imageAbonnement == null) {
            if (this.imageAbonnement != null) {
                this.imageAbonnement.setAbonnement(null);
            }
        } else {
            imageAbonnement.setAbonnement(this);
        }
        this.imageAbonnement = imageAbonnement;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void addMatiere(Matiere matiere) {
        matieres.add(matiere);
        matiere.addAbonnement(this); // ✅ au lieu de setAbonnements(this)
    }

    public void removeMatiere(Matiere matiere) {
        matieres.remove(matiere);
        matiere.removeAbonnement(this);
    }
    public ImageAbonnement getImageAbonnement() {
        return imageAbonnement;
    }

    public void setImageAbonnement(ImageAbonnement imageAbonnement) {
        this.imageAbonnement = imageAbonnement;
    }

}
