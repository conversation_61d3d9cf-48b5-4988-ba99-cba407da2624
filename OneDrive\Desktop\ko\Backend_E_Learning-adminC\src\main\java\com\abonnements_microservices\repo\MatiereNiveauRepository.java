package com.abonnements_microservices.repo;

import com.abonnements_microservices.model.Matiere;
import com.abonnements_microservices.model.MatiereNiveau;
import com.abonnements_microservices.model.MatiereNiveauId;
import com.abonnements_microservices.model.Niveau;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MatiereNiveauRepository extends JpaRepository<MatiereNiveau, MatiereNiveauId> {

    // Trouver par les entités directement
    Optional<MatiereNiveau> findByMatiereAndNiveau(Matiere matiere, Niveau niveau);

    // Trouver par les IDs (si tu veux éviter de charger les entités d'abord)
    Optional<MatiereNiveau> findById(MatiereNiveauId id);

    // Optionnel : récupérer tous les MatiereNiveau pour un certain niveau ou matière
    List<MatiereNiveau> findByNiveauId(Long niveauId);
    List<MatiereNiveau> findByMatiereId(Long matiereId);

    // Optionnel : charger les chapitres pour une relation spécifique
    Optional<MatiereNiveau> findWithChapitresById(MatiereNiveauId id);
    Optional<MatiereNiveau> findByMatiereIdAndNiveauId(Long matiereId, Long niveauId);

}
